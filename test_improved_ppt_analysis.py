import requests
import json
import os
import tempfile
from pptx import Presentation
from pptx.util import Inches

def print_header(title):
    print("\n" + "="*70)
    print(f"🎓 {title}")
    print("="*70)

def print_success(message):
    print(f"✅ {message}")

def print_error(message):
    print(f"❌ {message}")

def print_info(message):
    print(f"ℹ️ {message}")

def create_test_ppt():
    """Create a comprehensive test PowerPoint file"""
    print_info("创建测试PPT文件...")
    
    # Create a new presentation
    prs = Presentation()
    
    # Slide 1: Title slide
    slide_layout = prs.slide_layouts[0]  # Title slide layout
    slide = prs.slides.add_slide(slide_layout)
    title = slide.shapes.title
    subtitle = slide.placeholders[1]
    
    title.text = "智能教育平台功能介绍"
    subtitle.text = "现代化在线教育解决方案\n主讲人：张老师\n2024年12月"
    
    # Slide 2: Content slide with bullet points
    slide_layout = prs.slide_layouts[1]  # Title and content layout
    slide = prs.slides.add_slide(slide_layout)
    title = slide.shapes.title
    content = slide.placeholders[1]
    
    title.text = "平台核心功能"
    content.text = """课程管理系统
• 创建和管理在线课程
• 支持多种教学资料格式
• 智能内容分析和推荐

班级管理功能
• 学生班级分配管理
• 基于班级的权限控制
• 学习进度跟踪

文件上传系统
• 支持视频、图片、PPT等格式
• 拖拽上传功能
• 实时上传进度显示"""
    
    # Slide 3: Technical features
    slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(slide_layout)
    title = slide.shapes.title
    content = slide.placeholders[1]
    
    title.text = "技术特性"
    content.text = """前端技术
• React 18 现代化框架
• 响应式设计
• 拖拽交互体验

后端技术
• FastAPI 高性能框架
• SQLAlchemy ORM
• JWT 认证系统

AI 集成
• DeepSeek 大语言模型
• 智能内容分析
• 自动摘要生成"""
    
    # Slide 4: Table slide
    slide_layout = prs.slide_layouts[5]  # Blank layout
    slide = prs.slides.add_slide(slide_layout)
    
    # Add title
    title_shape = slide.shapes.add_textbox(Inches(1), Inches(0.5), Inches(8), Inches(1))
    title_frame = title_shape.text_frame
    title_frame.text = "支持的文件格式"
    
    # Add table
    rows, cols = 4, 3
    left = Inches(1)
    top = Inches(2)
    width = Inches(8)
    height = Inches(3)
    
    table = slide.shapes.add_table(rows, cols, left, top, width, height).table
    
    # Set table headers
    table.cell(0, 0).text = "文件类型"
    table.cell(0, 1).text = "支持格式"
    table.cell(0, 2).text = "用途"
    
    # Fill table data
    table.cell(1, 0).text = "视频文件"
    table.cell(1, 1).text = "MP4, AVI, MOV"
    table.cell(1, 2).text = "教学视频、演示"
    
    table.cell(2, 0).text = "文档文件"
    table.cell(2, 1).text = "PDF, DOC, PPT"
    table.cell(2, 2).text = "教材、讲义"
    
    table.cell(3, 0).text = "图片文件"
    table.cell(3, 1).text = "JPG, PNG, GIF"
    table.cell(3, 2).text = "图表、示例"
    
    # Slide 5: Summary slide
    slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(slide_layout)
    title = slide.shapes.title
    content = slide.placeholders[1]
    
    title.text = "总结"
    content.text = """智能教育平台优势
✓ 完整的教学管理功能
✓ 现代化的用户体验
✓ 强大的AI分析能力
✓ 灵活的权限管理
✓ 多格式文件支持

下一步计划
• 移动端应用开发
• 实时互动功能
• 高级数据分析
• 多语言支持"""
    
    # Add notes to slides
    notes_slide = slide.notes_slide
    notes_text_frame = notes_slide.notes_text_frame
    notes_text_frame.text = """演讲者备注：
这是课程总结部分，重点强调平台的核心优势和未来发展方向。
建议用5-8分钟时间详细介绍每个优势点，并回答学生问题。
可以结合实际演示来展示平台功能。"""
    
    # Set presentation properties
    prs.core_properties.title = "智能教育平台功能介绍"
    prs.core_properties.subject = "教育技术"
    prs.core_properties.author = "张老师"
    prs.core_properties.comments = "这是一个关于智能教育平台的详细介绍演示文稿"
    
    # Save the presentation
    temp_dir = tempfile.mkdtemp()
    ppt_path = os.path.join(temp_dir, "智能教育平台介绍.pptx")
    prs.save(ppt_path)
    
    print_success(f"测试PPT文件创建成功: {ppt_path}")
    return ppt_path, temp_dir

def test_ppt_analysis():
    """Test improved PPT analysis functionality"""
    print_header("改进的PPT分析功能测试")
    
    api_url = "http://localhost:8000/api/v1"
    
    # Step 1: Teacher login
    print("\n📋 Step 1: 教师登录")
    try:
        login_response = requests.post(f"{api_url}/auth/token", 
                                     json={"username": "teacher_zhang", "password": "teacher123456"},
                                     timeout=10)
        
        if login_response.status_code == 200:
            teacher_token = login_response.json()["access_token"]
            teacher_headers = {"Authorization": f"Bearer {teacher_token}"}
            print_success("教师登录成功")
        else:
            print_error(f"教师登录失败: {login_response.json()}")
            return False
    except Exception as e:
        print_error(f"教师登录异常: {e}")
        return False
    
    # Step 2: Get courses
    print("\n📋 Step 2: 获取课程列表")
    try:
        courses_response = requests.get(f"{api_url}/courses/", headers=teacher_headers, timeout=10)
        if courses_response.status_code == 200:
            courses = courses_response.json()
            if not courses:
                print_error("没有可用的课程进行测试")
                return False
            test_course = courses[0]
            print_success(f"使用课程: {test_course['title']} (ID: {test_course['id']})")
        else:
            print_error("获取课程失败")
            return False
    except Exception as e:
        print_error(f"获取课程异常: {e}")
        return False
    
    # Step 3: Create test PPT
    print("\n📋 Step 3: 创建测试PPT文件")
    try:
        ppt_path, temp_dir = create_test_ppt()
        file_size = os.path.getsize(ppt_path)
        print_info(f"PPT文件大小: {file_size} bytes")
    except Exception as e:
        print_error(f"创建测试PPT失败: {e}")
        return False
    
    # Step 4: Upload PPT file
    print("\n📋 Step 4: 上传PPT文件")
    try:
        # Get upload credentials
        filename = os.path.basename(ppt_path)
        params = {
            "course_id": test_course['id'],
            "original_filename": filename,
            "file_type": "ppt"
        }
        
        upload_creds_response = requests.post(f"{api_url}/materials/upload-credentials", 
                                            params=params, headers=teacher_headers, timeout=10)
        
        if upload_creds_response.status_code == 200:
            upload_info = upload_creds_response.json()
            upload_url = upload_info['upload_url']
            object_name = upload_info['object_name']
            
            # Upload file
            with open(ppt_path, 'rb') as f:
                file_content = f.read()
            
            upload_response = requests.put(upload_url, 
                                         data=file_content,
                                         headers={"Content-Type": "application/vnd.openxmlformats-officedocument.presentationml.presentation"},
                                         timeout=30)
            
            if upload_response.status_code in [200, 204]:
                # Create material record
                material_record = {
                    "title": "智能教育平台功能介绍PPT",
                    "description": "这是一个详细介绍智能教育平台功能的PowerPoint演示文稿",
                    "file_type": "ppt",
                    "file_path_minio": object_name,
                    "file_size_bytes": len(file_content)
                }
                
                material_response = requests.post(f"{api_url}/materials/{test_course['id']}",
                                                json=material_record, 
                                                headers=teacher_headers, 
                                                timeout=10)
                
                if material_response.status_code == 201:
                    material = material_response.json()
                    material_id = material['id']
                    print_success(f"PPT文件上传成功: {material['title']} (ID: {material_id})")
                else:
                    print_error(f"PPT文件记录创建失败: {material_response.json()}")
                    return False
            else:
                print_error(f"PPT文件上传失败: {upload_response.status_code}")
                return False
        else:
            print_error(f"获取PPT上传凭证失败: {upload_creds_response.json()}")
            return False
            
    except Exception as e:
        print_error(f"上传PPT文件异常: {e}")
        return False
    
    # Step 5: Analyze PPT file
    print("\n📋 Step 5: 分析PPT文件")
    try:
        analysis_response = requests.post(f"{api_url}/materials/{material_id}/analyze",
                                        headers=teacher_headers, timeout=60)
        
        if analysis_response.status_code == 200:
            analysis = analysis_response.json()
            print_success("PPT分析完成")
            
            # Display detailed analysis results
            print_info("📊 分析结果详情:")
            print_info(f"   成功状态: {analysis.get('success', False)}")
            print_info(f"   文件类型: {analysis.get('file_type', 'N/A')}")
            print_info(f"   字数统计: {analysis.get('word_count', 0)} 个词")
            print_info(f"   幻灯片数量: {analysis.get('slide_count', 0)} 张")
            
            print_info("\n📝 内容摘要:")
            print_info(f"   {analysis.get('summary', 'N/A')}")
            
            print_info("\n🎯 关键知识点:")
            for i, point in enumerate(analysis.get('key_points', []), 1):
                print_info(f"   {i}. {point}")
            
            print_info("\n📚 主要主题:")
            for i, topic in enumerate(analysis.get('topics', []), 1):
                print_info(f"   {i}. {topic}")
            
            print_info(f"\n📊 难度级别: {analysis.get('difficulty_level', 'N/A')}")
            
            if analysis.get('content_structure'):
                print_info(f"\n🏗️ 内容结构: {analysis.get('content_structure')}")
            
            print_info("\n💡 教学建议:")
            for i, suggestion in enumerate(analysis.get('teaching_suggestions', []), 1):
                print_info(f"   {i}. {suggestion}")
            
            print_info("\n❓ 建议测试问题:")
            for i, question in enumerate(analysis.get('suggested_questions', []), 1):
                print_info(f"   {i}. {question}")
            
            print_info("\n🎯 学习目标:")
            for i, objective in enumerate(analysis.get('learning_objectives', []), 1):
                print_info(f"   {i}. {objective}")
            
        else:
            print_error(f"PPT分析失败: {analysis_response.json()}")
            return False
    except Exception as e:
        print_error(f"分析PPT文件异常: {e}")
        return False
    
    # Cleanup
    print("\n📋 Step 6: 清理测试文件")
    try:
        import shutil
        shutil.rmtree(temp_dir)
        print_success("临时文件清理完成")
    except Exception as e:
        print_info(f"清理临时文件时出现问题: {e}")
    
    return True

def main():
    print("🎓 改进的PPT分析功能测试")
    print("=" * 80)
    
    print_info("此测试验证改进的PPT文件分析功能:")
    print_info("1. 真正的PPT文本提取")
    print_info("2. 幻灯片内容解析")
    print_info("3. 表格和备注提取")
    print_info("4. 增强的AI分析")
    print_info("5. 丰富的分析结果展示")
    
    # Test PPT analysis functionality
    success = test_ppt_analysis()
    
    # Generate final report
    print_header("PPT分析功能测试结果")
    
    print(f"📊 测试结果:")
    print(f"   🔧 PPT解析功能: {'✅ 完全正常' if success else '❌ 存在问题'}")
    print(f"   🤖 AI分析功能: {'✅ 显著改进' if success else '❌ 需要修复'}")
    
    if success:
        print("\n🎉 PPT分析功能改进成功！")
        print("\n✨ 改进内容:")
        print("   📄 真正的PPT文本提取 - 不再是简单的占位符")
        print("   🎯 幻灯片逐张解析 - 提取每张幻灯片的内容")
        print("   📊 表格内容提取 - 支持PPT中的表格数据")
        print("   📝 演讲者备注提取 - 包含备注内容")
        print("   🏗️ 内容结构分析 - 智能识别文档结构")
        print("   💡 教学建议生成 - 针对PPT的教学建议")
        print("   📈 文件统计信息 - 字数、幻灯片数量等")
        print("   🎨 丰富的前端展示 - 更美观的分析结果界面")
        
        print("\n🎯 使用指南:")
        print("   1. 上传PPT文件到课程中")
        print("   2. 点击'AI分析'按钮")
        print("   3. 查看详细的分析结果")
        print("   4. 利用教学建议优化教学")
        
    else:
        print("\n⚠️ PPT分析功能存在问题，需要进一步检查")
    
    print("\n" + "=" * 80)
    print("🎯 PPT分析功能测试完成！")
    print("=" * 80)

if __name__ == "__main__":
    main()
