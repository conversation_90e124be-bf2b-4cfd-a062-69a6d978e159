#!/usr/bin/env python3
"""
Database migration script to add CourseClass table and update relationships.
This script creates the course_classes table for managing course-class associations.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from app.config import settings
from app.models import Base
from app.database import engine

def create_course_class_table():
    """Create the course_classes table"""
    print("🔧 Creating course_classes table...")
    
    # Create the table using SQLAlchemy
    try:
        Base.metadata.create_all(bind=engine, checkfirst=True)
        print("✅ CourseClass table created successfully!")
        return True
    except Exception as e:
        print(f"❌ Error creating CourseClass table: {e}")
        return False

def verify_table_creation():
    """Verify that the course_classes table was created"""
    print("🔍 Verifying table creation...")
    
    try:
        with engine.connect() as conn:
            # Check if the table exists
            result = conn.execute(text("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='course_classes';
            """))
            
            table_exists = result.fetchone() is not None
            
            if table_exists:
                print("✅ course_classes table exists!")
                
                # Check table structure
                result = conn.execute(text("PRAGMA table_info(course_classes);"))
                columns = result.fetchall()
                
                print("📋 Table structure:")
                for column in columns:
                    print(f"   - {column[1]} ({column[2]})")
                
                return True
            else:
                print("❌ course_classes table not found!")
                return False
                
    except Exception as e:
        print(f"❌ Error verifying table: {e}")
        return False

def create_sample_data():
    """Create some sample course-class associations"""
    print("📊 Creating sample course-class associations...")
    
    try:
        from app.database import SessionLocal
        from app.models import Course, Class, CourseClass
        
        db = SessionLocal()
        
        # Get existing courses and classes
        courses = db.query(Course).limit(3).all()
        classes = db.query(Class).limit(3).all()
        
        if not courses or not classes:
            print("ℹ️ No existing courses or classes found. Skipping sample data creation.")
            db.close()
            return True
        
        # Create associations
        associations_created = 0
        for i, course in enumerate(courses):
            if i < len(classes):
                class_item = classes[i]
                
                # Check if association already exists
                existing = db.query(CourseClass).filter(
                    CourseClass.course_id == course.id,
                    CourseClass.class_id == class_item.id
                ).first()
                
                if not existing:
                    association = CourseClass(
                        course_id=course.id,
                        class_id=class_item.id,
                        is_active=True
                    )
                    db.add(association)
                    associations_created += 1
                    print(f"   ✅ Associated course '{course.title}' with class '{class_item.name}'")
        
        db.commit()
        db.close()
        
        print(f"✅ Created {associations_created} sample course-class associations!")
        return True
        
    except Exception as e:
        print(f"❌ Error creating sample data: {e}")
        return False

def main():
    """Main migration function"""
    print("🎓 Course-Class Association Migration")
    print("=" * 50)
    
    print("ℹ️ This migration will:")
    print("   1. Create the course_classes table")
    print("   2. Verify table creation")
    print("   3. Create sample associations")
    
    # Step 1: Create table
    if not create_course_class_table():
        print("❌ Migration failed at table creation step!")
        return False
    
    # Step 2: Verify table
    if not verify_table_creation():
        print("❌ Migration failed at verification step!")
        return False
    
    # Step 3: Create sample data
    if not create_sample_data():
        print("⚠️ Migration completed but sample data creation failed!")
        return True  # Still consider migration successful
    
    print("\n🎉 Migration completed successfully!")
    print("\n📋 Next steps:")
    print("   1. Restart the backend server")
    print("   2. Test the course-class association features")
    print("   3. Verify that students can access courses through class assignments")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
