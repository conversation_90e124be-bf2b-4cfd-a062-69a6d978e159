from fastapi import APIRouter, Request
from fastapi.responses import HTMLResponse
import logging
import json
from datetime import datetime
from typing import List, Dict
import threading

# In-memory storage for API calls (for debugging purposes)
api_calls_log = []
log_lock = threading.Lock()

# Custom log handler to capture API calls
class APICallHandler(logging.Handler):
    def emit(self, record):
        if hasattr(record, 'getMessage'):
            message = record.getMessage()
            
            # Only capture API request/response logs
            if any(keyword in message for keyword in ['API REQUEST', 'API RESPONSE', 'API ERROR']):
                with log_lock:
                    # Keep only last 100 calls to prevent memory issues
                    if len(api_calls_log) >= 100:
                        api_calls_log.pop(0)
                    
                    api_calls_log.append({
                        'timestamp': datetime.now().isoformat(),
                        'level': record.levelname,
                        'message': message,
                        'logger': record.name
                    })

# Add the handler to the root logger
api_handler = APICallHandler()
logging.getLogger().addHandler(api_handler)

router = APIRouter(prefix="/debug", tags=["Debug"])

@router.get("/api-calls", response_class=HTMLResponse)
async def debug_panel():
    """Debug panel showing recent API calls"""
    
    with log_lock:
        recent_calls = list(reversed(api_calls_log))  # Show newest first
    
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>API Debug Panel - Education Platform</title>
        <style>
            body {{ 
                font-family: 'Consolas', 'Monaco', monospace; 
                margin: 0; 
                padding: 20px; 
                background: #1a1a1a; 
                color: #e0e0e0;
            }}
            .header {{
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px;
                border-radius: 10px;
                margin-bottom: 20px;
                text-align: center;
            }}
            .stats {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                margin-bottom: 20px;
            }}
            .stat-card {{
                background: #2d2d2d;
                padding: 15px;
                border-radius: 8px;
                border-left: 4px solid #667eea;
                text-align: center;
            }}
            .stat-number {{
                font-size: 24px;
                font-weight: bold;
                color: #667eea;
            }}
            .controls {{
                margin-bottom: 20px;
                text-align: center;
            }}
            .btn {{
                background: #667eea;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                margin: 0 5px;
                font-size: 14px;
            }}
            .btn:hover {{
                background: #5a6fd8;
            }}
            .log-container {{
                background: #2d2d2d;
                border-radius: 8px;
                padding: 20px;
                max-height: 600px;
                overflow-y: auto;
                border: 1px solid #444;
            }}
            .log-entry {{
                margin-bottom: 15px;
                padding: 10px;
                border-radius: 5px;
                border-left: 4px solid #666;
                background: #333;
            }}
            .log-entry.request {{
                border-left-color: #4CAF50;
                background: #1a2e1a;
            }}
            .log-entry.response {{
                border-left-color: #2196F3;
                background: #1a1e2e;
            }}
            .log-entry.error {{
                border-left-color: #f44336;
                background: #2e1a1a;
            }}
            .timestamp {{
                color: #888;
                font-size: 12px;
                margin-bottom: 5px;
            }}
            .message {{
                font-size: 14px;
                line-height: 1.4;
                white-space: pre-wrap;
            }}
            .level {{
                display: inline-block;
                padding: 2px 8px;
                border-radius: 3px;
                font-size: 11px;
                font-weight: bold;
                margin-right: 10px;
            }}
            .level.INFO {{ background: #4CAF50; }}
            .level.ERROR {{ background: #f44336; }}
            .level.WARNING {{ background: #ff9800; }}
            .filter-buttons {{
                margin-bottom: 15px;
            }}
            .filter-btn {{
                background: #444;
                color: white;
                border: none;
                padding: 5px 15px;
                border-radius: 3px;
                cursor: pointer;
                margin: 0 5px;
                font-size: 12px;
            }}
            .filter-btn.active {{
                background: #667eea;
            }}
            .no-logs {{
                text-align: center;
                color: #888;
                font-style: italic;
                padding: 40px;
            }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🔍 API Debug Panel</h1>
            <p>Real-time monitoring of Education Platform API calls</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="total-calls">{len(recent_calls)}</div>
                <div>Total API Calls</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="success-calls">{len([c for c in recent_calls if 'API RESPONSE' in c['message'] and '✅' in c['message']])}</div>
                <div>Successful Calls</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="error-calls">{len([c for c in recent_calls if 'API ERROR' in c['message'] or '❌' in c['message']])}</div>
                <div>Failed Calls</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="avg-time">-</div>
                <div>Avg Response Time</div>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="refreshLogs()">🔄 Refresh</button>
            <button class="btn" onclick="clearLogs()">🗑️ Clear Logs</button>
            <button class="btn" onclick="exportLogs()">📥 Export</button>
        </div>
        
        <div class="filter-buttons">
            <button class="filter-btn active" onclick="filterLogs('all')">All</button>
            <button class="filter-btn" onclick="filterLogs('request')">Requests</button>
            <button class="filter-btn" onclick="filterLogs('response')">Responses</button>
            <button class="filter-btn" onclick="filterLogs('error')">Errors</button>
        </div>
        
        <div class="log-container" id="log-container">
            {generate_log_entries(recent_calls)}
        </div>
        
        <script>
            let currentFilter = 'all';
            
            function refreshLogs() {{
                window.location.reload();
            }}
            
            function clearLogs() {{
                if (confirm('Are you sure you want to clear all logs?')) {{
                    fetch('/api/v1/debug/clear-logs', {{method: 'POST'}})
                        .then(() => refreshLogs());
                }}
            }}
            
            function exportLogs() {{
                const logs = {json.dumps(recent_calls)};
                const blob = new Blob([JSON.stringify(logs, null, 2)], {{type: 'application/json'}});
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'api-logs-' + new Date().toISOString().slice(0,19) + '.json';
                a.click();
                URL.revokeObjectURL(url);
            }}
            
            function filterLogs(type) {{
                currentFilter = type;
                
                // Update button states
                document.querySelectorAll('.filter-btn').forEach(btn => {{
                    btn.classList.remove('active');
                }});
                event.target.classList.add('active');
                
                // Filter log entries
                document.querySelectorAll('.log-entry').forEach(entry => {{
                    const show = type === 'all' || entry.classList.contains(type);
                    entry.style.display = show ? 'block' : 'none';
                }});
            }}
            
            // Auto-refresh every 5 seconds
            setInterval(refreshLogs, 5000);
        </script>
    </body>
    </html>
    """
    
    return html_content

def generate_log_entries(calls: List[Dict]) -> str:
    """Generate HTML for log entries"""
    if not calls:
        return '<div class="no-logs">No API calls recorded yet. Make some API requests to see them here.</div>'
    
    html = ""
    for call in calls:
        message = call['message']
        timestamp = call['timestamp']
        level = call['level']
        
        # Determine entry type and class
        if 'API REQUEST' in message:
            entry_class = "request"
        elif 'API RESPONSE' in message:
            entry_class = "response"
        elif 'API ERROR' in message:
            entry_class = "error"
        else:
            entry_class = ""
        
        html += f'''
        <div class="log-entry {entry_class}">
            <div class="timestamp">{timestamp}</div>
            <span class="level {level}">{level}</span>
            <div class="message">{message}</div>
        </div>
        '''
    
    return html

@router.post("/clear-logs")
async def clear_logs():
    """Clear all API call logs"""
    with log_lock:
        api_calls_log.clear()
    return {"message": "Logs cleared successfully"}

@router.get("/api-calls/json")
async def get_api_calls_json():
    """Get API calls as JSON"""
    with log_lock:
        return {"calls": list(reversed(api_calls_log))}
