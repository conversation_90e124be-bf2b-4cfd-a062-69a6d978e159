from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, and_, or_, cast, Date, extract
from app.models import (
    User, UserRole, Course, Material, MaterialType, Quiz, QuizType,
    QuizQuestion, QuestionType, StudentQuizAttempt, Class, ClassStudent,
    StudentMaterialProgress, Notification  # Removed LiveSession
)
from app.schemas import (
    UserCreate, UserUpdate, MaterialCreate, MaterialUpdate, CourseCreate, CourseUpdate,
    ClassCreate, ClassUpdate, QuizCreate, QuizUpdate, QuestionCreate, QuestionUpdate,
    StudentAttemptCreate, GradeShortAnswerRequest, NotificationCreate,
    StudentMaterialProgressUpdate, UserAdminUpdate  # Removed LiveSessionCreate, LiveSessionUpdate
)
from app.auth.security import get_password_hash
from typing import Optional, List, Tuple, Dict, Any
from datetime import datetime, timedelta
import json
import logging

logger = logging.getLogger(__name__)


# --- User CRUD ---
def create_user(db: Session, user: UserCreate) -> User:
    hashed_password = get_password_hash(user.password)
    db_user = User(
        username=user.username,
        email=user.email,
        password_hash=hashed_password,
        full_name=user.full_name,
        role=user.role
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user


def get_user_by_username(db: Session, username: str) -> Optional[User]:
    return db.query(User).filter(User.username == username).first()


def get_user_by_email(db: Session, email: str) -> Optional[User]:
    return db.query(User).filter(User.email == email).first()


def get_user_by_id(db: Session, user_id: int) -> Optional[User]:
    return db.query(User).filter(User.id == user_id).first()


def update_user(db: Session, db_user: User, user_update: UserUpdate) -> User:
    for field, value in user_update.dict(exclude_unset=True).items():
        setattr(db_user, field, value)
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user


def update_user_password(db: Session, db_user: User, new_hashed_password: str) -> User:
    db_user.password_hash = new_hashed_password
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user


# Admin specific User CRUD
def get_users(db: Session, skip: int = 0, limit: int = 100, search_query: Optional[str] = None,
              role: Optional[UserRole] = None) -> List[User]:
    query = db.query(User)
    if search_query:
        query = query.filter(
            or_(
                User.username.ilike(f"%{search_query}%"),
                User.email.ilike(f"%{search_query}%"),
                User.full_name.ilike(f"%{search_query}%")
            )
        )
    if role:
        query = query.filter(User.role == role)
    return query.offset(skip).limit(limit).all()


def admin_update_user(db: Session, db_user: User, user_admin_update: UserAdminUpdate) -> User:
    update_data = user_admin_update.dict(exclude_unset=True)

    if "password" in update_data and update_data["password"] is not None:
        db_user.password_hash = get_password_hash(update_data["password"])
        del update_data["password"]

    for field, value in update_data.items():
        if field == "role" and value is not None:
            setattr(db_user, field, UserRole(value))
        else:
            setattr(db_user, field, value)

    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user


def admin_delete_user(db: Session, user_id: int) -> Optional[User]:
    db_user = db.query(User).filter(User.id == user_id).first()
    if db_user:
        # TODO: Implement proper cascade delete or archiving for associated data
        db.delete(db_user)
        db.commit()
    return db_user


# --- Course CRUD ---
def create_course(db: Session, course: CourseCreate, teacher_id: int) -> Course:
    db_course = Course(
        title=course.title,
        description=course.description,
        cover_image_url=course.cover_image_url,
        teacher_id=teacher_id,
        status="draft"
    )
    db.add(db_course)
    db.commit()
    db.refresh(db_course)
    return db_course


def get_course_by_id(db: Session, course_id: int) -> Optional[Course]:
    return db.query(Course).filter(Course.id == course_id).first()


def get_course_with_relations_by_id(db: Session, course_id: int) -> Optional[Course]:
    return db.query(Course).options(
        joinedload(Course.materials),
        joinedload(Course.quizzes)
    ).filter(Course.id == course_id).first()


def get_courses(
        db: Session,
        skip: int = 0,
        limit: int = 100,
        teacher_id: Optional[int] = None,
        search_query: Optional[str] = None
) -> List[Course]:
    query = db.query(Course)
    if teacher_id is not None:
        query = query.filter(Course.teacher_id == teacher_id)
    if search_query:
        query = query.filter(
            (Course.title.ilike(f"%{search_query}%")) | (Course.description.ilike(f"%{search_query}%"))
        )
    return query.offset(skip).limit(limit).all()


def update_course(db: Session, db_course: Course, course_update: CourseUpdate) -> Course:
    update_data = course_update.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_course, key, value)
    db.add(db_course)
    db.commit()
    db.refresh(db_course)
    return db_course


def delete_course(db: Session, course_id: int) -> Optional[Course]:
    db_course = db.query(Course).filter(Course.id == course_id).first()
    if db_course:
        # TODO: Implement proper cascade delete or archiving for associated data
        db.delete(db_course)
        db.commit()
    return db_course


# --- Class CRUD ---
def create_class(db: Session, class_data: ClassCreate, teacher_id: int) -> Class:
    db_class = Class(name=class_data.name, teacher_id=teacher_id)
    db.add(db_class)
    db.commit()
    db.refresh(db_class)
    return db_class


def get_class_by_id(db: Session, class_id: int) -> Optional[Class]:
    return db.query(Class).filter(Class.id == class_id).first()


def get_class_with_student_count(db: Session, class_id: int) -> Optional[Tuple[Class, int]]:
    result = db.query(Class, func.count(ClassStudent.student_id)).outerjoin(ClassStudent).filter(
        Class.id == class_id).group_by(Class.id).first()
    if result:
        return result
    return None


def get_classes(
        db: Session,
        skip: int = 0,
        limit: int = 100,
        teacher_id: Optional[int] = None,
        search_query: Optional[str] = None
) -> List[Tuple[Class, int]]:
    query = db.query(Class, func.count(ClassStudent.student_id)).outerjoin(ClassStudent).group_by(Class.id)

    if teacher_id is not None:
        query = query.filter(Class.teacher_id == teacher_id)
    if search_query:
        query = query.filter(Class.name.ilike(f"%{search_query}%"))

    return query.offset(skip).limit(limit).all()


def update_class(db: Session, db_class: Class, class_update: ClassUpdate) -> Class:
    update_data = class_update.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_class, key, value)
    db.add(db_class)
    db.commit()
    db.refresh(db_class)
    return db_class


def delete_class(db: Session, class_id: int) -> Optional[Class]:
    db_class = db.query(Class).filter(Class.id == class_id).first()
    if db_class:
        db.query(ClassStudent).filter(ClassStudent.class_id == class_id).delete(synchronize_session=False)
        db.delete(db_class)
        db.commit()
    return db_class


# --- ClassStudent CRUD ---
def add_students_to_class(db: Session, class_id: int, student_ids: List[int]) -> List[ClassStudent]:
    added_students = []
    existing_students = db.query(ClassStudent.student_id).filter(
        ClassStudent.class_id == class_id,
        ClassStudent.student_id.in_(student_ids)
    ).all()
    existing_student_ids = {s[0] for s in existing_students}

    for student_id in student_ids:
        if student_id not in existing_student_ids:
            student_user = db.query(User).filter(User.id == student_id, User.role == UserRole.student).first()
            if student_user:
                db_class_student = ClassStudent(class_id=class_id, student_id=student_id)
                db.add(db_class_student)
                added_students.append(db_class_student)
            else:
                logger.warning(
                    f"Attempted to add non-existent or non-student user ID {student_id} to class {class_id}.")

    if added_students:
        db.commit()
        for s in added_students:
            db.refresh(s)
    return added_students


def remove_student_from_class(db: Session, class_id: int, student_id: int) -> bool:
    result = db.query(ClassStudent).filter(
        ClassStudent.class_id == class_id,
        ClassStudent.student_id == student_id
    ).delete(synchronize_session=False)
    db.commit()
    return result > 0


def get_students_in_class(db: Session, class_id: int, skip: int = 0, limit: int = 100) -> List[User]:
    return db.query(User).join(ClassStudent).filter(
        ClassStudent.class_id == class_id,
        User.id == ClassStudent.student_id,
        User.role == UserRole.student
    ).offset(skip).limit(limit).all()


# --- Material CRUD ---
def create_material(db: Session, material_data: MaterialCreate, course_id: int, created_by_user_id: int) -> Material:
    db_material = Material(
        course_id=course_id,
        title=material_data.title,
        description=material_data.description,
        file_path_minio=material_data.file_path_minio,
        file_type=material_data.file_type,
        file_size_bytes=material_data.file_size_bytes,
        duration_seconds=material_data.duration_seconds,
        preview_url=material_data.preview_url,
        created_by_user_id=created_by_user_id
    )
    db.add(db_material)
    db.commit()
    db.refresh(db_material)
    return db_material


def get_material_by_id(db: Session, material_id: int) -> Optional[Material]:
    return db.query(Material).filter(Material.id == material_id).first()


def get_materials_by_course_id(db: Session, course_id: int, skip: int = 0, limit: int = 100) -> List[Material]:
    return db.query(Material).filter(Material.course_id == course_id).offset(skip).limit(limit).all()


def update_material(db: Session, db_material: Material, material_update: MaterialUpdate) -> Material:
    # Get update data and exclude protected fields
    update_data = material_update.dict(exclude_unset=True)

    # Remove protected fields that should not be updated
    protected_fields = {'course_id', 'created_by_user_id', 'created_at', 'id'}
    for field in protected_fields:
        update_data.pop(field, None)

    # Apply updates
    for field, value in update_data.items():
        setattr(db_material, field, value)

    db.add(db_material)
    db.commit()
    db.refresh(db_material)
    return db_material


def delete_material(db: Session, material_id: int) -> Optional[Material]:
    db_material = db.query(Material).filter(Material.id == material_id).first()
    if db_material:
        db.delete(db_material)
        db.commit()
    return db_material


def update_material_milvus_vector_id(db: Session, material_id: int, milvus_vector_id: str) -> Optional[Material]:
    db_material = db.query(Material).filter(Material.id == material_id).first()
    if db_material:
        db_material.milvus_vector_id = milvus_vector_id
        db.add(db_material)
        db.commit()
        db.refresh(db_material)
    return db_material


def update_material_extracted_text_content(db: Session, material_id: int, extracted_text: str) -> Optional[Material]:
    db_material = db.query(Material).filter(Material.id == material_id).first()
    if db_material:
        db_material.extracted_text_content = extracted_text
        db.add(db_material)
        db.commit()
        db.refresh(db_material)
    return db_material


# --- Quiz CRUD ---
def create_quiz(db: Session, quiz_data: QuizCreate, created_by_user_id: int) -> Quiz:
    db_quiz = Quiz(
        course_id=quiz_data.course_id,
        title=quiz_data.title,
        description=quiz_data.description,
        quiz_type=quiz_data.quiz_type,
        due_date=quiz_data.due_date,
        is_published=quiz_data.is_published,
        created_by_user_id=created_by_user_id
    )
    db.add(db_quiz)
    db.commit()
    db.refresh(db_quiz)
    return db_quiz


def get_quiz_by_id(db: Session, quiz_id: int) -> Optional[Quiz]:
    return db.query(Quiz).filter(Quiz.id == quiz_id).first()


def get_quiz_with_questions(db: Session, quiz_id: int) -> Optional[Quiz]:
    return db.query(Quiz).options(joinedload(Quiz.questions)).filter(Quiz.id == quiz_id).first()


def get_quiz_for_student_with_questions(db: Session, quiz_id: int) -> Optional[Quiz]:
    return db.query(Quiz).options(
        joinedload(Quiz.questions)
    ).filter(Quiz.id == quiz_id, Quiz.is_published == True).first()


def get_quizzes_by_course_id(db: Session, course_id: int, skip: int = 0, limit: int = 100) -> List[Quiz]:
    return db.query(Quiz).filter(Quiz.course_id == course_id).offset(skip).limit(limit).all()


def update_quiz(db: Session, db_quiz: Quiz, quiz_update: QuizUpdate) -> Quiz:
    update_data = quiz_update.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_quiz, key, value)
    db.add(db_quiz)
    db.commit()
    db.refresh(db_quiz)
    return db_quiz


def delete_quiz(db: Session, quiz_id: int) -> Optional[Quiz]:
    db_quiz = db.query(Quiz).filter(Quiz.id == quiz_id).first()
    if db_quiz:
        db.query(QuizQuestion).filter(QuizQuestion.quiz_id == quiz_id).delete(synchronize_session=False)
        db.query(StudentQuizAttempt).filter(StudentQuizAttempt.quiz_id == quiz_id).delete(synchronize_session=False)
        db.delete(db_quiz)
        db.commit()
    return db_quiz


# --- QuizQuestion CRUD ---
def create_question(db: Session, question_data: QuestionCreate, quiz_id: int) -> QuizQuestion:
    db_question = QuizQuestion(
        quiz_id=quiz_id,
        question_text=question_data.question_text,
        question_type=question_data.question_type,
        options=json.dumps(question_data.options) if question_data.options is not None else None,
        correct_answer=json.dumps(question_data.correct_answer) if question_data.correct_answer is not None else None,
        score=question_data.score,
        question_order=question_data.question_order
    )
    db.add(db_question)
    db.commit()
    db.refresh(db_question)
    return db_question


def get_question_by_id(db: Session, question_id: int) -> Optional[QuizQuestion]:
    return db.query(QuizQuestion).filter(QuizQuestion.id == question_id).first()


def update_question(db: Session, db_question: QuizQuestion, question_update: QuestionUpdate) -> QuizQuestion:
    update_data = question_update.dict(exclude_unset=True)
    for key, value in update_data.items():
        if key in ["options", "correct_answer"]:
            setattr(db_question, key, json.dumps(value) if value is not None else None)
        else:
            setattr(db_question, key, value)
    db.add(db_question)
    db.commit()
    db.refresh(db_question)
    return db_question


def delete_question(db: Session, question_id: int) -> Optional[QuizQuestion]:
    db_question = db.query(QuizQuestion).filter(QuizQuestion.id == question_id).first()
    if db_question:
        db.delete(db_question)
        db.commit()
    return db_question


# --- StudentQuizAttempt CRUD ---
def create_student_quiz_attempt(db: Session, student_id: int, quiz_id: int,
                                submitted_answers_data: List[Dict[str, Any]]) -> StudentQuizAttempt:
    submitted_answers_dict = {str(ans['question_id']): ans['answer'] for ans in submitted_answers_data}

    db_attempt = StudentQuizAttempt(
        student_id=student_id,
        quiz_id=quiz_id,
        submitted_answers=json.dumps(submitted_answers_dict),
        start_time=func.now(),
        submit_time=func.now(),
        is_completed=True
    )

    total_score = 0.0
    quiz_questions = db.query(QuizQuestion).filter(QuizQuestion.quiz_id == quiz_id).all()
    questions_map = {q.id: q for q in quiz_questions}

    for question_id_str, submitted_answer in submitted_answers_dict.items():
        q_id = int(question_id_str)
        question = questions_map.get(q_id)
        if question and question.correct_answer is not None:
            correct_ans = json.loads(question.correct_answer)

            if question.question_type == QuestionType.single_choice:
                if not isinstance(submitted_answer, str):
                    logger.warning(
                        f"Attempt {quiz_id} by student {student_id}: Question {q_id} (single_choice) submitted non-string answer.")
                    continue
                if submitted_answer == correct_ans:
                    total_score += question.score
            elif question.question_type == QuestionType.multiple_choice:
                if not isinstance(submitted_answer, list):
                    logger.warning(
                        f"Attempt {quiz_id} by student {student_id}: Question {q_id} (multiple_choice) submitted non-list answer.")
                    continue
                submitted_list = sorted(submitted_answer) if isinstance(submitted_answer, list) else []
                correct_list = sorted(correct_ans) if isinstance(correct_ans, list) else []
                if submitted_list == correct_list:
                    total_score += question.score
            elif question.question_type == QuestionType.fill_in_blank:
                if not isinstance(submitted_answer, str):
                    logger.warning(
                        f"Attempt {quiz_id} by student {student_id}: Question {q_id} (fill_in_blank) submitted non-string answer.")
                    continue
                if str(submitted_answer).strip().lower() == str(correct_ans).strip().lower():
                    total_score += question.score
            # Short answer questions are not auto-graded here

    db_attempt.score = float(total_score)

    db.add(db_attempt)
    db.commit()
    db.refresh(db_attempt)
    return db_attempt


def get_student_quiz_attempt_by_id(db: Session, attempt_id: int) -> Optional[StudentQuizAttempt]:
    return db.query(StudentQuizAttempt).filter(StudentQuizAttempt.id == attempt_id).first()


def get_student_quiz_attempt_with_questions(db: Session, attempt_id: int) -> Optional[StudentQuizAttempt]:
    return db.query(StudentQuizAttempt).options(
        joinedload(StudentQuizAttempt.quiz).joinedload(Quiz.questions),
        joinedload(StudentQuizAttempt.student)
    ).filter(StudentQuizAttempt.id == attempt_id).first()


def get_student_quiz_attempts_for_quiz(db: Session, quiz_id: int, skip: int = 0, limit: int = 100) -> List[
    StudentQuizAttempt]:
    return db.query(StudentQuizAttempt).filter(StudentQuizAttempt.quiz_id == quiz_id).offset(skip).limit(limit).all()


def get_student_attempts_for_user(db: Session, student_id: int, skip: int = 0, limit: int = 100) -> List[
    StudentQuizAttempt]:
    return db.query(StudentQuizAttempt).filter(StudentQuizAttempt.student_id == student_id).offset(skip).limit(
        limit).all()


def update_attempt_score_and_feedback(db: Session, db_attempt: StudentQuizAttempt, question_id: int, new_score: float,
                                      feedback: Optional[str]) -> StudentQuizAttempt:
    current_submitted_answers_data = json.loads(db_attempt.submitted_answers)

    quiz_questions = db.query(QuizQuestion).filter(QuizQuestion.quiz_id == db_attempt.quiz_id).all()
    questions_map = {q.id: q for q in quiz_questions}

    total_score = 0.0
    for q_id_str, answer_data in current_submitted_answers_data.items():
        q_id = int(q_id_str)
        question = questions_map.get(q_id)
        if question:
            if q_id == question_id:  # This is the question being manually graded
                total_score += new_score
            else:  # For other questions, re-calculate auto-graded score
                if question.question_type in [QuestionType.single_choice,
                                              QuestionType.fill_in_blank] and question.correct_answer is not None:
                    correct_ans = json.loads(question.correct_answer)
                    if question.question_type == QuestionType.single_choice and answer_data == correct_ans:
                        total_score += question.score
                    elif question.question_type == QuestionType.multiple_choice and isinstance(correct_ans,
                                                                                               list):  # Corrected to ensure correct_ans is a list for comparison
                        submitted_list = sorted(answer_data) if isinstance(answer_data, list) else []
                        correct_list = sorted(correct_ans)
                        if submitted_list == correct_list:
                            total_score += question.score
                    elif question.question_type == QuestionType.fill_in_blank and isinstance(correct_ans,
                                                                                             str):  # Corrected to ensure correct_ans is a string for comparison
                        if str(answer_data).strip().lower() == correct_ans.strip().lower():
                            total_score += question.score
                elif question.question_type == QuestionType.short_answer:
                    pass

    db_attempt.score = float(total_score)
    db.add(db_attempt)
    db.commit()
    db.refresh(db_attempt)
    return db_attempt


# --- Student Specific CRUD ---
def get_student_enrolled_courses(db: Session, student_id: int, skip: int = 0, limit: int = 100) -> List[Course]:
    student_classes = db.query(ClassStudent).filter(ClassStudent.student_id == student_id).all()
    class_ids = [cs.class_id for cs in student_classes]

    if not class_ids:
        return []

    teacher_ids = db.query(Class.teacher_id).filter(Class.id.in_(class_ids)).distinct().all()
    teacher_ids = [t[0] for t in teacher_ids]

    if not teacher_ids:
        return []

    courses = db.query(Course).filter(Course.teacher_id.in_(teacher_ids)).offset(skip).limit(limit).all()
    return courses


def get_students_enrolled_in_course(db: Session, course_id: int) -> List[User]:
    db_course = db.query(Course).filter(Course.id == course_id).first()
    if not db_course:
        return []

    teacher_id = db_course.teacher_id

    teacher_classes = db.query(Class.id).filter(Class.teacher_id == teacher_id).all()
    class_ids = [c[0] for c in teacher_classes]

    if not class_ids:
        return []

    students = db.query(User).join(ClassStudent).filter(
        ClassStudent.class_id.in_(class_ids),
        User.id == ClassStudent.student_id,
        User.role == UserRole.student
    ).distinct().all()
    return students


def get_student_course_progress(db: Session, student_id: int, course_id: int) -> float:
    total_materials = db.query(func.count(Material.id)).filter(Material.course_id == course_id).scalar()

    if not total_materials:
        return 0.0

    completed_materials = db.query(func.count(StudentMaterialProgress.id)).filter(
        StudentMaterialProgress.student_id == student_id,
        StudentMaterialProgress.material_id == Material.id,
        Material.course_id == course_id,
        StudentMaterialProgress.is_completed == True
    ).scalar()

    return float(completed_materials / total_materials) * 100.0 if total_materials > 0 else 0.0


def get_upcoming_quizzes_for_student(db: Session, student_id: int, limit: int = 5) -> List[Quiz]:
    now = datetime.now()

    student_courses = get_student_enrolled_courses(db, student_id)
    course_ids = [c.id for c in student_courses]

    if not course_ids:
        return []

    completed_quiz_ids = db.query(StudentQuizAttempt.quiz_id).filter(StudentQuizAttempt.student_id == student_id).all()
    completed_quiz_ids = {q[0] for q in completed_quiz_ids}

    quizzes = db.query(Quiz).filter(
        Quiz.course_id.in_(course_ids),
        Quiz.is_published == True,
        or_(Quiz.due_date > now, Quiz.due_date.is_(None)),
        ~Quiz.id.in_(completed_quiz_ids)
    ).order_by(Quiz.due_date.asc(), Quiz.created_at.desc()).limit(limit).all()

    return quizzes


# --- Notification CRUD ---
def create_notification(db: Session, notification_data: NotificationCreate) -> Notification:
    db_notification = Notification(
        target_user_id=notification_data.target_user_id,
        target_class_id=notification_data.target_class_id,
        target_course_id=notification_data.target_course_id,
        type=notification_data.type,
        title=notification_data.title,
        content=notification_data.content,
        is_read=notification_data.is_read
    )
    db.add(db_notification)
    db.commit()
    db.refresh(db_notification)
    return db_notification


def get_notifications_for_user(db: Session, student_id: int, limit: int = 5) -> List[Notification]:
    student_class_ids = db.query(ClassStudent.class_id).filter(ClassStudent.student_id == student_id).all()
    student_class_ids = [c[0] for c in student_class_ids]

    student_course_ids = db.query(Course.id).join(Class).join(ClassStudent).filter(
        ClassStudent.student_id == student_id).distinct().all()
    student_course_ids = [c[0] for c in student_course_ids]

    notifications = db.query(Notification).filter(
        or_(
            Notification.target_user_id == student_id,
            Notification.target_class_id.in_(student_class_ids) if student_class_ids else False,
            Notification.target_course_id.in_(student_course_ids) if student_course_ids else False,
            Notification.type == NotificationType.system
        )
    ).order_by(Notification.created_at.desc()).limit(limit).all()

    return notifications


def mark_notification_as_read(db: Session, notification_id: int, user_id: int) -> Optional[Notification]:
    db_notification = db.query(Notification).filter(Notification.id == notification_id).first()
    if db_notification and (
            db_notification.target_user_id == user_id or
            db_notification.type == NotificationType.system or
            db.query(ClassStudent).filter(ClassStudent.student_id == user_id,
                                          ClassStudent.class_id == db_notification.target_class_id).first() or
            db.query(User).join(ClassStudent).join(Class).join(Course).filter(User.id == user_id,
                                                                              Course.id == db_notification.target_course_id).first()
    ):
        db_notification.is_read = True
        db.add(db_notification)
        db.commit()
        db.refresh(db_notification)
        return db_notification
    return None


# --- StudentMaterialProgress CRUD ---
def get_student_material_progress(db: Session, student_id: int, material_id: int) -> Optional[StudentMaterialProgress]:
    return db.query(StudentMaterialProgress).filter(
        StudentMaterialProgress.student_id == student_id,
        StudentMaterialProgress.material_id == material_id
    ).first()


def update_or_create_student_material_progress(
        db: Session,
        student_id: int,
        material_id: int,
        progress_data: StudentMaterialProgressUpdate
) -> StudentMaterialProgress:
    db_progress = get_student_material_progress(db, student_id, material_id)

    if db_progress:
        if progress_data.progress_percentage is not None:
            db_progress.progress_percentage = progress_data.progress_percentage
        if progress_data.last_viewed_timestamp is not None:
            db_progress.last_viewed_timestamp = progress_data.last_viewed_timestamp
        if progress_data.view_duration_increment is not None:
            db_progress.total_view_duration_seconds += progress_data.view_duration_increment
        if progress_data.is_completed is not None:
            db_progress.is_completed = progress_data.is_completed
        db_progress.last_updated_at = func.now()
    else:
        db_progress = StudentMaterialProgress(
            student_id=student_id,
            material_id=material_id,
            progress_percentage=progress_data.progress_percentage if progress_data.progress_percentage is not None else 0.0,
            last_viewed_timestamp=progress_data.last_viewed_timestamp,
            total_view_duration_seconds=progress_data.view_duration_increment if progress_data.view_duration_increment is not None else 0,
            is_completed=progress_data.is_completed if progress_data.is_completed is not None else False,
            last_updated_at=func.now()
        )

    db.add(db_progress)
    db.commit()
    db.refresh(db_progress)
    return db_progress


def get_materials_for_course_by_student(db: Session, course_id: int, student_id: int, skip: int = 0,
                                        limit: int = 100) -> List[Material]:
    return db.query(Material).outerjoin(
        StudentMaterialProgress,
        and_(
            StudentMaterialProgress.material_id == Material.id,
            StudentMaterialProgress.student_id == student_id
        )
    ).filter(Material.course_id == course_id).offset(skip).limit(limit).all()


# --- Learning Statistics CRUD ---
def get_student_daily_study_duration(db: Session, student_id: int, start_date: datetime, end_date: datetime) -> List[
    Tuple[datetime, int]]:
    results = db.query(
        cast(StudentMaterialProgress.last_updated_at, Date),
        func.sum(StudentMaterialProgress.total_view_duration_seconds)
    ).filter(
        StudentMaterialProgress.student_id == student_id,
        StudentMaterialProgress.last_updated_at >= start_date,
        StudentMaterialProgress.last_updated_at <= end_date
    ).group_by(
        cast(StudentMaterialProgress.last_updated_at, Date)
    ).order_by(
        cast(StudentMaterialProgress.last_updated_at, Date)
    ).all()

    return results


def get_course_material_completion_rates(db: Session, course_id: int) -> List[Tuple[Material, float, int]]:
    results = db.query(
        Material,
        func.avg(StudentMaterialProgress.progress_percentage),
        func.count(StudentMaterialProgress.id)
    ).outerjoin(
        StudentMaterialProgress,
        StudentMaterialProgress.material_id == Material.id
    ).filter(
        Material.course_id == course_id
    ).group_by(
        Material.id
    ).all()

    return results


def get_quiz_score_distribution_for_course(db: Session, course_id: int) -> List[Dict[str, Any]]:
    attempts = db.query(StudentQuizAttempt.score).join(Quiz).filter(
        Quiz.course_id == course_id,
        StudentQuizAttempt.is_completed == True,
        StudentQuizAttempt.score.isnot(None)
    ).all()

    scores = [a[0] for a in attempts if a[0] is not None]

    if not scores:
        return []

    bins = [0, 50, 70, 90, 101]
    labels = ["0-50", "51-70", "71-90", "91-100"]

    distribution = {label: 0 for label in labels}

    for score in scores:
        if score >= 91:
            distribution["91-100"] += 1
        elif score >= 71:
            distribution["71-90"] += 1
        elif score >= 51:
            distribution["51-70"] += 1
        else:
            distribution["0-50"] += 1

    total_attempts = len(scores)
    result = []
    for label in labels:
        count = distribution[label]
        percentage = (count / total_attempts) * 100 if total_attempts > 0 else 0
        result.append({
            "score_range": label,
            "student_count": count,
            "percentage": round(percentage, 2)
        })

    return result


def get_student_overall_stats(db: Session, student_id: int) -> Dict[str, Any]:
    stats = {}

    enrolled_courses = get_student_enrolled_courses(db, student_id)
    stats['total_courses_enrolled'] = len(enrolled_courses)

    completed_courses_count = 0
    total_progress = 0.0
    for course in enrolled_courses:
        progress = get_student_course_progress(db, student_id, course.id)
        total_progress += progress
    stats['completed_courses_count'] = completed_courses_count
    stats['average_course_progress'] = round(total_progress / stats['total_courses_enrolled'], 2) if stats[
                                                                                                         'total_courses_enrolled'] > 0 else 0.0

    total_duration_result = db.query(func.sum(StudentMaterialProgress.total_view_duration_seconds)).filter(
        StudentMaterialProgress.student_id == student_id
    ).scalar()
    stats['total_study_duration_seconds'] = int(total_duration_result) if total_duration_result else 0

    avg_score_result = db.query(func.avg(StudentQuizAttempt.score)).filter(
        StudentQuizAttempt.student_id == student_id,
        StudentQuizAttempt.is_completed == True,
        StudentQuizAttempt.score.isnot(None)
    ).scalar()
    stats['average_quiz_score'] = round(float(avg_score_result), 2) if avg_score_result else None

    return stats

# --- LiveSession CRUD ---
# REMOVED: LiveSession related CRUD functions