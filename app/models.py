from sqlalchemy import <PERSON>umn, Integer, String, <PERSON>olean, DateTime, Enum, Text, ForeignKey, BigInteger, Float
from sqlalchemy.sql import func
from sqlalchemy.schema import UniqueConstraint
from sqlalchemy.orm import relationship
from app.database import Base
import enum

# Enums
class UserRole(enum.Enum):
    student = "student"
    teacher = "teacher"
    admin = "admin"

class MaterialType(enum.Enum):
    video = "video"
    image = "image"
    ppt = "ppt"
    pdf = "pdf"
    word = "word"
    audio = "audio"
    text = "text"

class QuizType(enum.Enum):
    quiz = "quiz"
    assignment = "assignment"

class QuestionType(enum.Enum):
    single_choice = "single_choice"
    multiple_choice = "multiple_choice"
    fill_in_blank = "fill_in_blank"
    short_answer = "short_answer"

class NotificationType(enum.Enum):
    system = "system"
    course = "course"
    class_ = "class" # 'class' is a Python keyword, so use 'class_'
    personal = "personal"

# Tables
class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, nullable=False)
    email = Column(String(100), unique=True)
    password_hash = Column(String(255), nullable=False)
    full_name = Column(String(100))
    role = Column(Enum(UserRole), nullable=False)
    avatar_url = Column(String(255))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    courses = relationship("Course", back_populates="teacher", foreign_keys="[Course.teacher_id]")
    classes_managed = relationship("Class", back_populates="teacher", foreign_keys="[Class.teacher_id]") # 修正：back_popates -> back_populates
    student_classes = relationship("ClassStudent", back_populates="student")
    created_materials = relationship("Material", back_populates="created_by_user", foreign_keys="[Material.created_by_user_id]")
    created_quizzes = relationship("Quiz", back_populates="created_by_user", foreign_keys="[Quiz.created_by_user_id]")
    material_progresses = relationship("StudentMaterialProgress", back_populates="student")
    quiz_attempts = relationship("StudentQuizAttempt", back_populates="student")


class Course(Base):
    __tablename__ = "courses"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False)
    description = Column(Text)
    cover_image_url = Column(String(255))
    teacher_id = Column(Integer, ForeignKey('users.id'), index=True)
    status = Column(String(50), default="draft")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    teacher = relationship("User", back_populates="courses", foreign_keys=[teacher_id])
    materials = relationship("Material", back_populates="course")
    quizzes = relationship("Quiz", back_populates="course")


class Class(Base):
    __tablename__ = "classes"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    teacher_id = Column(Integer, ForeignKey('users.id'), index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    teacher = relationship("User", back_populates="classes_managed", foreign_keys=[teacher_id])
    students = relationship("ClassStudent", back_populates="class_")


class ClassStudent(Base):
    __tablename__ = "class_students"

    class_id = Column(Integer, ForeignKey('classes.id'), primary_key=True, index=True)
    student_id = Column(Integer, ForeignKey('users.id'), primary_key=True, index=True)
    joined_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    class_ = relationship("Class", back_populates="students", foreign_keys=[class_id])
    student = relationship("User", back_populates="student_classes", foreign_keys=[student_id])


class CourseClass(Base):
    __tablename__ = "course_classes"

    course_id = Column(Integer, ForeignKey('courses.id'), primary_key=True, index=True)
    class_id = Column(Integer, ForeignKey('classes.id'), primary_key=True, index=True)
    assigned_at = Column(DateTime(timezone=True), server_default=func.now())
    is_active = Column(Boolean, default=True)

    # Relationships
    course = relationship("Course", back_populates="course_classes", foreign_keys=[course_id])
    class_ = relationship("Class", back_populates="course_classes", foreign_keys=[class_id])


class Material(Base):
    __tablename__ = "materials"

    id = Column(Integer, primary_key=True, index=True)
    course_id = Column(Integer, ForeignKey('courses.id'), nullable=False, index=True)
    title = Column(String(255), nullable=False)
    description = Column(Text)
    file_path_minio = Column(String(255), nullable=False)
    file_type = Column(Enum(MaterialType), nullable=False)
    file_size_bytes = Column(BigInteger)
    duration_seconds = Column(Integer)
    preview_url = Column(String(255))
    extracted_text_content = Column(Text)
    milvus_vector_id = Column(String(255), unique=True)
    created_by_user_id = Column(Integer, ForeignKey('users.id'), index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    course = relationship("Course", back_populates="materials")
    created_by_user = relationship("User", back_populates="created_materials", foreign_keys=[created_by_user_id])
    material_progresses = relationship("StudentMaterialProgress", back_populates="material")


class StudentMaterialProgress(Base):
    __tablename__ = "student_material_progress"

    id = Column(Integer, primary_key=True, index=True)
    student_id = Column(Integer, ForeignKey('users.id'), index=True)
    material_id = Column(Integer, ForeignKey('materials.id'), index=True)
    progress_percentage = Column(Float, default=0.0)
    last_viewed_timestamp = Column(Integer)
    is_completed = Column(Boolean, default=False)
    total_view_duration_seconds = Column(Integer, default=0)
    last_updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    __table_args__ = (UniqueConstraint('student_id', 'material_id', name='_student_material_uc'),)

    # Relationships
    student = relationship("User", back_populates="material_progresses")
    material = relationship("Material", back_populates="material_progresses")


class Quiz(Base):
    __tablename__ = "quizzes"

    id = Column(Integer, primary_key=True, index=True)
    course_id = Column(Integer, ForeignKey('courses.id'), nullable=False, index=True)
    title = Column(String(255), nullable=False)
    description = Column(Text)
    quiz_type = Column(Enum(QuizType), nullable=False)
    due_date = Column(DateTime(timezone=True))
    is_published = Column(Boolean, default=False)
    created_by_user_id = Column(Integer, ForeignKey('users.id'), index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    course = relationship("Course", back_populates="quizzes")
    created_by_user = relationship("User", back_populates="created_quizzes", foreign_keys=[created_by_user_id])
    questions = relationship("QuizQuestion", back_populates="quiz")
    attempts = relationship("StudentQuizAttempt", back_populates="quiz")


class QuizQuestion(Base):
    __tablename__ = "quiz_questions"

    id = Column(Integer, primary_key=True, index=True)
    quiz_id = Column(Integer, ForeignKey('quizzes.id'), nullable=False, index=True)
    question_text = Column(Text, nullable=False)
    question_type = Column(Enum(QuestionType), nullable=False)
    options = Column(Text) # Stored as JSON string
    correct_answer = Column(Text) # Stored as JSON string
    score = Column(Integer, default=1)
    question_order = Column(Integer)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    quiz = relationship("Quiz", back_populates="questions")


class StudentQuizAttempt(Base):
    __tablename__ = "student_quiz_attempts"

    id = Column(Integer, primary_key=True, index=True)
    student_id = Column(Integer, ForeignKey('users.id'), index=True)
    quiz_id = Column(Integer, ForeignKey('quizzes.id'), index=True)
    score = Column(Float)
    submitted_answers = Column(Text) # Stored as JSON string
    start_time = Column(DateTime(timezone=True))
    submit_time = Column(DateTime(timezone=True))
    is_completed = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    student = relationship("User", back_populates="quiz_attempts")
    quiz = relationship("Quiz", back_populates="attempts")


class Notification(Base):
    __tablename__ = "notifications"

    id = Column(Integer, primary_key=True, index=True)
    target_user_id = Column(Integer, ForeignKey('users.id'), index=True)
    target_class_id = Column(Integer, ForeignKey('classes.id'), index=True)
    target_course_id = Column(Integer, ForeignKey('courses.id'), index=True)
    type = Column(Enum(NotificationType), nullable=False)
    title = Column(String(255), nullable=False)
    content = Column(Text)
    is_read = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships (optional, based on need, lazy='joined' for faster loading)
    target_user = relationship("User", foreign_keys=[target_user_id])
    target_class = relationship("Class", foreign_keys=[target_class_id])
    target_course = relationship("Course", foreign_keys=[target_course_id])

# REMOVED: LiveSession table (Agora related)
# class LiveSession(Base):
#     __tablename__ = "live_sessions"
#     # ... (removed fields) ...
#     # Relationships
#     # course = relationship("Course", back_populates="live_sessions")
#     # host = relationship("User", back_populates="live_sessions_hosted", foreign_keys="[LiveSession.host_user_id]")