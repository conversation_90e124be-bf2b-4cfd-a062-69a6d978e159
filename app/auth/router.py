from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.database import get_db
from app.schemas import UserCreate, User<PERSON>ogin, Token, UserPublic, PasswordChange
from app.crud import create_user, get_user_by_username, update_user_password, get_user_by_email
from app.auth.security import verify_password, create_access_token, get_password_hash
from app.auth.dependencies import get_current_active_user
from app.models import User, UserRole  # 修正：导入 User 模型
from app.exceptions import BadRequestError, UnauthorizedError
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/auth", tags=["Auth"])


@router.post("/register", response_model=UserPublic, status_code=status.HTTP_201_CREATED)
def register_user(user: UserCreate, db: Session = Depends(get_db)):
    """
    User registration. Defaults to 'student' role.
    """
    db_user_by_username = get_user_by_username(db, username=user.username)
    if db_user_by_username:
        logger.warning(f"Registration failed: Username '{user.username}' already registered.")
        raise BadRequestError(detail="Username already registered", code="USERNAME_EXISTS")

    if user.email:
        db_user_by_email = get_user_by_email(db, email=user.email)
        if db_user_by_email:
            logger.warning(f"Registration failed: Email '{user.email}' already registered.")
            raise BadRequestError(detail="Email already registered", code="EMAIL_EXISTS")

    # Keep the role as specified in the registration request
    # user.role is already set from the UserCreate schema
    new_user = create_user(db=db, user=user)
    logger.info(f"User '{new_user.username}' ({new_user.role.value}) registered successfully with ID: {new_user.id}.")
    return new_user


@router.post("/token", response_model=Token)
def login_for_access_token(user_login: UserLogin, db: Session = Depends(get_db)):
    """
    User login and get access token.
    """
    user = get_user_by_username(db, username=user_login.username)
    if not user or not verify_password(user_login.password, user.password_hash):
        logger.warning(f"Login failed: Incorrect credentials for username '{user_login.username}'.")
        raise UnauthorizedError(detail="Incorrect username or password", code="INVALID_CREDENTIALS")
    if not user.is_active:
        logger.warning(f"Login failed: Inactive user '{user_login.username}'.")
        raise UnauthorizedError(detail="Inactive user", code="USER_INACTIVE")

    access_token = create_access_token(
        data={"sub": user.username, "user_id": user.id, "role": user.role.value}
    )
    logger.info(f"User '{user.username}' logged in successfully.")
    return {"access_token": access_token, "token_type": "bearer"}


@router.post("/change-password", status_code=status.HTTP_200_OK)
def change_password(
        password_change: PasswordChange,
        current_user: User = Depends(get_current_active_user),  # 使用 User 类型提示
        db: Session = Depends(get_db)
):
    """
    Change password for the current logged-in user.
    """
    if not verify_password(password_change.old_password, current_user.password_hash):
        logger.warning(f"Password change failed for user '{current_user.username}': Old password incorrect.")
        raise BadRequestError(detail="Old password is incorrect", code="INCORRECT_OLD_PASSWORD")

    if password_change.old_password == password_change.new_password:
        raise BadRequestError(detail="New password cannot be the same as old password.", code="SAME_PASSWORD")

    new_hashed_password = get_password_hash(password_change.new_password)
    update_user_password(db, current_user, new_hashed_password)
    logger.info(f"Password for user '{current_user.username}' updated successfully.")
    return {"message": "Password updated successfully"}