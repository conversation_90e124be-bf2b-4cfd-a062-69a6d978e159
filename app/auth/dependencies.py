from fastapi import Depends, HTTPException, status, WebSocketException, WebSocket
from fastapi.security import OAuth2<PERSON><PERSON>wordBearer
from sqlalchemy.orm import Session
from app.database import get_db
from app.auth.security import decode_access_token
from app.crud import get_user_by_id
from app.models import User, UserRole
from app.schemas import TokenData
from typing import Optional  # 导入 Optional
import logging

logger = logging.getLogger(__name__)

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/v1/auth/token")


async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)) -> User:
    """
    HTTP authentication dependency, used for regular REST APIs.
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    payload = decode_access_token(token)
    if payload is None:
        logger.warning("JWT decode failed or payload is empty for HTTP request.")
        raise credentials_exception

    username: str = payload.get("sub")
    user_id: int = payload.get("user_id")
    role: str = payload.get("role")

    if username is None or user_id is None or role is None:
        logger.warning(f"Invalid token payload for HTTP request: missing username, user_id or role. Payload: {payload}")
        raise credentials_exception

    token_data = TokenData(username=username, user_id=user_id, role=UserRole(role))

    user = get_user_by_id(db, user_id=token_data.user_id)
    if user is None:
        logger.warning(f"User with ID {token_data.user_id} from token not found in DB for HTTP request.")
        raise credentials_exception
    return user


async def get_current_websocket_user(websocket: WebSocket, token: Optional[str] = None,
                                     db: Session = Depends(get_db)) -> User:
    """
    WebSocket authentication dependency. Gets JWT token from query parameter 'token'.
    """
    token = websocket.query_params.get("token")
    if not token:
        logger.warning("WebSocket authentication failed: Missing token in query params.")
        raise WebSocketException(code=status.WS_1008_POLICY_VIOLATION, reason="Authentication token required")

    payload = decode_access_token(token)
    if payload is None:
        logger.warning("WebSocket authentication failed: Invalid token.")
        raise WebSocketException(code=status.WS_1008_POLICY_VIOLATION, reason="Invalid authentication token")

    username: str = payload.get("sub")
    user_id: int = payload.get("user_id")
    role: str = payload.get("role")

    if username is None or user_id is None or role is None:
        logger.warning(f"WebSocket authentication failed: Invalid token payload for user_id={user_id}.")
        raise WebSocketException(code=status.WS_1008_POLICY_VIOLATION, reason="Invalid authentication token payload")

    token_data = TokenData(username=username, user_id=user_id, role=UserRole(role))

    user = get_user_by_id(db, user_id=token_data.user_id)
    if user is None:
        logger.warning(f"WebSocket authentication failed: User with ID {token_data.user_id} not found.")
        raise WebSocketException(code=status.WS_1008_POLICY_VIOLATION, reason="User not found")

    if not user.is_active:
        logger.warning(f"WebSocket authentication failed: Inactive user {user.username}.")
        raise WebSocketException(code=status.WS_1008_POLICY_VIOLATION, reason="User is inactive")

    return user


def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    if not current_user.is_active:
        logger.warning(f"Inactive user {current_user.username} tried to access active-only endpoint.")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Inactive user")
    return current_user


def get_current_teacher(current_user: User = Depends(get_current_active_user)) -> User:
    if current_user.role != UserRole.teacher and current_user.role != UserRole.admin:
        logger.warning(
            f"User {current_user.username} (role: {current_user.role.value}) tried to access teacher-only endpoint.")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions (Teacher or Admin required)"
        )
    return current_user


def get_current_admin(current_user: User = Depends(get_current_active_user)) -> User:
    if current_user.role != UserRole.admin:
        logger.warning(
            f"User {current_user.username} (role: {current_user.role.value}) tried to access admin-only endpoint.")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions (Admin required)"
        )
    return current_user