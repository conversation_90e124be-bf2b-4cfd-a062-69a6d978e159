from fastapi import FastAP<PERSON>, Depends, Request, status
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.exceptions import RequestValidationError
from sqlalchemy.orm import Session
from app.database import Base, engine, get_db
from app.auth import router as auth_router
from app.api.v1.routers import users as users_router
from app.api.v1.routers import materials as materials_router
from app.api.v1.routers import courses as courses_router
from app.api.v1.routers import classes as classes_router
from app.api.v1.routers import quizzes as quizzes_router
from app.api.v1.routers import students as students_router
from app.api.v1.routers import notifications as notifications_router
from app.api.v1.routers import reports as reports_router
from app.api.v1.routers import admin as admin_router
# from app.api.v1.routers import live_sessions as live_sessions_router # REMOVED: Agora related import
from app.models import User, ClassStudent, Class, QuizQuestion, QuestionType, StudentQuizAttempt, Material, Notification # Ensure all models are imported to be recognized by Base.metadata.create_all
from app.utils.minio_client import ensure_bucket_exists
from app.utils.milvus_client_utils import init_milvus, get_sentence_transformer_model
from app.config import setup_logging
from app.exceptions import CustomHTTPException, BadRequestError, UnauthorizedError, ForbiddenError, NotFoundError, InternalServerError
from app.schemas import ErrorResponse
import logging
from app.auth.dependencies import get_current_active_user # 修正：导入 get_current_active_user

logger = logging.getLogger(__name__)

# Create database tables (if they don't exist)
def create_db_and_tables():
    Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="Education Platform API",
    description="Backend API for Education Platform (Teacher & Student)",
    version="0.1.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# --- Global Exception Handling ---
@app.exception_handler(CustomHTTPException)
async def custom_http_exception_handler(request: Request, exc: CustomHTTPException):
    """Handles all CustomHTTPException"""
    logger.error(f"Custom HTTP Exception occurred: {exc.detail}, Code: {exc.code}, Status: {exc.status_code}, Data: {exc.data}")
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(detail=exc.detail, code=exc.code, data=exc.data).dict()
    )

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Handles Pydantic validation errors"""
    error_details = exc.errors()
    # Format validation error messages to be more readable
    formatted_errors = [
        {"loc": err["loc"], "msg": err["msg"], "type": err["type"]} for err in error_details
    ]
    logger.warning(f"Validation Error: {formatted_errors}")
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=ErrorResponse(detail="Validation error", code="VALIDATION_ERROR", data=formatted_errors).dict()
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Catches all unhandled general exceptions"""
    logger.exception(f"Unhandled Internal Server Error: {exc}") # Use exception to log traceback
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=ErrorResponse(detail="An unexpected error occurred. Please try again later.", code="INTERNAL_SERVER_ERROR").dict()
    )

# Register routers
app.include_router(auth_router.router)
app.include_router(users_router.router, prefix="/api/v1")
app.include_router(materials_router.router, prefix="/api/v1")
app.include_router(courses_router.router, prefix="/api/v1")
app.include_router(classes_router.router, prefix="/api/v1")
app.include_router(quizzes_router.router, prefix="/api/v1")
app.include_router(students_router.router, prefix="/api/v1")
app.include_router(notifications_router.router, prefix="/api/v1")
app.include_router(reports_router.router, prefix="/api/v1")
app.include_router(admin_router.router, prefix="/api/v1")
# app.include_router(live_sessions_router.router, prefix="/api/v1") # REMOVED: Agora related router


@app.on_event("startup")
def on_startup():
    """
    Startup events for the application.
    """
    try:
        create_db_and_tables() # Create database tables
        logger.info("Database tables created successfully.")
    except Exception as e:
        logger.warning(f"Database initialization failed: {e}. Continuing without database...")

    try:
        ensure_bucket_exists() # Ensure Minio bucket exists
        logger.info("Minio bucket check completed.")
    except Exception as e:
        logger.warning(f"Minio initialization failed: {e}. Continuing without Minio...")

    try:
        get_sentence_transformer_model() # Preload AI model
        logger.info("AI model loaded successfully.")
    except Exception as e:
        logger.warning(f"AI model loading failed: {e}. Continuing without AI model...")

    try:
        init_milvus() # Initialize Milvus connection and Collection
        logger.info("Milvus initialized successfully.")
    except Exception as e:
        logger.warning(f"Milvus initialization failed: {e}. Continuing without Milvus...")

    logger.info("Application started successfully.")

@app.get("/", response_class=HTMLResponse)
async def read_root():
    """
    Root path welcome message.
    """
    return """
    <html>
        <head>
            <title>Education Platform API</title>
        </head>
        <body>
            <h1>Welcome to Education Platform API!</h1>
            <p>Go to <a href="/docs">/docs</a> for API documentation (Swagger UI).</p>
            <p>Go to <a href="/redoc">/redoc</a> for alternative API documentation (ReDoc).</p>
        </body>
    </html>
    """

@app.get("/api/v1/protected-test")
def protected_test(current_user: User = Depends(get_current_active_user)):
    logger.info(f"Protected test endpoint accessed by {current_user.username}")
    return {"message": f"Hello, {current_user.username}! You are a {current_user.role.value}."}