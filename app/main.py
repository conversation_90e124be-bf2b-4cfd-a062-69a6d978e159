from fastapi import FastAP<PERSON>, Depends, Request, Response, status
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.base import BaseHTTPMiddleware
from sqlalchemy.orm import Session
import time
import json
import threading
from datetime import datetime
from app.database import Base, engine, get_db
from app.auth import router as auth_router
from app.api.v1.routers import users as users_router
from app.api.v1.routers import materials as materials_router
from app.api.v1.routers import courses as courses_router
from app.api.v1.routers import classes as classes_router
from app.api.v1.routers import quizzes as quizzes_router
from app.api.v1.routers import students as students_router
from app.api.v1.routers import notifications as notifications_router
from app.api.v1.routers import reports as reports_router
from app.api.v1.routers import admin as admin_router
# from app.api.v1.routers import live_sessions as live_sessions_router # REMOVED: Agora related import
from app.models import User, ClassStudent, Class, QuizQuestion, QuestionType, StudentQuizAttempt, Material, Notification # Ensure all models are imported to be recognized by Base.metadata.create_all
from app.utils.minio_client import ensure_bucket_exists
from app.utils.milvus_client_utils import init_milvus, get_sentence_transformer_model
from app.config import setup_logging
from app.exceptions import CustomHTTPException, BadRequestError, UnauthorizedError, ForbiddenError, NotFoundError, InternalServerError
from app.schemas import ErrorResponse
import logging
from app.auth.dependencies import get_current_active_user # 修正：导入 get_current_active_user

logger = logging.getLogger(__name__)

# API Debug Middleware for detailed request/response logging
class APIDebugMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Record start time
        start_time = time.time()

        # Get request details
        method = request.method
        url = str(request.url)
        path = request.url.path
        query_params = dict(request.query_params)
        headers = dict(request.headers)

        # Get client IP
        client_ip = request.client.host if request.client else "unknown"

        # Read request body (for POST/PUT requests)
        request_body = None
        if method in ["POST", "PUT", "PATCH"]:
            try:
                body = await request.body()
                if body:
                    # Try to parse as JSON for better logging
                    try:
                        request_body = json.loads(body.decode())
                        # Hide sensitive data
                        if isinstance(request_body, dict) and "password" in request_body:
                            request_body["password"] = "***HIDDEN***"
                    except:
                        request_body = body.decode()[:200] + "..." if len(body) > 200 else body.decode()

                # Recreate request with body for downstream processing
                async def receive():
                    return {"type": "http.request", "body": body}
                request._receive = receive
            except Exception as e:
                logger.warning(f"Failed to read request body: {e}")

        # Log request details
        request_log = f"🔵 API REQUEST | {method} {path}"
        logger.info(request_log)
        logger.info(f"   📍 Full URL: {url}")
        logger.info(f"   🌐 Client IP: {client_ip}")

        # Add to debug logs
        with debug_lock:
            if len(debug_logs) >= 100:  # Keep only last 100 logs
                debug_logs.pop(0)
            debug_logs.append({
                'timestamp': datetime.now().strftime('%H:%M:%S'),
                'type': 'request',
                'message': f"{method} {path} from {client_ip}"
            })

        if query_params:
            logger.info(f"   🔍 Query Params: {query_params}")

        # Log important headers (excluding sensitive ones)
        important_headers = {
            k: v for k, v in headers.items()
            if k.lower() in ['content-type', 'user-agent', 'origin', 'referer', 'accept']
        }
        if important_headers:
            logger.info(f"   📋 Headers: {important_headers}")

        # Log authorization info (without token details)
        if 'authorization' in headers:
            auth_type = headers['authorization'].split(' ')[0] if ' ' in headers['authorization'] else 'Unknown'
            logger.info(f"   🔐 Auth: {auth_type} token provided")

        if request_body:
            logger.info(f"   📄 Request Body: {request_body}")

        # Process request
        try:
            response = await call_next(request)

            # Calculate processing time
            process_time = time.time() - start_time

            # Get response details
            status_code = response.status_code

            # Read response body for logging
            response_body = None
            if hasattr(response, 'body'):
                try:
                    # For streaming responses, we can't easily read the body
                    # So we'll just log the status and headers
                    pass
                except:
                    pass

            # Determine status emoji
            if status_code < 300:
                status_emoji = "✅"
            elif status_code < 400:
                status_emoji = "🔄"
            elif status_code < 500:
                status_emoji = "⚠️"
            else:
                status_emoji = "❌"

            # Log response details
            response_log = f"{status_emoji} API RESPONSE | {method} {path} | {status_code} | {process_time:.3f}s"
            logger.info(response_log)

            # Add to debug logs
            with debug_lock:
                if len(debug_logs) >= 100:
                    debug_logs.pop(0)
                debug_logs.append({
                    'timestamp': datetime.now().strftime('%H:%M:%S'),
                    'type': 'response',
                    'message': f"{status_code} {method} {path} ({process_time:.3f}s)"
                })

            # Log response headers
            response_headers = dict(response.headers)
            important_response_headers = {
                k: v for k, v in response_headers.items()
                if k.lower() in ['content-type', 'content-length', 'access-control-allow-origin']
            }
            if important_response_headers:
                logger.info(f"   📋 Response Headers: {important_response_headers}")

            # Add custom debug headers to response
            response.headers["X-Process-Time"] = str(process_time)
            response.headers["X-Request-ID"] = f"{int(start_time * 1000)}"

            return response

        except Exception as e:
            # Calculate processing time for errors
            process_time = time.time() - start_time

            # Log error details
            logger.error(f"❌ API ERROR | {method} {path} | {process_time:.3f}s")
            logger.error(f"   🚨 Error: {str(e)}")
            logger.exception("   📋 Full traceback:")

            # Re-raise the exception
            raise

# Create database tables (if they don't exist)
def create_db_and_tables():
    Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="Education Platform API",
    description="Backend API for Education Platform (Teacher & Student)",
    version="0.1.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add API Debug middleware (should be added first to catch all requests)
# app.add_middleware(APIDebugMiddleware)  # Temporarily disabled due to issues

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://localhost:3001",
        "http://localhost:3002",  # New frontend port
        "http://127.0.0.1:3000",
        "http://127.0.0.1:3001",
        "http://127.0.0.1:3002"
    ],  # React dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --- Global Exception Handling ---
@app.exception_handler(CustomHTTPException)
async def custom_http_exception_handler(request: Request, exc: CustomHTTPException):
    """Handles all CustomHTTPException"""
    logger.error(f"Custom HTTP Exception occurred: {exc.detail}, Code: {exc.code}, Status: {exc.status_code}, Data: {exc.data}")
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(detail=exc.detail, code=exc.code, data=exc.data).dict()
    )

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Handles Pydantic validation errors"""
    error_details = exc.errors()
    # Format validation error messages to be more readable
    formatted_errors = [
        {"loc": err["loc"], "msg": err["msg"], "type": err["type"]} for err in error_details
    ]
    logger.warning(f"Validation Error: {formatted_errors}")
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=ErrorResponse(detail="Validation error", code="VALIDATION_ERROR", data=formatted_errors).dict()
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Catches all unhandled general exceptions"""
    logger.exception(f"Unhandled Internal Server Error: {exc}") # Use exception to log traceback
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=ErrorResponse(detail="An unexpected error occurred. Please try again later.", code="INTERNAL_SERVER_ERROR").dict()
    )

# Register routers
app.include_router(auth_router.router, prefix="/api/v1")
app.include_router(users_router.router, prefix="/api/v1")
app.include_router(materials_router.router, prefix="/api/v1")
app.include_router(courses_router.router, prefix="/api/v1")
app.include_router(classes_router.router, prefix="/api/v1")
app.include_router(quizzes_router.router, prefix="/api/v1")
app.include_router(students_router.router, prefix="/api/v1")
app.include_router(notifications_router.router, prefix="/api/v1")
app.include_router(reports_router.router, prefix="/api/v1")
app.include_router(admin_router.router, prefix="/api/v1")
# Debug panel routes will be added directly below
# app.include_router(live_sessions_router.router, prefix="/api/v1") # REMOVED: Agora related router

# Simple debug storage
debug_logs = []
debug_lock = threading.Lock()

@app.get("/api/v1/debug/api-calls", response_class=HTMLResponse)
async def debug_panel():
    """Simple debug panel showing recent API calls"""
    with debug_lock:
        recent_calls = list(reversed(debug_logs[-50:]))  # Show last 50 calls

    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>API Debug Panel - Education Platform</title>
        <style>
            body {{ font-family: 'Consolas', monospace; margin: 20px; background: #1a1a1a; color: #e0e0e0; }}
            .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; text-align: center; }}
            .stats {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }}
            .stat-card {{ background: #2d2d2d; padding: 15px; border-radius: 8px; border-left: 4px solid #667eea; text-align: center; }}
            .stat-number {{ font-size: 24px; font-weight: bold; color: #667eea; }}
            .log-container {{ background: #2d2d2d; border-radius: 8px; padding: 20px; max-height: 600px; overflow-y: auto; }}
            .log-entry {{ margin-bottom: 10px; padding: 10px; border-radius: 5px; background: #333; border-left: 4px solid #666; }}
            .log-entry.request {{ border-left-color: #4CAF50; background: #1a2e1a; }}
            .log-entry.response {{ border-left-color: #2196F3; background: #1a1e2e; }}
            .log-entry.error {{ border-left-color: #f44336; background: #2e1a1a; }}
            .timestamp {{ color: #888; font-size: 12px; }}
            .message {{ font-size: 14px; margin-top: 5px; }}
            .btn {{ background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🔍 API Debug Panel</h1>
            <p>Real-time monitoring of Education Platform API calls</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{len(debug_logs)}</div>
                <div>Total API Calls</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{len([l for l in debug_logs if 'REQUEST' in l.get('type', '')])}</div>
                <div>Requests</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{len([l for l in debug_logs if 'RESPONSE' in l.get('type', '')])}</div>
                <div>Responses</div>
            </div>
        </div>

        <button class="btn" onclick="window.location.reload()">🔄 Refresh</button>
        <button class="btn" onclick="clearLogs()">🗑️ Clear</button>

        <div class="log-container">
            {generate_debug_entries(recent_calls)}
        </div>

        <script>
            function clearLogs() {{
                if (confirm('Clear all logs?')) {{
                    fetch('/api/v1/debug/clear', {{method: 'POST'}}).then(() => location.reload());
                }}
            }}
            setTimeout(() => location.reload(), 10000); // Auto-refresh every 10 seconds
        </script>
    </body>
    </html>
    """
    return html_content

def generate_debug_entries(calls):
    """Generate HTML for debug entries"""
    if not calls:
        return '<div style="text-align: center; color: #888; padding: 40px;">No API calls recorded yet.</div>'

    html = ""
    for call in calls:
        entry_type = call.get('type', '').lower()
        timestamp = call.get('timestamp', '')
        message = call.get('message', '')

        html += f'''
        <div class="log-entry {entry_type}">
            <div class="timestamp">{timestamp}</div>
            <div class="message">{message}</div>
        </div>
        '''
    return html

@app.post("/api/v1/debug/clear")
async def clear_debug_logs():
    """Clear debug logs"""
    with debug_lock:
        debug_logs.clear()
    return {{"message": "Debug logs cleared"}}

@app.get("/api/v1/debug/logs")
async def get_debug_logs():
    """Get debug logs as JSON"""
    with debug_lock:
        return {{"logs": debug_logs[-50:]}}  # Return last 50 logs


@app.on_event("startup")
def on_startup():
    """
    Startup events for the application.
    """
    try:
        create_db_and_tables() # Create database tables
        logger.info("Database tables created successfully.")
    except Exception as e:
        logger.warning(f"Database initialization failed: {e}. Continuing without database...")

    try:
        ensure_bucket_exists() # Ensure Minio bucket exists
        logger.info("Minio bucket check completed.")
    except Exception as e:
        logger.warning(f"Minio initialization failed: {e}. Continuing without Minio...")

    try:
        get_sentence_transformer_model() # Preload AI model
        logger.info("AI model loaded successfully.")
    except Exception as e:
        logger.warning(f"AI model loading failed: {e}. Continuing without AI model...")

    try:
        init_milvus() # Initialize Milvus connection and Collection
        logger.info("Milvus initialized successfully.")
    except Exception as e:
        logger.warning(f"Milvus initialization failed: {e}. Continuing without Milvus...")

    logger.info("Application started successfully.")

@app.get("/", response_class=HTMLResponse)
async def read_root():
    """
    Root path welcome message.
    """
    return """
    <html>
        <head>
            <title>Education Platform API</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
                .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                h1 { color: #333; text-align: center; }
                .links { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-top: 30px; }
                .link-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; text-decoration: none; text-align: center; transition: transform 0.2s; }
                .link-card:hover { transform: translateY(-2px); text-decoration: none; color: white; }
                .link-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; }
                .link-desc { font-size: 14px; opacity: 0.9; }
                .status { background: #4CAF50; color: white; padding: 5px 15px; border-radius: 20px; font-size: 12px; display: inline-block; margin-top: 10px; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🎓 Education Platform API</h1>
                <div class="status">✅ Server Running</div>

                <div class="links">
                    <a href="/docs" class="link-card">
                        <div class="link-title">📚 API Documentation</div>
                        <div class="link-desc">Interactive Swagger UI for testing APIs</div>
                    </a>

                    <a href="/redoc" class="link-card">
                        <div class="link-title">📖 ReDoc Documentation</div>
                        <div class="link-desc">Alternative API documentation</div>
                    </a>

                    <a href="/api/v1/debug/api-calls" class="link-card">
                        <div class="link-title">🔍 Debug Panel</div>
                        <div class="link-desc">Real-time API call monitoring</div>
                    </a>

                    <a href="http://localhost:3002" class="link-card" target="_blank">
                        <div class="link-title">🎨 Frontend App</div>
                        <div class="link-desc">React frontend application</div>
                    </a>
                </div>

                <div style="margin-top: 30px; text-align: center; color: #666; font-size: 14px;">
                    <p>🚀 Backend API Server | Port 8000</p>
                    <p>🎯 Features: Authentication, File Upload, AI Analysis, Course Management</p>
                </div>
            </div>
        </body>
    </html>
    """

@app.get("/api/v1/protected-test")
def protected_test(current_user: User = Depends(get_current_active_user)):
    logger.info(f"Protected test endpoint accessed by {current_user.username}")
    return {"message": f"Hello, {current_user.username}! You are a {current_user.role.value}."}