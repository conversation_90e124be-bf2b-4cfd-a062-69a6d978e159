from fastapi import APIRouter, Depends, HTTPException, status, Query, Path, Body  # 导入 Body
from sqlalchemy.orm import Session
from typing import List, Optional

from app.database import get_db
from app.schemas import (CourseCreate, CourseUpdate, CourseInDB, CourseDetail, CourseSummaryForTeacher,
                        CourseClassAssignRequest, CourseClassRemoveRequest, CourseWithClasses, ClassInDB)
from app.crud import (create_course, get_course_by_id, get_courses, update_course, delete_course,
                     get_course_with_relations_by_id, assign_course_to_class, remove_course_from_class,
                     get_classes_for_course, get_available_classes_for_course, get_class_by_id)
from app.auth.dependencies import get_current_teacher
from app.models import User, UserRole
from app.exceptions import BadRequestError, NotFoundError, ForbiddenError, InternalServerError
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/courses", tags=["Courses"])


@router.post("/", response_model=CourseInDB, status_code=status.HTTP_201_CREATED)
async def create_new_course(
        course: CourseCreate,
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [Teacher] Create a new course.
    """
    db_course = create_course(db=db, course=course, teacher_id=current_user.id)
    logger.info(f"Course '{db_course.title}' (ID: {db_course.id}) created by teacher {current_user.id}.")
    return db_course


@router.get("/", response_model=List[CourseSummaryForTeacher])
async def read_courses(
        skip: int = Query(0, ge=0),
        limit: int = Query(100, ge=1, le=1000),
        search: Optional[str] = Query(None, max_length=255),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [Teacher] Get a list of courses. Teachers see their own classes, Admins see all.
    Supports pagination and search by title/description.
    """
    teacher_id_filter: Optional[int] = None
    if current_user.role == UserRole.teacher:
        teacher_id_filter = current_user.id

    courses = get_courses(db=db, skip=skip, limit=limit, teacher_id=teacher_id_filter, search_query=search)

    result_courses = []
    for course in courses:
        teacher_name = course.teacher.full_name or course.teacher.username if course.teacher else "Unknown Teacher"
        result_courses.append(CourseSummaryForTeacher(
            id=course.id,
            title=course.title,
            description=course.description,
            cover_image_url=course.cover_image_url,
            teacher_id=course.teacher_id,
            status=course.status,
            created_at=course.created_at,
            updated_at=course.updated_at,
            teacher_name=teacher_name
        ))
    return result_courses


@router.get("/{course_id}", response_model=CourseDetail)
async def read_course(
        course_id: int = Path(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [Teacher] Get course details, including its associated materials and quizzes.
    """
    db_course = get_course_with_relations_by_id(db, course_id=course_id)
    if not db_course:
        raise NotFoundError(detail="Course not found.")

    if current_user.role == UserRole.teacher and db_course.teacher_id != current_user.id:
        raise ForbiddenError(detail="Not enough permissions to view this course.")

    return db_course


@router.put("/{course_id}", response_model=CourseInDB)
async def update_course_info(
        course_id: int = Path(..., gt=0),
        course_update: CourseUpdate = Body(...),  # 修正：使用 Body(...) 明确为请求体，并将其标记为必需
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [Teacher] Update course information.
    """
    db_course = get_course_by_id(db, course_id=course_id)
    if not db_course:
        raise NotFoundError(detail="Course not found.")

    if current_user.role == UserRole.teacher and db_course.teacher_id != current_user.id:
        raise ForbiddenError(detail="Not enough permissions to update this course.")

    if not course_update:  # 检查请求体是否为空
        raise BadRequestError(detail="Course update data is required in the request body.", code="MISSING_UPDATE_DATA")

    updated_course = update_course(db, db_course, course_update)
    logger.info(f"Course '{updated_course.title}' (ID: {updated_course.id}) updated by teacher {current_user.id}.")
    return updated_course


@router.delete("/{course_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_course_by_id(
        course_id: int = Path(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [Teacher] Delete a course.
    Note: This operation currently does not cascade delete associated materials and quizzes.
    """
    db_course = get_course_by_id(db, course_id=course_id)
    if not db_course:
        raise NotFoundError(detail="Course not found.")

    if current_user.role == UserRole.teacher and db_course.teacher_id != current_user.id:
        raise ForbiddenError(detail="Not enough permissions to delete this course.")

    deleted_course = delete_course(db, course_id)
    if not deleted_course:
        logger.error(f"Failed to delete course {course_id} by teacher {current_user.id}.")
        raise InternalServerError(detail="Failed to delete course.")
    logger.info(f"Course {course_id} deleted by teacher {current_user.id}.")
    return