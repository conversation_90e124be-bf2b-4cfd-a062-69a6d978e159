from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks, Query, Path, Body
from sqlalchemy.orm import Session
from app.database import get_db
from app.auth.dependencies import get_current_teacher, get_current_active_user
from app.models import User, Course, Material, MaterialType, UserRole, Class, ClassStudent
from app.schemas import PresignedUploadUrlResponse, PresignedDownloadUrlResponse, MaterialCreate, MaterialInDB, \
    MaterialUploadedConfirmation, MaterialUpdate, MaterialSearchQuery, MaterialSearchResult
from app.crud import get_material_by_id, create_material, delete_material, get_materials_by_course_id, get_course_by_id, \
    update_material, update_material_milvus_vector_id, update_material_extracted_text_content
from app.utils.minio_client import get_presigned_upload_url, get_presigned_download_url, delete_file_from_minio, \
    file_exists_in_minio, get_file_content_from_minio
from app.utils.milvus_client_utils import vectorize_text, insert_vectors_into_milvus, search_milvus
from app.utils.deepseek_client import deepseek_client
from app.exceptions import BadRequestError, NotFoundError, ForbiddenError, InternalServerError
from minio.error import S3Error
import os
import uuid
import requests
import logging
from datetime import datetime
from typing import Optional, List  # 修正：导入 List

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/materials", tags=["Materials"])


def generate_minio_object_name(course_id: int, original_filename: str, file_type: MaterialType) -> str:
    file_extension = os.path.splitext(original_filename)[1]
    unique_id = str(uuid.uuid4())
    return f"courses/{course_id}/{file_type.value}/{unique_id}{file_extension}"


def process_material_for_vectorization_async(material_id: int, file_path_minio: str, file_type: MaterialType):
    from app.database import SessionLocal
    db = SessionLocal()
    try:
        db_material = get_material_by_id(db, material_id)
        if not db_material:
            logger.warning(f"Material {material_id} not found for vectorization (might have been deleted).")
            return

        extracted_text = ""
        extracted_text = db_material.title + " " + (db_material.description or "")

        if file_type in [MaterialType.text, MaterialType.pdf, MaterialType.word, MaterialType.ppt]:
            try:
                download_url = get_presigned_download_url(file_path_minio, expiry_seconds=330)
                response = requests.get(download_url, timeout=30)
                response.raise_for_status()

                if file_type == MaterialType.text:
                    extracted_text += "\n" + response.text[:60000]
                elif file_type in [MaterialType.pdf, MaterialType.word, MaterialType.ppt]:
                    extracted_text += f"\n(Content from {file_type.value} file, requires specific parser)"

            except requests.exceptions.RequestException as e:
                logger.error(f"Failed to download or read content for material {material_id} from Minio: {e}")
            except Exception as e:
                logger.error(f"Error during text extraction for material {material_id}: {e}")

        extracted_text = extracted_text[:65000]

        if extracted_text.strip():
            vector = vectorize_text(extracted_text)
            entities_to_insert = [{
                "embedding": vector,
                "material_id": db_material.id,
                "course_id": db_material.course_id,
                "material_type": db_material.file_type.value,
                "title": db_material.title,
                "source_text": extracted_text
            }]
            milvus_pks = insert_vectors_into_milvus(entities_to_insert)

            if milvus_pks:
                update_material_milvus_vector_id(db, db_material.id, str(milvus_pks[0]))
                update_material_extracted_text_content(db, db_material.id, extracted_text)
                logger.info(f"Material {material_id} vectorized and inserted into Milvus (PK: {milvus_pks[0]}).")
            else:
                logger.error(f"Failed to insert material {material_id} into Milvus, no PK returned.")
        else:
            logger.warning(f"No meaningful text content for vectorization for material {material_id}.")

    except Exception as e:
        logger.exception(f"Critical error during vectorization for material {material_id}: {e}")
    finally:
        db.close()


@router.post("/upload-credentials", response_model=PresignedUploadUrlResponse)
async def get_upload_credentials(
        course_id: int = Query(..., gt=0),
        original_filename: str = Query(..., min_length=1, max_length=255),
        file_type: MaterialType = Query(...),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [Teacher] Get Minio presigned upload URL.
    Frontend calls this to get a URL, then directly PUTs the file to Minio.
    """
    db_course = get_course_by_id(db, course_id=course_id)
    if not db_course:
        logger.warning(f"Upload credentials request: Course {course_id} not found.")
        raise NotFoundError(detail="Course not found.")

    if db_course.teacher_id != current_user.id and current_user.role != UserRole.admin:
        logger.warning(f"Upload credentials request: User {current_user.id} not authorized for course {course_id}.")
        raise ForbiddenError(detail="Not enough permissions to upload materials for this course.")

    if not os.path.splitext(original_filename)[1]:
        raise BadRequestError(detail="Original filename must include a file extension.", code="INVALID_FILENAME")

    object_name = generate_minio_object_name(course_id, original_filename, file_type)

    try:
        upload_url = get_presigned_upload_url(object_name, expiry_seconds=3600)
        logger.info(f"Generated presigned upload URL for course {course_id}, object {object_name}.")
        return PresignedUploadUrlResponse(
            upload_url=upload_url,
            object_name=object_name,
            expires_in_seconds=3600
        )
    except S3Error as e:
        logger.error(f"Minio error generating upload URL for object {object_name}: {e}")
        raise InternalServerError(detail=f"Failed to generate upload URL: {e.message}")


@router.post("/{course_id}", response_model=MaterialInDB, status_code=status.HTTP_201_CREATED)
async def create_new_material(
        course_id: int = Path(..., gt=0),
        material_data: MaterialUploadedConfirmation = Body(...),
        *,  # Add a star to make subsequent arguments keyword-only
        background_tasks: BackgroundTasks,  # This is now a required keyword-only argument
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [Teacher] Create a teaching material record.
    This API is called after the frontend has uploaded the file to Minio.
    """
    db_course = get_course_by_id(db, course_id=course_id)
    if not db_course:
        logger.warning(f"Create material request: Course {course_id} not found.")
        raise NotFoundError(detail="Course not found.")

    if db_course.teacher_id != current_user.id and current_user.role != UserRole.admin:
        logger.warning(f"Create material request: User {current_user.id} not authorized for course {course_id}.")
        raise ForbiddenError(detail="Not enough permissions to create materials for this course.")

    try:
        if not material_data:  # 检查请求体是否为空
            raise BadRequestError(detail="Material data is required in the request body.", code="MISSING_MATERIAL_DATA")

        if not file_exists_in_minio(material_data.file_path_minio):
            logger.warning(f"Create material failed: File '{material_data.file_path_minio}' not found in Minio.")
            raise BadRequestError(
                detail="Uploaded file not found in storage. Please ensure file was uploaded successfully to Minio.",
                code="FILE_NOT_FOUND_IN_STORAGE")
    except S3Error as e:
        logger.error(f"Minio error checking file existence for '{material_data.file_path_minio}': {e}")
        raise InternalServerError(detail=f"Storage check failed: {e.message}")

    expected_path_prefix = f"courses/{course_id}/{material_data.file_type.value}/"
    if not material_data.file_path_minio.startswith(expected_path_prefix):
        logger.warning(
            f"Create material failed: Invalid Minio path format for '{material_data.file_path_minio}'. Expected prefix: '{expected_path_prefix}'")
        raise BadRequestError(
            detail="Invalid file path format in storage. Please use the path provided by upload credentials.",
            code="INVALID_STORAGE_PATH")

    db_material = create_material(
        db=db,
        material_data=MaterialCreate(**material_data.dict()),
        course_id=course_id,
        created_by_user_id=current_user.id
    )
    logger.info(f"Material '{db_material.title}' (ID: {db_material.id}) created successfully for course {course_id}.")

    background_tasks.add_task(
        process_material_for_vectorization_async,
        db_material.id,
        db_material.file_path_minio,
        db_material.file_type
    )

    return db_material


@router.get("/{material_id}/download-url", response_model=PresignedDownloadUrlResponse)
async def get_material_download_url(
        material_id: int = Path(..., gt=0),
        current_user: User = Depends(get_current_active_user),
        db: Session = Depends(get_db)
):
    """
    [Student/Teacher] Get Minio presigned download URL for a teaching material.
    """
    db_material = get_material_by_id(db, material_id=material_id)
    if not db_material:
        logger.warning(f"Download URL request: Material {material_id} not found.")
        raise NotFoundError(detail="Material not found.")

    db_course = db_material.course
    if not db_course:
        logger.error(f"Material {material_id} has no associated course. Data integrity issue.")
        raise InternalServerError(detail="Material's course not found. Data integrity issue.")

    has_access = False
    if current_user.role == UserRole.student:
        student_class_teacher_ids = db.query(Class.teacher_id).join(ClassStudent).filter(
            ClassStudent.student_id == current_user.id).distinct().all()
        student_class_teacher_ids = {t[0] for t in student_class_teacher_ids}
        if db_course.teacher_id in student_class_teacher_ids:
            has_access = True
    elif current_user.role in [UserRole.teacher, UserRole.admin]:
        if current_user.id == db_course.teacher_id or current_user.role == UserRole.admin:
            has_access = True

    if not has_access:
        logger.warning(f"Download URL request: User {current_user.id} not authorized for material {material_id}.")
        raise ForbiddenError(detail="You do not have access to download this material.")

    try:
        download_url = get_presigned_download_url(db_material.file_path_minio, expiry_seconds=3600)
        logger.info(f"Generated presigned download URL for material {material_id}.")
        return PresignedDownloadUrlResponse(
            download_url=download_url,
            expires_in_seconds=3600
        )
    except S3Error as e:
        logger.error(f"Minio error generating download URL for object {db_material.file_path_minio}: {e}")
        raise InternalServerError(detail=f"Failed to generate download URL: {e.message}")


@router.delete("/{material_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_material_by_id(
        material_id: int = Path(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [Teacher] Delete a teaching material record and its file in Minio.
    """
    db_material = get_material_by_id(db, material_id=material_id)
    if not db_material:
        logger.warning(f"Delete material failed: Material {material_id} not found.")
        raise NotFoundError(detail="Material not found.")

    if db_material.created_by_user_id != current_user.id and db_material.course.teacher_id != current_user.id and current_user.role != UserRole.admin:
        raise ForbiddenError(detail="Not enough permissions to delete this material")

    try:
        delete_file_from_minio(db_material.file_path_minio)
    except S3Error as e:
        logger.error(f"Warning: Could not delete file from Minio for material {material_id}: {e.message}")
        raise InternalServerError(detail=f"Failed to delete file from storage: {e.message}")
    except Exception as e:
        logger.error(f"Unexpected error deleting file from Minio for material {material_id}: {e}")

    deleted_material = delete_material(db, material_id)
    if not deleted_material:
        logger.error(f"Failed to delete material record {material_id} from database.")
        raise InternalServerError(detail="Failed to delete material record from database.")
    logger.info(f"Material {material_id} deleted by user {current_user.id}.")

    return


@router.get("/course/{course_id}", response_model=List[MaterialInDB])
async def get_materials_for_course(
        course_id: int = Path(..., gt=0),
        skip: int = Query(0, ge=0),
        limit: int = Query(100, ge=1, le=1000),
        current_user: User = Depends(get_current_active_user),
        db: Session = Depends(get_db)
):
    """
    [Student/Teacher] Get a list of all teaching materials for a specific course.
    """
    db_course = get_course_by_id(db, course_id=course_id)
    if not db_course:
        raise NotFoundError(detail="Course not found.")

    has_access = False
    if current_user.role == UserRole.student:
        student_class_teacher_ids = db.query(Class.teacher_id).join(ClassStudent).filter(
            ClassStudent.student_id == current_user.id).distinct().all()
        student_class_teacher_ids = {t[0] for t in student_class_teacher_ids}
        if db_course.teacher_id in student_class_teacher_ids:
            has_access = True
    elif current_user.role in [UserRole.teacher, UserRole.admin]:
        if current_user.id == db_course.teacher_id or current_user.role == UserRole.admin:
            has_access = True

    if not has_access:
        logger.warning(
            f"Get materials for course failed: User {current_user.id} not authorized for course {course_id}.")
        raise ForbiddenError(detail="You are not enrolled in this course or do not have access.")

    materials = get_materials_by_course_id(db, course_id=course_id, skip=skip, limit=limit)
    return materials


@router.put("/{material_id}", response_model=MaterialInDB)
async def update_material_info(
        material_id: int = Path(..., gt=0),  # Path parameter
        material_update: MaterialUpdate = Body(...),  # Request body parameter
        current_user: User = Depends(get_current_teacher),  # Dependency
        db: Session = Depends(get_db)  # Dependency
):
    """
    [Teacher] Update teaching material metadata (title, description, etc.).
    """
    db_material = get_material_by_id(db, material_id=material_id)
    if not db_material:
        raise NotFoundError(detail="Material not found.")

    if db_material.created_by_user_id != current_user.id and db_material.course.teacher_id != current_user.id and current_user.role != UserRole.admin:
        raise ForbiddenError(detail="Not enough permissions to update this material.")

    if not material_update:  # 检查请求体是否为空
        raise BadRequestError(detail="Material update data is required in the request body.",
                              code="MISSING_UPDATE_DATA")

    updated_material = update_material(db, db_material, material_update)
    logger.info(f"Material {material_id} updated by user {current_user.id}.")
    return updated_material


@router.post("/search", response_model=List[MaterialSearchResult])
async def search_materials(
        search_query: MaterialSearchQuery,  # Request body parameter
        current_user: User = Depends(get_current_active_user),  # Dependency
        db: Session = Depends(get_db)  # Dependency
):
    """
    [Student/Teacher] Search teaching materials by semantic query.
    """
    try:
        query_vector = vectorize_text(search_query.query_text)

        milvus_expr = None
        if search_query.course_id:
            db_course = get_course_by_id(db, course_id=search_query.course_id)
            if not db_course:
                raise NotFoundError(detail="Course not found for specified search.")

            if current_user.role == UserRole.student:
                is_student_enrolled = db.query(db.exists().where(
                    User.id == current_user.id,
                    User.student_classes.any(ClassStudent.class_.has(Class.teacher_id == db_course.teacher_id))
                )).scalar()

                if not is_student_enrolled:
                    raise ForbiddenError(detail="You do not have access to search within this course.")
            elif current_user.role == UserRole.teacher:
                if current_user.id != db_course.teacher_id and current_user.role != UserRole.admin:
                    raise ForbiddenError(detail="You do not have access to search within this course.")

            milvus_expr = f"course_id == {search_query.course_id}"

        milvus_results = search_milvus(query_vector, top_k=search_query.top_k, expr=milvus_expr)

        material_ids = [res['material_id'] for res in milvus_results]

        pg_materials = db.query(Material).filter(Material.id.in_(material_ids)).all()
        pg_materials_map = {m.id: m for m in pg_materials}

        final_results = []
        for res in milvus_results:
            pg_material = pg_materials_map.get(res['material_id'])
            if pg_material:
                final_results.append(MaterialSearchResult(
                    id=res['id'],
                    distance=res['distance'],
                    material_id=pg_material.id,
                    course_id=pg_material.course_id,
                    title=pg_material.title,
                    material_type=pg_material.file_type,
                ))
        logger.info(
            f"User {current_user.id} performed material search: '{search_query.query_text}'. Found {len(final_results)} results.")
        return final_results

    except Exception as e:
        logger.exception(f"Error during material search for user {current_user.id}: {e}")
        raise InternalServerError(detail="An error occurred during search.")