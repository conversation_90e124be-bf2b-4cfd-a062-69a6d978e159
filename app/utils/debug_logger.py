import logging
import time
from datetime import datetime
from typing import Dict, Any
import json

# Configure logger
logger = logging.getLogger(__name__)

# Simple in-memory storage for API calls
api_calls_log = []
MAX_LOGS = 100

def log_api_call(method: str, path: str, status_code: int = None, 
                 process_time: float = None, client_ip: str = None, 
                 request_data: Dict[str, Any] = None, error: str = None):
    """Log API call details"""
    
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # Create log entry
    log_entry = {
        'timestamp': timestamp,
        'method': method,
        'path': path,
        'client_ip': client_ip or 'unknown',
        'status_code': status_code,
        'process_time': process_time,
        'request_data': request_data,
        'error': error
    }
    
    # Add to in-memory storage
    api_calls_log.append(log_entry)
    
    # Keep only last MAX_LOGS entries
    if len(api_calls_log) > MAX_LOGS:
        api_calls_log.pop(0)
    
    # Log to console
    if status_code:
        # Response log
        if status_code < 300:
            status_emoji = "✅"
        elif status_code < 400:
            status_emoji = "🔄"
        elif status_code < 500:
            status_emoji = "⚠️"
        else:
            status_emoji = "❌"
        
        time_str = f"({process_time:.3f}s)" if process_time else ""
        logger.info(f"{status_emoji} {status_code} {method} {path} {time_str}")
        
        if error:
            logger.error(f"   🚨 Error: {error}")
    else:
        # Request log
        logger.info(f"🔵 {method} {path} from {client_ip}")
        
        if request_data:
            # Hide sensitive data
            safe_data = dict(request_data)
            if 'password' in safe_data:
                safe_data['password'] = '***HIDDEN***'
            logger.info(f"   📄 Request data: {safe_data}")

def get_api_logs():
    """Get all API logs"""
    return list(reversed(api_calls_log))  # Most recent first

def clear_api_logs():
    """Clear all API logs"""
    api_calls_log.clear()

def get_api_stats():
    """Get API call statistics"""
    total_calls = len(api_calls_log)
    
    if total_calls == 0:
        return {
            'total_calls': 0,
            'success_calls': 0,
            'error_calls': 0,
            'avg_response_time': 0
        }
    
    success_calls = len([log for log in api_calls_log if log.get('status_code', 0) < 400])
    error_calls = len([log for log in api_calls_log if log.get('status_code', 0) >= 400])
    
    # Calculate average response time
    response_times = [log.get('process_time', 0) for log in api_calls_log if log.get('process_time')]
    avg_response_time = sum(response_times) / len(response_times) if response_times else 0
    
    return {
        'total_calls': total_calls,
        'success_calls': success_calls,
        'error_calls': error_calls,
        'avg_response_time': round(avg_response_time, 3)
    }

# Decorator for automatic API logging
def log_api_endpoint(func):
    """Decorator to automatically log API endpoints"""
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        
        # Try to get request info
        request = None
        for arg in args:
            if hasattr(arg, 'method') and hasattr(arg, 'url'):
                request = arg
                break
        
        method = request.method if request else 'UNKNOWN'
        path = request.url.path if request else func.__name__
        client_ip = request.client.host if request and request.client else 'unknown'
        
        # Log request
        log_api_call(method, path, client_ip=client_ip)
        
        try:
            # Execute function
            result = await func(*args, **kwargs)
            
            # Log successful response
            process_time = time.time() - start_time
            status_code = getattr(result, 'status_code', 200)
            log_api_call(method, path, status_code=status_code, 
                        process_time=process_time, client_ip=client_ip)
            
            return result
            
        except Exception as e:
            # Log error response
            process_time = time.time() - start_time
            log_api_call(method, path, status_code=500, 
                        process_time=process_time, client_ip=client_ip, 
                        error=str(e))
            raise
    
    return wrapper
