# Stub implementation for development without Minio
import logging

logger = logging.getLogger(__name__)

# Mock Minio client for development
class MockMinioClient:
    def bucket_exists(self, bucket_name):
        return True

    def make_bucket(self, bucket_name):
        pass

    def presigned_put_object(self, bucket_name, object_name, expires):
        return f"http://localhost:9000/{bucket_name}/{object_name}?upload=true"

    def presigned_get_object(self, bucket_name, object_name, expires):
        return f"http://localhost:9000/{bucket_name}/{object_name}"

    def remove_object(self, bucket_name, object_name):
        pass

    def stat_object(self, bucket_name, object_name):
        return {"size": 1024}

minio_client = MockMinioClient()

def ensure_bucket_exists():
    """Ensure Minio bucket exists, create if not."""
    logger.info("Mock Minio: Bucket exists check passed.")

def get_presigned_upload_url(object_name: str, expiry_seconds: int = 3600) -> str:
    """Generate a presigned URL for clients to directly upload files to <PERSON><PERSON>."""
    logger.info(f"Mock Minio: Generated upload URL for {object_name}")
    return f"http://localhost:9000/mock-bucket/{object_name}?upload=true"

def get_presigned_download_url(object_name: str, expiry_seconds: int = 3600) -> str:
    """Generate a presigned URL for clients to directly download files from Minio."""
    logger.info(f"Mock Minio: Generated download URL for {object_name}")
    return f"http://localhost:9000/mock-bucket/{object_name}"

def delete_file_from_minio(object_name: str):
    """Delete a file from Minio."""
    logger.info(f"Mock Minio: Deleted {object_name}")

def file_exists_in_minio(object_name: str) -> bool:
    """Check if a file exists in Minio."""
    logger.info(f"Mock Minio: File {object_name} exists check")
    return True

def get_file_content_from_minio(object_name: str) -> bytes:
    """Get file content from Minio as bytes."""
    logger.info(f"Mock Minio: Getting content for {object_name}")
    # Return mock content for testing
    return b"Mock file content for testing DeepSeek integration"
