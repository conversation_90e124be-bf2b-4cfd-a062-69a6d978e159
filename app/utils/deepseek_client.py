"""
DeepSeek AI client for document analysis and content extraction
"""
import requests
import json
import logging
from typing import Dict, Any, Optional
import base64
import mimetypes
from io import BytesIO
import PyPDF2
import docx
from PIL import Image
import pytesseract

logger = logging.getLogger(__name__)

class DeepSeekClient:
    def __init__(self, api_key: str = "sk-9c664d43330f4265b207747b26de8f38"):
        self.api_key = api_key
        self.base_url = "https://api.deepseek.com/v1"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def analyze_document(self, file_content: bytes, file_name: str, file_type: str) -> Dict[str, Any]:
        """
        Analyze document content using DeepSeek AI
        
        Args:
            file_content: Raw file content as bytes
            file_name: Original filename
            file_type: MIME type or file extension
            
        Returns:
            Dictionary containing analysis results
        """
        try:
            # Extract text content based on file type
            extracted_text = self._extract_text_from_file(file_content, file_name, file_type)
            
            if not extracted_text:
                return {
                    "success": False,
                    "error": "Could not extract text from file",
                    "extracted_text": "",
                    "summary": "",
                    "key_points": [],
                    "topics": [],
                    "difficulty_level": "unknown"
                }
            
            # Analyze content with DeepSeek
            analysis_result = self._analyze_text_with_deepseek(extracted_text, file_name)
            
            return {
                "success": True,
                "extracted_text": extracted_text[:2000],  # Limit for storage
                "summary": analysis_result.get("summary", ""),
                "key_points": analysis_result.get("key_points", []),
                "topics": analysis_result.get("topics", []),
                "difficulty_level": analysis_result.get("difficulty_level", "medium"),
                "suggested_questions": analysis_result.get("suggested_questions", []),
                "learning_objectives": analysis_result.get("learning_objectives", [])
            }
            
        except Exception as e:
            logger.error(f"Error analyzing document {file_name}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "extracted_text": "",
                "summary": "",
                "key_points": [],
                "topics": [],
                "difficulty_level": "unknown"
            }
    
    def _extract_text_from_file(self, file_content: bytes, file_name: str, file_type: str) -> str:
        """Extract text content from various file types"""
        try:
            file_extension = file_name.lower().split('.')[-1] if '.' in file_name else ''
            
            # PDF files
            if file_type == 'pdf' or file_extension == 'pdf' or 'pdf' in file_type.lower():
                return self._extract_text_from_pdf(file_content)
            
            # Word documents
            elif file_type == 'word' or file_extension in ['doc', 'docx'] or 'word' in file_type.lower():
                return self._extract_text_from_word(file_content)
            
            # Text files
            elif file_type == 'text' or file_extension == 'txt' or 'text' in file_type.lower():
                return file_content.decode('utf-8', errors='ignore')
            
            # Image files (OCR)
            elif file_type == 'image' or file_extension in ['jpg', 'jpeg', 'png', 'bmp'] or 'image' in file_type.lower():
                return self._extract_text_from_image(file_content)
            
            # PowerPoint files (basic text extraction)
            elif file_type == 'ppt' or file_extension in ['ppt', 'pptx'] or 'powerpoint' in file_type.lower():
                return "PowerPoint file detected. Manual text extraction required."
            
            else:
                # Try to decode as text
                return file_content.decode('utf-8', errors='ignore')
                
        except Exception as e:
            logger.error(f"Error extracting text from {file_name}: {str(e)}")
            return ""
    
    def _extract_text_from_pdf(self, file_content: bytes) -> str:
        """Extract text from PDF file"""
        try:
            pdf_file = BytesIO(file_content)
            pdf_reader = PyPDF2.PdfReader(pdf_file)
            text = ""
            
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
            
            return text.strip()
        except Exception as e:
            logger.error(f"Error extracting text from PDF: {str(e)}")
            return ""
    
    def _extract_text_from_word(self, file_content: bytes) -> str:
        """Extract text from Word document"""
        try:
            doc_file = BytesIO(file_content)
            doc = docx.Document(doc_file)
            text = ""
            
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            
            return text.strip()
        except Exception as e:
            logger.error(f"Error extracting text from Word document: {str(e)}")
            return ""
    
    def _extract_text_from_image(self, file_content: bytes) -> str:
        """Extract text from image using OCR"""
        try:
            image = Image.open(BytesIO(file_content))
            text = pytesseract.image_to_string(image, lang='chi_sim+eng')
            return text.strip()
        except Exception as e:
            logger.error(f"Error extracting text from image: {str(e)}")
            return ""
    
    def _analyze_text_with_deepseek(self, text: str, file_name: str) -> Dict[str, Any]:
        """Analyze extracted text using DeepSeek AI"""
        try:
            # Limit text length for API call
            if len(text) > 8000:
                text = text[:8000] + "..."
            
            prompt = f"""
请分析以下教学文档内容，并提供详细的教学分析：

文档名称：{file_name}
文档内容：
{text}

请按照以下格式返回JSON分析结果：
{{
    "summary": "文档内容的简要总结（100-200字）",
    "key_points": ["关键知识点1", "关键知识点2", "关键知识点3"],
    "topics": ["主题1", "主题2", "主题3"],
    "difficulty_level": "beginner/intermediate/advanced",
    "suggested_questions": ["建议的测试问题1", "建议的测试问题2", "建议的测试问题3"],
    "learning_objectives": ["学习目标1", "学习目标2", "学习目标3"]
}}

请确保返回有效的JSON格式。
"""
            
            payload = {
                "model": "deepseek-chat",
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.3,
                "max_tokens": 2000
            }
            
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                # Try to parse JSON from the response
                try:
                    # Extract JSON from the response (in case there's extra text)
                    start_idx = content.find('{')
                    end_idx = content.rfind('}') + 1
                    if start_idx != -1 and end_idx != -1:
                        json_str = content[start_idx:end_idx]
                        analysis_result = json.loads(json_str)
                        return analysis_result
                    else:
                        raise ValueError("No JSON found in response")
                        
                except json.JSONDecodeError:
                    # Fallback: create structured response from text
                    return self._create_fallback_analysis(content, file_name)
            else:
                logger.error(f"DeepSeek API error: {response.status_code} - {response.text}")
                return self._create_fallback_analysis(text[:500], file_name)
                
        except Exception as e:
            logger.error(f"Error calling DeepSeek API: {str(e)}")
            return self._create_fallback_analysis(text[:500], file_name)
    
    def _create_fallback_analysis(self, text: str, file_name: str) -> Dict[str, Any]:
        """Create a basic analysis when AI analysis fails"""
        words = text.split()
        word_count = len(words)
        
        # Simple heuristics for difficulty level
        if word_count < 100:
            difficulty = "beginner"
        elif word_count < 500:
            difficulty = "intermediate"
        else:
            difficulty = "advanced"
        
        return {
            "summary": f"文档 {file_name} 包含约 {word_count} 个词，内容涉及教学材料。",
            "key_points": ["需要进一步分析", "包含教学内容", "可用于课程学习"],
            "topics": ["教学材料", "课程内容"],
            "difficulty_level": difficulty,
            "suggested_questions": ["这个文档的主要内容是什么？", "学习这个材料需要什么前置知识？"],
            "learning_objectives": ["理解文档内容", "掌握相关知识点"]
        }

# Global instance
deepseek_client = DeepSeekClient()
