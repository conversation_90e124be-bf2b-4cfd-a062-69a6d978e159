import requests

def check_student_class_membership():
    """Check if test student is in any class"""
    api_url = "http://localhost:8000/api/v1"
    
    # Login as student
    login_response = requests.post(f"{api_url}/auth/token", 
                                 json={"username": "testuser", "password": "testpass123"},
                                 timeout=10)
    
    if login_response.status_code != 200:
        print("❌ 学生登录失败")
        return
    
    student_token = login_response.json()["access_token"]
    student_headers = {"Authorization": f"Bearer {student_token}"}
    
    # Try to get student's classes (this endpoint might not exist)
    print("🔍 检查学生班级成员关系...")
    
    # Login as teacher to check classes
    teacher_login = requests.post(f"{api_url}/auth/token", 
                                json={"username": "teacher_zhang", "password": "teacher123456"},
                                timeout=10)
    
    if teacher_login.status_code != 200:
        print("❌ 教师登录失败")
        return
    
    teacher_token = teacher_login.json()["access_token"]
    teacher_headers = {"Authorization": f"Bearer {teacher_token}"}
    
    # Get all classes
    classes_response = requests.get(f"{api_url}/classes/", headers=teacher_headers, timeout=10)
    if classes_response.status_code == 200:
        classes = classes_response.json()
        print(f"✅ 找到 {len(classes)} 个班级")
        
        # Check each class for students
        for class_item in classes:
            students_response = requests.get(f"{api_url}/classes/{class_item['id']}/students", 
                                           headers=teacher_headers, timeout=10)
            if students_response.status_code == 200:
                students = students_response.json()
                print(f"📚 班级 '{class_item['name']}' 有 {len(students)} 个学生")
                
                # Check if testuser is in this class
                testuser_in_class = any(s['username'] == 'testuser' for s in students)
                if testuser_in_class:
                    print(f"✅ 学生 'testuser' 在班级 '{class_item['name']}' 中")
                    return True
                else:
                    print(f"ℹ️ 学生 'testuser' 不在班级 '{class_item['name']}' 中")
    
    print("❌ 学生 'testuser' 不在任何班级中")
    return False

def add_student_to_class():
    """Add test student to a class"""
    api_url = "http://localhost:8000/api/v1"
    
    # Login as teacher
    teacher_login = requests.post(f"{api_url}/auth/token", 
                                json={"username": "teacher_zhang", "password": "teacher123456"},
                                timeout=10)
    
    if teacher_login.status_code != 200:
        print("❌ 教师登录失败")
        return False
    
    teacher_token = teacher_login.json()["access_token"]
    teacher_headers = {"Authorization": f"Bearer {teacher_token}"}
    
    # Get classes
    classes_response = requests.get(f"{api_url}/classes/", headers=teacher_headers, timeout=10)
    if classes_response.status_code != 200:
        print("❌ 获取班级失败")
        return False
    
    classes = classes_response.json()
    if not classes:
        print("❌ 没有可用的班级")
        return False
    
    # Add student to first class
    first_class = classes[0]
    print(f"📝 尝试将学生添加到班级 '{first_class['name']}'...")
    
    # Get student ID first
    # We need to find the student user ID
    # Let's try to add by username (if the API supports it)
    
    add_response = requests.post(f"{api_url}/classes/{first_class['id']}/students",
                               json={"student_username": "testuser"},
                               headers=teacher_headers, timeout=10)
    
    if add_response.status_code in [200, 201]:
        print(f"✅ 成功将学生添加到班级 '{first_class['name']}'")
        return True
    else:
        print(f"❌ 添加学生到班级失败: {add_response.status_code}")
        try:
            error_detail = add_response.json()
            print(f"错误详情: {error_detail}")
        except:
            print(f"响应内容: {add_response.text}")
        return False

if __name__ == "__main__":
    print("🔍 检查学生班级成员关系")
    print("=" * 50)
    
    # Check if student is in any class
    is_in_class = check_student_class_membership()
    
    if not is_in_class:
        print("\n📝 尝试将学生添加到班级...")
        success = add_student_to_class()
        
        if success:
            print("\n🔍 重新检查学生班级成员关系...")
            check_student_class_membership()
    
    print("\n" + "=" * 50)
    print("🎯 检查完成！")
