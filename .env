# Database Configuration
# DATABASE_URL="postgresql+psycopg2://postgres:postgres@localhost/your_database"
DATABASE_URL="sqlite:///./education_platform.db"

# JWT Secret Key (MUST be a long, random string in production)
SECRET_KEY="your-super-secret-key-for-jwt-and-more-a-very-long-random-string-for-production-environment-security-purposes"
ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRE_MINUTES="30"

# Minio Configuration
MINIO_ENDPOINT="************:9000"
MINIO_ACCESS_KEY="minioadmin"
MINIO_SECRET_KEY="minioadmin"
MINIO_BUCKET_NAME="shengshuo"
MINIO_SECURE="False" # Set to "True" if using HTTPS with Minio

# Milvus Configuration
MILVUS_URI="http://************:19530" # Or your remote Milvus instance
EMBEDDING_DIM="384" # Default for 'all-MiniLM-L6-v2' model

# Logging Configuration
LOG_LEVEL="INFO" # DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_FILE_PATH="./logs/app.log"
LOG_MAX_BYTES="10485760" # 10 MB
LOG_BACKUP_COUNT="5" # Keep 5 backup files

# AGORA RELATED CONFIG REMOVED
# AGORA_APP_ID="YOUR_AGORA_APP_ID"
# AGORA_APP_CERTIFICATE="YOUR_AGORA_APP_CERTIFICATE"
# AGORA_TOKEN_EXPIRE_SECONDS="3600"