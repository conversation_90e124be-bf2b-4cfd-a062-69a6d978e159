"""
Initialize users in the database
This script creates admin and student users for testing
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from app.database import SessionLocal, engine
from app.models import User, Class, Course
from app.auth.password import get_password_hash
from app import models

def print_header(title):
    print("\n" + "="*70)
    print(f"🎓 {title}")
    print("="*70)

def print_success(message):
    print(f"✅ {message}")

def print_error(message):
    print(f"❌ {message}")

def print_info(message):
    print(f"ℹ️ {message}")

def create_tables():
    """Create all database tables"""
    print_header("创建数据库表")
    try:
        models.Base.metadata.create_all(bind=engine)
        print_success("数据库表创建成功")
        return True
    except Exception as e:
        print_error(f"创建数据库表失败: {e}")
        return False

def create_admin_user(db: Session):
    """Create admin user"""
    print_header("创建管理员用户")
    
    # Check if admin already exists
    existing_admin = db.query(User).filter(User.username == "admin").first()
    if existing_admin:
        print_info("管理员用户已存在")
        return existing_admin
    
    try:
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            full_name="系统管理员",
            hashed_password=get_password_hash("admin123456"),
            role="admin",
            is_active=True
        )
        
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        print_success(f"管理员用户创建成功: {admin_user.username} (ID: {admin_user.id})")
        return admin_user
    except Exception as e:
        print_error(f"创建管理员用户失败: {e}")
        db.rollback()
        return None

def create_teacher_users(db: Session):
    """Create teacher users"""
    print_header("创建教师用户")
    
    teachers = [
        {
            "username": "teacher_zhang",
            "email": "<EMAIL>",
            "full_name": "张老师",
            "password": "teacher123456"
        },
        {
            "username": "teacher_li",
            "email": "<EMAIL>", 
            "full_name": "李老师",
            "password": "teacher123456"
        }
    ]
    
    created_teachers = []
    
    for teacher_data in teachers:
        # Check if teacher already exists
        existing_teacher = db.query(User).filter(User.username == teacher_data["username"]).first()
        if existing_teacher:
            print_info(f"教师用户已存在: {teacher_data['username']}")
            created_teachers.append(existing_teacher)
            continue
        
        try:
            teacher_user = User(
                username=teacher_data["username"],
                email=teacher_data["email"],
                full_name=teacher_data["full_name"],
                hashed_password=get_password_hash(teacher_data["password"]),
                role="teacher",
                is_active=True
            )
            
            db.add(teacher_user)
            db.commit()
            db.refresh(teacher_user)
            
            created_teachers.append(teacher_user)
            print_success(f"教师用户创建成功: {teacher_user.username} (ID: {teacher_user.id})")
        except Exception as e:
            print_error(f"创建教师用户失败: {e}")
            db.rollback()
    
    return created_teachers

def create_student_users(db: Session):
    """Create student users"""
    print_header("创建学生用户")
    
    students = [
        {
            "username": "testuser",
            "email": "<EMAIL>",
            "full_name": "测试学生",
            "password": "testpass123"
        },
        {
            "username": "student001",
            "email": "<EMAIL>",
            "full_name": "张三",
            "password": "student123"
        },
        {
            "username": "student002",
            "email": "<EMAIL>",
            "full_name": "李四", 
            "password": "student123"
        },
        {
            "username": "student003",
            "email": "<EMAIL>",
            "full_name": "王五",
            "password": "student123"
        },
        {
            "username": "student004",
            "email": "<EMAIL>",
            "full_name": "赵六",
            "password": "student123"
        }
    ]
    
    created_students = []
    
    for student_data in students:
        # Check if student already exists
        existing_student = db.query(User).filter(User.username == student_data["username"]).first()
        if existing_student:
            print_info(f"学生用户已存在: {student_data['username']}")
            created_students.append(existing_student)
            continue
        
        try:
            student_user = User(
                username=student_data["username"],
                email=student_data["email"],
                full_name=student_data["full_name"],
                hashed_password=get_password_hash(student_data["password"]),
                role="student",
                is_active=True
            )
            
            db.add(student_user)
            db.commit()
            db.refresh(student_user)
            
            created_students.append(student_user)
            print_success(f"学生用户创建成功: {student_user.username} (ID: {student_user.id})")
        except Exception as e:
            print_error(f"创建学生用户失败: {e}")
            db.rollback()
    
    return created_students

def create_sample_courses(db: Session, teacher_user):
    """Create sample courses"""
    print_header("创建示例课程")
    
    courses_data = [
        {
            "title": "Python基础编程",
            "description": "学习Python编程语言的基础知识，包括语法、数据类型、控制结构等。",
            "category": "编程"
        },
        {
            "title": "Web开发入门",
            "description": "学习HTML、CSS、JavaScript等前端技术，以及后端开发基础。",
            "category": "Web开发"
        },
        {
            "title": "数据分析基础",
            "description": "使用Python进行数据分析，学习pandas、numpy等库的使用。",
            "category": "数据科学"
        }
    ]
    
    created_courses = []
    
    for course_data in courses_data:
        # Check if course already exists
        existing_course = db.query(Course).filter(Course.title == course_data["title"]).first()
        if existing_course:
            print_info(f"课程已存在: {course_data['title']}")
            created_courses.append(existing_course)
            continue
        
        try:
            course = Course(
                title=course_data["title"],
                description=course_data["description"],
                category=course_data["category"],
                teacher_id=teacher_user.id,
                is_active=True
            )
            
            db.add(course)
            db.commit()
            db.refresh(course)
            
            created_courses.append(course)
            print_success(f"课程创建成功: {course.title} (ID: {course.id})")
        except Exception as e:
            print_error(f"创建课程失败: {e}")
            db.rollback()
    
    return created_courses

def create_sample_classes(db: Session, teacher_user):
    """Create sample classes"""
    print_header("创建示例班级")
    
    classes_data = [
        {
            "name": "Python编程班级A",
            "description": "Python编程初级班，适合零基础学员"
        },
        {
            "name": "Python初级班",
            "description": "Python编程基础班级"
        },
        {
            "name": "Web开发进阶班",
            "description": "Web开发进阶班级，需要一定基础"
        }
    ]
    
    created_classes = []
    
    for class_data in classes_data:
        # Check if class already exists
        existing_class = db.query(Class).filter(Class.name == class_data["name"]).first()
        if existing_class:
            print_info(f"班级已存在: {class_data['name']}")
            created_classes.append(existing_class)
            continue
        
        try:
            class_obj = Class(
                name=class_data["name"],
                description=class_data["description"],
                teacher_id=teacher_user.id,
                is_active=True
            )
            
            db.add(class_obj)
            db.commit()
            db.refresh(class_obj)
            
            created_classes.append(class_obj)
            print_success(f"班级创建成功: {class_obj.name} (ID: {class_obj.id})")
        except Exception as e:
            print_error(f"创建班级失败: {e}")
            db.rollback()
    
    return created_classes

def main():
    print("🎓 初始化用户和数据")
    print("=" * 80)
    
    print_info("此脚本将创建管理员、教师、学生用户以及示例课程和班级")
    
    # Create tables
    if not create_tables():
        print_error("数据库表创建失败，停止初始化")
        return False
    
    # Get database session
    db = SessionLocal()
    
    try:
        # Create admin user
        admin_user = create_admin_user(db)
        
        # Create teacher users
        teacher_users = create_teacher_users(db)
        
        # Create student users
        student_users = create_student_users(db)
        
        # Create sample courses and classes using first teacher
        if teacher_users:
            main_teacher = teacher_users[0]
            courses = create_sample_courses(db, main_teacher)
            classes = create_sample_classes(db, main_teacher)
        
        # Generate report
        print_header("初始化完成报告")
        
        print(f"📊 创建结果:")
        print(f"   👑 管理员用户: {'✅ 已创建' if admin_user else '❌ 创建失败'}")
        print(f"   👨‍🏫 教师用户: {len(teacher_users)} 个")
        print(f"   👨‍🎓 学生用户: {len(student_users)} 个")
        print(f"   📚 示例课程: {len(courses) if teacher_users else 0} 个")
        print(f"   🏫 示例班级: {len(classes) if teacher_users else 0} 个")
        
        if student_users:
            print("\n👥 可用的学生账号:")
            for student in student_users:
                print_info(f"   用户名: {student.username} (ID: {student.id}) - 密码: student123 或 testpass123")
            
            print("\n🎯 测试指南:")
            print("   1. 访问: http://localhost:3001/teacher/classes")
            print("   2. 登录教师账号: teacher_zhang / teacher123456")
            print("   3. 在'添加学生到班级'部分:")
            print("      - 选择一个班级")
            print(f"      - 输入学生ID: {student_users[0].id}")
            print("   4. 观察'添加学生'按钮是否变为可用")
            
            print("\n✅ 初始化成功！'添加学生'按钮问题应该已解决！")
        else:
            print("\n❌ 学生用户创建失败")
        
        return len(student_users) > 0
        
    except Exception as e:
        print_error(f"初始化过程异常: {e}")
        return False
    finally:
        db.close()

if __name__ == "__main__":
    success = main()
    print("\n" + "=" * 80)
    print("🎯 用户初始化完成！")
    print("=" * 80)
    
    if success:
        print("\n🎊 恭喜！所有用户已创建，可以开始测试！")
    else:
        print("\n⚠️ 初始化过程中遇到问题")
