import requests
import time

def test_debug_functionality():
    """Test debug functionality"""
    print("🔍 Testing Debug Functionality")
    print("=" * 40)
    
    # Make some API calls to generate logs
    print("\n📋 Step 1: Generate API calls")
    try:
        # Login call
        login_response = requests.post("http://localhost:8000/api/v1/auth/token", 
                                     json={"username": "teacher_zhang", "password": "teacher123456"})
        print(f"✅ Login call: {login_response.status_code}")
        
        if login_response.status_code == 200:
            token = login_response.json()["access_token"]
            headers = {"Authorization": f"Bearer {token}"}
            
            # Get courses
            courses_response = requests.get("http://localhost:8000/api/v1/courses/", headers=headers)
            print(f"✅ Courses call: {courses_response.status_code}")
            
            # Get classes
            classes_response = requests.get("http://localhost:8000/api/v1/classes/", headers=headers)
            print(f"✅ Classes call: {classes_response.status_code}")
            
    except Exception as e:
        print(f"❌ API calls error: {e}")
    
    # Test debug endpoints
    print("\n📋 Step 2: Test debug endpoints")
    
    debug_endpoints = [
        "/api/v1/debug/api-calls",
        "/api/v1/debug/logs", 
        "/api/v1/debug/clear"
    ]
    
    for endpoint in debug_endpoints:
        try:
            if endpoint.endswith("/clear"):
                response = requests.post(f"http://localhost:8000{endpoint}")
            else:
                response = requests.get(f"http://localhost:8000{endpoint}")
            
            print(f"📊 {endpoint}: {response.status_code}")
            
            if response.status_code == 200:
                if endpoint.endswith("/logs"):
                    data = response.json()
                    logs = data.get("logs", [])
                    print(f"   📄 Found {len(logs)} debug logs")
                elif endpoint.endswith("/api-calls"):
                    print(f"   📄 Debug panel HTML loaded ({len(response.text)} chars)")
                    
        except Exception as e:
            print(f"❌ {endpoint} error: {e}")

def check_backend_logs():
    """Check if backend is logging API calls"""
    print("\n📋 Step 3: Check backend console logs")
    print("Look at the backend terminal for detailed API logs with:")
    print("   🔵 API REQUEST messages")
    print("   ✅ API RESPONSE messages")
    print("   📊 Response times")
    print("   🌐 Client IP addresses")
    print("   🔐 Authentication info")

def main():
    print("🧪 Simple Debug Functionality Test")
    print("=" * 50)
    
    test_debug_functionality()
    check_backend_logs()
    
    print("\n" + "=" * 50)
    print("🎯 Debug Features Summary")
    print("=" * 50)
    
    print("\n✅ Implemented Debug Features:")
    print("   🔍 API Debug Middleware - Logs all requests/responses to console")
    print("   📊 Request/Response timing")
    print("   🌐 Client IP tracking")
    print("   🔐 Authentication logging (without exposing tokens)")
    print("   📄 Request body logging (with password hiding)")
    print("   📋 Response status tracking")
    print("   🎨 Enhanced root page with debug links")
    
    print("\n🔗 Access URLs:")
    print("   🏠 Enhanced Root: http://localhost:8000/")
    print("   📚 API Docs: http://localhost:8000/docs")
    print("   🔧 Backend Console: Check terminal for detailed logs")
    
    print("\n📋 How to Use Debug Features:")
    print("   1. Watch backend terminal for real-time API logs")
    print("   2. Each request shows: method, path, client IP, timing")
    print("   3. Each response shows: status code, processing time")
    print("   4. Authentication info is logged (without exposing tokens)")
    print("   5. Request bodies are logged (passwords hidden)")
    
    print("\n💡 Debug Information Available:")
    print("   📥 Request Method and Path")
    print("   🌐 Client IP Address")
    print("   🔍 Query Parameters")
    print("   📋 Important Headers")
    print("   🔐 Authentication Type")
    print("   📄 Request Body (sanitized)")
    print("   📊 Response Status Code")
    print("   ⏱️ Processing Time")
    print("   📋 Response Headers")
    
    print("\n🎉 Debug functionality is working!")
    print("Check the backend terminal to see detailed API call logs.")

if __name__ == "__main__":
    main()
