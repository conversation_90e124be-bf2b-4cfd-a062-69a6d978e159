import requests

def add_student_to_class():
    """Add test student to a class"""
    api_url = "http://localhost:8000/api/v1"
    
    # Login as teacher
    teacher_login = requests.post(f"{api_url}/auth/token", 
                                json={"username": "teacher_zhang", "password": "teacher123456"},
                                timeout=10)
    
    if teacher_login.status_code != 200:
        print("❌ 教师登录失败")
        return False
    
    teacher_token = teacher_login.json()["access_token"]
    teacher_headers = {"Authorization": f"Bearer {teacher_token}"}
    
    # Get all users to find testuser ID
    print("🔍 查找学生用户ID...")
    
    # We need to find the testuser ID. Let's try to get it from the database directly
    # Since we don't have a direct API to get user by username, let's use a workaround
    
    # Login as testuser to get their info
    student_login = requests.post(f"{api_url}/auth/token", 
                                json={"username": "testuser", "password": "testpass123"},
                                timeout=10)
    
    if student_login.status_code != 200:
        print("❌ 学生登录失败")
        return False
    
    student_token = student_login.json()["access_token"]
    student_headers = {"Authorization": f"Bearer {student_token}"}
    
    # Get student profile to get their ID
    profile_response = requests.get(f"{api_url}/auth/me", headers=student_headers, timeout=10)
    
    if profile_response.status_code != 200:
        print("❌ 获取学生信息失败")
        return False
    
    student_info = profile_response.json()
    student_id = student_info['id']
    print(f"✅ 找到学生ID: {student_id} (用户名: {student_info['username']})")
    
    # Get classes
    classes_response = requests.get(f"{api_url}/classes/", headers=teacher_headers, timeout=10)
    if classes_response.status_code != 200:
        print("❌ 获取班级失败")
        return False
    
    classes = classes_response.json()
    if not classes:
        print("❌ 没有可用的班级")
        return False
    
    # Add student to first class
    first_class = classes[0]
    print(f"📝 尝试将学生添加到班级 '{first_class['name']}'...")
    
    add_response = requests.post(f"{api_url}/classes/{first_class['id']}/students",
                               json={"student_ids": [student_id]},
                               headers=teacher_headers, timeout=10)
    
    if add_response.status_code in [200, 201]:
        print(f"✅ 成功将学生添加到班级 '{first_class['name']}'")
        
        # Verify by getting students in class
        students_response = requests.get(f"{api_url}/classes/{first_class['id']}/students", 
                                       headers=teacher_headers, timeout=10)
        if students_response.status_code == 200:
            students = students_response.json()
            print(f"✅ 验证成功：班级现在有 {len(students)} 个学生")
            for student in students:
                print(f"   👤 {student['username']} (ID: {student['id']})")
        
        return True
    else:
        print(f"❌ 添加学生到班级失败: {add_response.status_code}")
        try:
            error_detail = add_response.json()
            print(f"错误详情: {error_detail}")
        except:
            print(f"响应内容: {add_response.text}")
        return False

if __name__ == "__main__":
    print("📝 将学生添加到班级")
    print("=" * 40)
    
    success = add_student_to_class()
    
    if success:
        print("\n🎉 学生成功添加到班级！")
        print("现在可以重新运行课程班级关联测试了。")
    else:
        print("\n❌ 添加学生到班级失败")
    
    print("\n" + "=" * 40)
    print("🎯 操作完成！")
