import requests
import time

def test_login():
    """Quick login test"""
    print("🔍 快速登录测试")
    print("=" * 40)
    
    api_url = "http://localhost:8000/api/v1"
    
    # Test teacher login
    print("📋 测试教师登录...")
    try:
        response = requests.post(f"{api_url}/auth/token", 
                               json={"username": "teacher_zhang", "password": "teacher123456"},
                               timeout=30)  # Increased timeout
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 教师登录成功！")
            print(f"Token: {result['access_token'][:50]}...")
            return True
        else:
            print(f"❌ 教师登录失败: {response.json()}")
            return False
            
    except Exception as e:
        print(f"❌ 登录测试错误: {e}")
        return False

def test_frontend_access():
    """Test frontend access"""
    print("\n📋 测试前端访问...")
    try:
        response = requests.get("http://localhost:3003", timeout=10)
        if response.status_code == 200:
            print("✅ 前端应用可访问")
            return True
        else:
            print(f"❌ 前端应用异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 前端访问错误: {e}")
        return False

def main():
    print("🚀 快速系统测试")
    print("=" * 50)
    
    # Test login
    login_success = test_login()
    
    # Test frontend
    frontend_success = test_frontend_access()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"   🔐 登录功能: {'✅ 正常' if login_success else '❌ 异常'}")
    print(f"   🎨 前端应用: {'✅ 正常' if frontend_success else '❌ 异常'}")
    
    if login_success and frontend_success:
        print("\n🎉 系统基本功能正常！")
        print("\n🔗 访问地址:")
        print("   🎨 前端应用: http://localhost:3003")
        print("   🔧 后端API: http://localhost:8000")
        print("   📚 API文档: http://localhost:8000/docs")
        print("   🧪 前端调试: http://localhost:3003/debug")
        
        print("\n👥 登录账号:")
        print("   👨‍🏫 教师: teacher_zhang / teacher123456")
        print("   👨‍🎓 学生: testuser / testpass123")
        
        print("\n📋 登录问题解决方案:")
        print("   1. 确保使用正确的URL: http://localhost:3003")
        print("   2. 使用正确的账号密码")
        print("   3. 检查浏览器控制台错误信息")
        print("   4. 访问调试页面: http://localhost:3003/debug")
        
    else:
        print("\n⚠️ 系统存在问题，需要进一步检查")

if __name__ == "__main__":
    main()
