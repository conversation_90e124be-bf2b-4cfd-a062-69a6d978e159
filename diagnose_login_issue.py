"""
Login Issue Diagnosis and Fix
登录问题诊断和修复
"""

import requests
import json
import time

def print_header(title):
    print("\n" + "="*80)
    print(f"🔍 {title}")
    print("="*80)

def print_success(message):
    print(f"✅ {message}")

def print_error(message):
    print(f"❌ {message}")

def print_info(message):
    print(f"ℹ️ {message}")

def print_warning(message):
    print(f"⚠️ {message}")

def test_backend_health():
    """Test backend health and API endpoints"""
    print_header("后端健康检查")
    
    try:
        # Test root endpoint
        print("\n📋 测试根端点")
        response = requests.get("http://localhost:8000/", timeout=10)
        print_success(f"根端点响应: {response.status_code}")
        
        # Test API docs
        print("\n📋 测试API文档")
        docs_response = requests.get("http://localhost:8000/docs", timeout=10)
        print_success(f"API文档响应: {docs_response.status_code}")
        
        # Test auth endpoint structure
        print("\n📋 测试认证端点")
        auth_response = requests.post("http://localhost:8000/api/v1/auth/token", 
                                    json={"test": "test"}, timeout=10)
        print_info(f"认证端点响应: {auth_response.status_code}")
        if auth_response.status_code != 200:
            try:
                error_detail = auth_response.json()
                print_info(f"错误详情: {error_detail}")
            except:
                print_info(f"响应内容: {auth_response.text}")
        
        return True
    except Exception as e:
        print_error(f"后端健康检查失败: {e}")
        return False

def test_user_accounts():
    """Test existing user accounts"""
    print_header("用户账号测试")
    
    api_url = "http://localhost:8000/api/v1"
    
    # Test accounts to try
    test_accounts = [
        {"username": "teacher_zhang", "password": "teacher123456", "role": "teacher"},
        {"username": "teststudent", "password": "student123", "role": "student"},
        {"username": "teststudent2", "password": "student123", "role": "student"},
        {"username": "admin", "password": "admin123456", "role": "admin"}
    ]
    
    working_accounts = []
    
    for account in test_accounts:
        print(f"\n📋 测试账号: {account['username']}")
        try:
            login_data = {
                "username": account["username"],
                "password": account["password"]
            }
            
            response = requests.post(f"{api_url}/auth/token", 
                                   json=login_data, timeout=10)
            
            if response.status_code == 200:
                token_data = response.json()
                token = token_data.get("access_token")
                
                if token:
                    # Verify token by getting user info
                    headers = {"Authorization": f"Bearer {token}"}
                    profile_response = requests.get(f"{api_url}/auth/me", 
                                                  headers=headers, timeout=10)
                    
                    if profile_response.status_code == 200:
                        user_info = profile_response.json()
                        print_success(f"账号正常: {user_info['username']} (ID: {user_info['id']}, 角色: {user_info['role']})")
                        working_accounts.append({
                            "account": account,
                            "user_info": user_info,
                            "token": token
                        })
                    else:
                        print_error(f"Token验证失败: {profile_response.json()}")
                else:
                    print_error("未收到访问令牌")
            else:
                print_error(f"登录失败: {response.status_code}")
                try:
                    error_detail = response.json()
                    print_error(f"错误详情: {error_detail}")
                except:
                    print_error(f"响应内容: {response.text}")
                    
        except Exception as e:
            print_error(f"测试账号异常: {e}")
    
    return working_accounts

def create_missing_users():
    """Create missing users if needed"""
    print_header("创建缺失用户")
    
    api_url = "http://localhost:8000/api/v1"
    
    # Users to ensure exist
    required_users = [
        {
            "username": "teacher_zhang",
            "password": "teacher123456",
            "email": "<EMAIL>",
            "full_name": "张老师",
            "role": "teacher"
        },
        {
            "username": "teststudent",
            "password": "student123",
            "email": "<EMAIL>", 
            "full_name": "测试学生",
            "role": "student"
        },
        {
            "username": "admin",
            "password": "admin123456",
            "email": "<EMAIL>",
            "full_name": "系统管理员",
            "role": "admin"
        }
    ]
    
    created_users = []
    
    for user_data in required_users:
        print(f"\n📋 确保用户存在: {user_data['username']}")
        
        try:
            # Try to register the user
            register_response = requests.post(f"{api_url}/auth/register",
                                            json=user_data, timeout=10)
            
            if register_response.status_code == 201:
                user = register_response.json()
                print_success(f"创建用户: {user['username']} (ID: {user['id']})")
                created_users.append(user)
            else:
                # User might already exist, try to login
                login_data = {
                    "username": user_data["username"],
                    "password": user_data["password"]
                }
                
                login_response = requests.post(f"{api_url}/auth/token",
                                             json=login_data, timeout=10)
                
                if login_response.status_code == 200:
                    print_info(f"用户已存在: {user_data['username']}")
                else:
                    print_error(f"用户创建和登录都失败: {user_data['username']}")
                    
        except Exception as e:
            print_error(f"处理用户 {user_data['username']} 异常: {e}")
    
    return created_users

def test_frontend_connectivity():
    """Test frontend connectivity"""
    print_header("前端连接测试")
    
    ports = [3003, 3002, 3001, 3000]
    working_ports = []
    
    for port in ports:
        try:
            print(f"\n📋 测试端口 {port}")
            response = requests.get(f"http://localhost:{port}", timeout=5)
            if response.status_code == 200:
                print_success(f"端口 {port} 正常响应")
                working_ports.append(port)
            else:
                print_warning(f"端口 {port} 响应异常: {response.status_code}")
        except Exception as e:
            print_info(f"端口 {port} 无响应: {e}")
    
    return working_ports

def test_login_flow_end_to_end():
    """Test complete login flow"""
    print_header("端到端登录流程测试")
    
    api_url = "http://localhost:8000/api/v1"
    
    # Test the complete login flow
    print("\n📋 完整登录流程测试")
    
    try:
        # Step 1: Login
        login_data = {
            "username": "teacher_zhang",
            "password": "teacher123456"
        }
        
        print_info("步骤1: 发送登录请求")
        login_response = requests.post(f"{api_url}/auth/token",
                                     json=login_data, timeout=10)
        
        print_info(f"登录响应状态: {login_response.status_code}")
        
        if login_response.status_code == 200:
            token_data = login_response.json()
            print_success("登录成功，收到令牌")
            
            # Step 2: Use token to access protected resource
            token = token_data.get("access_token")
            headers = {"Authorization": f"Bearer {token}"}
            
            print_info("步骤2: 使用令牌访问受保护资源")
            profile_response = requests.get(f"{api_url}/auth/me",
                                          headers=headers, timeout=10)
            
            if profile_response.status_code == 200:
                user_info = profile_response.json()
                print_success(f"获取用户信息成功: {user_info['username']}")
                
                # Step 3: Test accessing other resources
                print_info("步骤3: 测试访问其他资源")
                courses_response = requests.get(f"{api_url}/courses/",
                                              headers=headers, timeout=10)
                
                if courses_response.status_code == 200:
                    courses = courses_response.json()
                    print_success(f"获取课程列表成功: {len(courses)} 个课程")
                    return True
                else:
                    print_error(f"获取课程失败: {courses_response.status_code}")
            else:
                print_error(f"获取用户信息失败: {profile_response.status_code}")
        else:
            print_error(f"登录失败: {login_response.status_code}")
            try:
                error_detail = login_response.json()
                print_error(f"错误详情: {error_detail}")
            except:
                print_error(f"响应内容: {login_response.text}")
                
    except Exception as e:
        print_error(f"端到端测试异常: {e}")
    
    return False

def main():
    print("🔍 登录问题全面诊断")
    print("=" * 90)
    
    print_info("🎯 本次诊断将检查:")
    print_info("   1. 后端服务健康状态")
    print_info("   2. 用户账号可用性")
    print_info("   3. 前端应用连接")
    print_info("   4. 完整登录流程")
    print_info("   5. 自动修复问题")
    
    # Step 1: Backend health check
    backend_healthy = test_backend_health()
    
    if not backend_healthy:
        print_error("后端服务异常，停止诊断")
        return False
    
    # Step 2: Test user accounts
    working_accounts = test_user_accounts()
    
    # Step 3: Create missing users if needed
    if len(working_accounts) == 0:
        print_warning("没有可用的用户账号，尝试创建")
        created_users = create_missing_users()
        
        # Re-test accounts after creation
        working_accounts = test_user_accounts()
    
    # Step 4: Test frontend
    working_ports = test_frontend_connectivity()
    
    # Step 5: End-to-end test
    login_flow_works = test_login_flow_end_to_end()
    
    # Generate diagnosis report
    print_header("🔍 诊断结果报告")
    
    print(f"📊 系统状态:")
    print(f"   🔧 后端服务: {'✅ 正常' if backend_healthy else '❌ 异常'}")
    print(f"   👥 可用账号: {len(working_accounts)} 个")
    print(f"   🎨 前端端口: {len(working_ports)} 个可用")
    print(f"   🔄 登录流程: {'✅ 正常' if login_flow_works else '❌ 异常'}")
    
    if working_accounts:
        print(f"\n👥 可用账号列表:")
        for account_info in working_accounts:
            user = account_info['user_info']
            print_info(f"   {user['username']} (角色: {user['role']}, ID: {user['id']})")
    
    if working_ports:
        print(f"\n🎨 可用前端端口:")
        for port in working_ports:
            print_info(f"   http://localhost:{port}")
    
    # Determine if login issue is resolved
    login_issue_resolved = backend_healthy and len(working_accounts) > 0 and login_flow_works
    
    print(f"\n🎯 登录问题状态:")
    if login_issue_resolved:
        print_success("✅ 登录功能正常，问题已解决")
        
        print(f"\n🔗 立即测试:")
        if working_ports:
            main_port = working_ports[0]
            print_info(f"   1. 访问: http://localhost:{main_port}")
            print_info(f"   2. 使用账号: teacher_zhang / teacher123456")
            print_info(f"   3. 或使用: teststudent / student123")
        
        print_info(f"   4. API文档: http://localhost:8000/docs")
        
    else:
        print_error("❌ 登录功能仍有问题")
        
        print(f"\n🔧 建议修复步骤:")
        if not backend_healthy:
            print_info("   1. 重启后端服务")
        if len(working_accounts) == 0:
            print_info("   2. 检查数据库和用户数据")
        if not login_flow_works:
            print_info("   3. 检查认证逻辑和JWT配置")
    
    return login_issue_resolved

if __name__ == "__main__":
    success = main()
    print("\n" + "=" * 90)
    print("🔍 登录问题诊断完成！")
    print("=" * 90)
    
    if success:
        print("\n🎊 登录功能正常，可以使用应用！")
    else:
        print("\n⚠️ 登录功能存在问题，需要进一步修复")
