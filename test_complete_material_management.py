import requests
import json
import time

def print_header(title):
    print("\n" + "="*70)
    print(f"🎓 {title}")
    print("="*70)

def print_success(message):
    print(f"✅ {message}")

def print_error(message):
    print(f"❌ {message}")

def print_info(message):
    print(f"ℹ️ {message}")

def test_complete_material_management():
    """Test complete material management functionality"""
    print_header("完整教学资料管理功能测试")
    
    api_url = "http://localhost:8000/api/v1"
    
    # Step 1: Login as teacher
    print("\n📋 Step 1: 教师登录")
    try:
        login_response = requests.post(f"{api_url}/auth/token", 
                                     json={"username": "teacher_zhang", "password": "teacher123456"},
                                     timeout=10)
        
        if login_response.status_code == 200:
            token = login_response.json()["access_token"]
            headers = {"Authorization": f"Bearer {token}"}
            print_success("教师登录成功")
        else:
            print_error(f"教师登录失败: {login_response.json()}")
            return False
    except Exception as e:
        print_error(f"登录异常: {e}")
        return False
    
    # Step 2: Create test courses
    print("\n📋 Step 2: 创建测试课程")
    course_ids = []
    course_names = ["Python编程基础", "数据结构与算法", "Web开发实战"]
    
    for course_name in course_names:
        try:
            course_data = {
                "title": course_name,
                "description": f"{course_name}的详细课程内容",
                "cover_image_url": None
            }
            
            course_response = requests.post(f"{api_url}/courses/",
                                          json=course_data, headers=headers, timeout=10)
            
            if course_response.status_code == 201:
                course = course_response.json()
                course_ids.append(course['id'])
                print_success(f"课程创建成功: {course_name} (ID: {course['id']})")
            else:
                print_error(f"课程创建失败: {course_response.json()}")
                return False
        except Exception as e:
            print_error(f"创建课程异常: {e}")
            return False
    
    # Step 3: Upload materials to each course
    print("\n📋 Step 3: 为每个课程上传教学资料")
    material_ids = []
    
    test_materials = [
        {
            "filename": "python_basics.txt",
            "title": "Python基础语法",
            "description": "Python编程语言的基础语法介绍",
            "content": """Python基础语法教程

1. 变量和数据类型
   - 整数 (int)
   - 浮点数 (float)
   - 字符串 (str)
   - 布尔值 (bool)

2. 控制结构
   - if/elif/else 条件语句
   - for 循环
   - while 循环

3. 函数定义
   def function_name(parameters):
       return result

4. 类和对象
   class ClassName:
       def __init__(self):
           pass

这是Python编程的基础内容，适合初学者学习。"""
        },
        {
            "filename": "data_structures.txt",
            "title": "数据结构概述",
            "description": "常用数据结构的介绍和应用",
            "content": """数据结构与算法

1. 线性数据结构
   - 数组 (Array)
   - 链表 (Linked List)
   - 栈 (Stack)
   - 队列 (Queue)

2. 树形数据结构
   - 二叉树 (Binary Tree)
   - 二叉搜索树 (BST)
   - 平衡树 (AVL Tree)
   - 红黑树 (Red-Black Tree)

3. 图数据结构
   - 有向图 (Directed Graph)
   - 无向图 (Undirected Graph)
   - 加权图 (Weighted Graph)

4. 哈希表
   - 哈希函数
   - 冲突解决
   - 应用场景

数据结构是计算机科学的基础，对算法效率有重要影响。"""
        },
        {
            "filename": "web_development.txt",
            "title": "Web开发技术栈",
            "description": "现代Web开发的技术栈介绍",
            "content": """Web开发实战指南

1. 前端技术
   - HTML5: 网页结构
   - CSS3: 样式设计
   - JavaScript: 交互逻辑
   - React/Vue: 前端框架

2. 后端技术
   - Python: Flask/Django
   - Node.js: Express
   - Java: Spring Boot
   - 数据库: MySQL/PostgreSQL

3. 开发工具
   - 版本控制: Git
   - 包管理: npm/pip
   - 构建工具: Webpack
   - 部署: Docker/云服务

4. 项目实战
   - 需求分析
   - 系统设计
   - 编码实现
   - 测试部署

Web开发需要掌握全栈技术，从前端到后端的完整开发流程。"""
        }
    ]
    
    for i, (course_id, material_data) in enumerate(zip(course_ids, test_materials)):
        try:
            # Get upload credentials
            params = {
                "course_id": course_id,
                "original_filename": material_data["filename"],
                "file_type": "text"
            }
            
            upload_creds_response = requests.post(f"{api_url}/materials/upload-credentials", 
                                                params=params, headers=headers, timeout=10)
            
            if upload_creds_response.status_code == 200:
                upload_info = upload_creds_response.json()
                upload_url = upload_info['upload_url']
                object_name = upload_info['object_name']
                
                # Upload file
                upload_response = requests.put(upload_url, 
                                             data=material_data["content"].encode('utf-8'),
                                             headers={"Content-Type": "text/plain; charset=utf-8"},
                                             timeout=30)
                
                if upload_response.status_code in [200, 204]:
                    # Create material record
                    material_record = {
                        "title": material_data["title"],
                        "description": material_data["description"],
                        "file_type": "text",
                        "file_path_minio": object_name,
                        "file_size_bytes": len(material_data["content"].encode('utf-8'))
                    }
                    
                    material_response = requests.post(f"{api_url}/materials/{course_id}",
                                                    json=material_record, headers=headers, timeout=10)
                    
                    if material_response.status_code == 201:
                        material = material_response.json()
                        material_ids.append(material['id'])
                        print_success(f"材料上传成功: {material_data['title']} (ID: {material['id']})")
                    else:
                        print_error(f"材料记录创建失败: {material_response.json()}")
                        return False
                else:
                    print_error(f"文件上传失败: {upload_response.status_code}")
                    return False
            else:
                print_error(f"获取上传凭证失败: {upload_creds_response.json()}")
                return False
        except Exception as e:
            print_error(f"上传材料异常: {e}")
            return False
    
    # Step 4: Verify materials are displayed under correct courses
    print("\n📋 Step 4: 验证材料正确显示在对应课程下")
    try:
        for course_id in course_ids:
            materials_response = requests.get(f"{api_url}/materials/course/{course_id}",
                                            headers=headers, timeout=10)
            
            if materials_response.status_code == 200:
                materials = materials_response.json()
                print_success(f"课程 {course_id} 包含 {len(materials)} 个材料")
                for material in materials:
                    print_info(f"  - {material['title']} ({material['file_type']})")
            else:
                print_error(f"获取课程材料失败: {materials_response.json()}")
                return False
    except Exception as e:
        print_error(f"验证材料异常: {e}")
        return False
    
    # Step 5: Test material deletion
    print("\n📋 Step 5: 测试材料删除功能")
    try:
        if material_ids:
            # Delete the first material
            material_to_delete = material_ids[0]
            delete_response = requests.delete(f"{api_url}/materials/{material_to_delete}",
                                            headers=headers, timeout=10)
            
            if delete_response.status_code in [200, 204]:
                print_success(f"材料删除成功: ID {material_to_delete}")
                
                # Verify material is deleted
                get_response = requests.get(f"{api_url}/materials/{material_to_delete}",
                                          headers=headers, timeout=10)
                
                if get_response.status_code == 404:
                    print_success("确认材料已被删除")
                else:
                    print_error("材料删除后仍然存在")
                    return False
            else:
                print_error(f"材料删除失败: {delete_response.json()}")
                return False
    except Exception as e:
        print_error(f"删除材料异常: {e}")
        return False
    
    # Step 6: Test AI analysis (optional)
    print("\n📋 Step 6: 测试AI分析功能")
    try:
        if len(material_ids) > 1:
            # Analyze the second material
            material_to_analyze = material_ids[1]
            analysis_response = requests.post(f"{api_url}/materials/{material_to_analyze}/analyze",
                                            headers=headers, timeout=30)
            
            if analysis_response.status_code == 200:
                analysis = analysis_response.json()
                print_success("AI分析成功")
                print_info(f"分析结果: {analysis.get('summary', 'N/A')[:100]}...")
            else:
                print_info("AI分析功能可能未启用或出现错误")
    except Exception as e:
        print_info(f"AI分析测试跳过: {e}")
    
    return True

def check_frontend_accessibility():
    """Check if frontend is accessible"""
    print_header("前端可访问性检查")
    
    ports = [3000, 3001, 3002, 3003]
    
    for port in ports:
        try:
            response = requests.get(f"http://localhost:{port}", timeout=3)
            if response.status_code == 200:
                print_success(f"前端运行在端口 {port}")
                return port
        except:
            continue
    
    print_error("未找到运行中的前端应用")
    return None

def main():
    print("🎓 完整教学资料管理功能测试")
    print("=" * 80)
    
    print_info("此测试验证完整的教学资料管理功能，包括:")
    print_info("1. 课程创建")
    print_info("2. 材料上传到指定课程")
    print_info("3. 材料在对应课程下正确显示")
    print_info("4. 材料删除功能")
    print_info("5. AI分析功能")
    
    # Test backend functionality
    backend_success = test_complete_material_management()
    
    # Check frontend
    frontend_port = check_frontend_accessibility()
    
    # Generate final report
    print_header("完整功能测试结果")
    
    print(f"📊 测试结果:")
    print(f"   🔧 后端功能: {'✅ 完全正常' if backend_success else '❌ 存在问题'}")
    print(f"   🎨 前端应用: {'✅ 运行正常' if frontend_port else '❌ 无法访问'}")
    
    if backend_success and frontend_port:
        print("\n🎉 教学资料管理功能开发完成！")
        print("\n🚀 系统已就绪，可以上线使用！")
        
        print("\n🔗 访问地址:")
        print(f"   🎨 前端应用: http://localhost:{frontend_port}")
        print(f"   📚 课程管理: http://localhost:{frontend_port}/teacher/courses")
        print(f"   🔧 后端API: http://localhost:8000")
        print(f"   📖 API文档: http://localhost:8000/docs")
        
        print("\n👥 测试账号:")
        print("   👨‍🏫 教师账号: teacher_zhang / teacher123456")
        print("   👨‍🎓 学生账号: testuser / testpass123")
        
        print("\n✨ 核心功能:")
        print("   📚 课程创建和管理")
        print("   📁 教学资料上传")
        print("   🗂️ 资料按课程分类显示")
        print("   🗑️ 资料删除功能")
        print("   🤖 AI智能分析")
        print("   📊 完整的用户界面")
        
        print("\n🎯 使用指南:")
        print("   1. 访问前端应用并登录教师账号")
        print("   2. 在课程管理页面创建新课程")
        print("   3. 选择课程并上传教学资料")
        print("   4. 查看资料在对应课程下的显示")
        print("   5. 使用删除功能管理资料")
        print("   6. 使用AI分析功能获取内容洞察")
        
    else:
        print("\n⚠️ 系统存在问题，需要进一步检查")
        if not backend_success:
            print("   🔧 后端功能需要修复")
        if not frontend_port:
            print("   🎨 前端应用需要启动")
    
    print("\n" + "=" * 80)
    print("🎯 完整功能测试完成！")
    print("=" * 80)

if __name__ == "__main__":
    main()
