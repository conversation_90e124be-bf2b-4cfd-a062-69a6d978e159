import requests
import json

def print_header(title):
    print("\n" + "="*70)
    print(f"🎓 {title}")
    print("="*70)

def print_success(message):
    print(f"✅ {message}")

def print_error(message):
    print(f"❌ {message}")

def print_info(message):
    print(f"ℹ️ {message}")

def test_add_student_functionality():
    """Test the add student functionality"""
    print_header("添加学生功能测试")
    
    api_url = "http://localhost:8000/api/v1"
    
    # Step 1: Teacher login
    print("\n📋 Step 1: 教师登录")
    try:
        login_response = requests.post(f"{api_url}/auth/token", 
                                     json={"username": "teacher_zhang", "password": "teacher123456"},
                                     timeout=10)
        
        if login_response.status_code == 200:
            teacher_token = login_response.json()["access_token"]
            teacher_headers = {"Authorization": f"Bearer {teacher_token}"}
            print_success("教师登录成功")
        else:
            print_error(f"教师登录失败: {login_response.json()}")
            return False
    except Exception as e:
        print_error(f"教师登录异常: {e}")
        return False
    
    # Step 2: Get classes
    print("\n📋 Step 2: 获取班级列表")
    try:
        classes_response = requests.get(f"{api_url}/classes/", headers=teacher_headers, timeout=10)
        if classes_response.status_code == 200:
            classes = classes_response.json()
            print_success(f"获取到 {len(classes)} 个班级")
            for cls in classes:
                print_info(f"  班级: {cls['name']} (ID: {cls['id']}, 学生数: {cls.get('student_count', 0)})")
            
            if not classes:
                print_error("没有可用的班级进行测试")
                return False
            
            test_class = classes[0]
        else:
            print_error(f"获取班级失败: {classes_response.json()}")
            return False
    except Exception as e:
        print_error(f"获取班级异常: {e}")
        return False
    
    # Step 3: Get all users to find students
    print("\n📋 Step 3: 查找可用的学生")
    try:
        # Try to get student user
        student_login = requests.post(f"{api_url}/auth/token", 
                                    json={"username": "testuser", "password": "testpass123"},
                                    timeout=10)
        
        if student_login.status_code == 200:
            student_token = student_login.json()["access_token"]
            student_headers = {"Authorization": f"Bearer {student_token}"}
            
            # Get student profile
            profile_response = requests.get(f"{api_url}/auth/me", headers=student_headers, timeout=10)
            if profile_response.status_code == 200:
                student_info = profile_response.json()
                student_id = student_info['id']
                print_success(f"找到学生: {student_info['username']} (ID: {student_id})")
            else:
                print_error("无法获取学生信息")
                return False
        else:
            print_error("学生登录失败")
            return False
    except Exception as e:
        print_error(f"查找学生异常: {e}")
        return False
    
    # Step 4: Test adding student to class
    print(f"\n📋 Step 4: 测试添加学生到班级 '{test_class['name']}'")
    try:
        add_student_data = {
            "student_ids": [student_id]
        }
        
        print_info(f"请求数据: {add_student_data}")
        
        add_response = requests.post(f"{api_url}/classes/{test_class['id']}/students",
                                   json=add_student_data,
                                   headers=teacher_headers,
                                   timeout=10)
        
        if add_response.status_code == 200:
            result = add_response.json()
            print_success(f"成功添加学生到班级")
            print_info(f"添加的学生: {result}")
        else:
            print_error(f"添加学生失败: {add_response.status_code}")
            try:
                error_detail = add_response.json()
                print_error(f"错误详情: {error_detail}")
            except:
                print_error(f"响应内容: {add_response.text}")
            return False
    except Exception as e:
        print_error(f"添加学生异常: {e}")
        return False
    
    # Step 5: Verify student was added
    print(f"\n📋 Step 5: 验证学生是否成功添加")
    try:
        students_response = requests.get(f"{api_url}/classes/{test_class['id']}/students",
                                       headers=teacher_headers, timeout=10)
        
        if students_response.status_code == 200:
            students = students_response.json()
            print_success(f"班级现在有 {len(students)} 个学生")
            for student in students:
                print_info(f"  学生: {student['username']} (ID: {student['id']})")
            
            # Check if our test student is in the list
            test_student_found = any(s['id'] == student_id for s in students)
            if test_student_found:
                print_success("测试学生已成功添加到班级")
            else:
                print_error("测试学生未在班级中找到")
                return False
        else:
            print_error(f"获取班级学生失败: {students_response.json()}")
            return False
    except Exception as e:
        print_error(f"验证学生异常: {e}")
        return False
    
    return True

def test_frontend_accessibility():
    """Test frontend accessibility"""
    print_header("前端可访问性测试")
    
    try:
        response = requests.get("http://localhost:3001", timeout=5)
        if response.status_code == 200:
            print_success("前端应用运行正常")
            print_info("班级管理页面: http://localhost:3001/teacher/classes")
            return True
        else:
            print_error(f"前端应用异常: {response.status_code}")
            return False
    except Exception as e:
        print_error(f"前端连接失败: {e}")
        return False

def analyze_button_issue():
    """Analyze the add student button issue"""
    print_header("添加学生按钮问题分析")
    
    print_info("根据代码分析，'添加学生'按钮变灰的可能原因:")
    print_info("1. 没有选择班级 (selectedClassId为空)")
    print_info("2. 没有输入学生ID (studentsToAdd为空)")
    print_info("3. 前端状态管理问题")
    print_info("4. API调用失败导致状态异常")
    
    print_info("\n按钮启用条件:")
    print_info("- 必须选择一个班级")
    print_info("- 必须输入至少一个学生ID")
    print_info("- 格式: 逗号分隔的数字ID，如 '1,2,3'")
    
    print_info("\n建议的测试步骤:")
    print_info("1. 打开班级管理页面")
    print_info("2. 在'添加学生到班级'部分选择一个班级")
    print_info("3. 在'学生ID'输入框中输入有效的学生ID")
    print_info("4. 观察按钮是否变为可用状态")

def main():
    print("🎓 添加学生按钮问题诊断和修复")
    print("=" * 80)
    
    print_info("此测试将诊断'添加学生'按钮为灰色不可用的问题")
    
    # Test 1: Frontend accessibility
    frontend_ok = test_frontend_accessibility()
    
    # Test 2: Backend functionality
    backend_ok = test_add_student_functionality()
    
    # Test 3: Analyze button issue
    analyze_button_issue()
    
    # Generate report
    print_header("问题诊断结果")
    
    print(f"📊 测试结果:")
    print(f"   🎨 前端应用: {'✅ 正常' if frontend_ok else '❌ 异常'}")
    print(f"   🔧 后端功能: {'✅ 正常' if backend_ok else '❌ 异常'}")
    
    if frontend_ok and backend_ok:
        print("\n✅ 后端功能正常，问题可能在前端状态管理")
        print("\n🔧 建议的解决方案:")
        print("   1. 检查前端组件的状态更新")
        print("   2. 确保正确选择班级和输入学生ID")
        print("   3. 检查浏览器控制台是否有JavaScript错误")
        print("   4. 验证API调用是否正常")
        
        print("\n🎯 立即测试:")
        print("   1. 访问: http://localhost:3001/teacher/classes")
        print("   2. 登录教师账号: teacher_zhang / teacher123456")
        print("   3. 选择班级并输入学生ID: 2")
        print("   4. 观察按钮状态变化")
        
    else:
        print("\n❌ 发现系统问题，需要进一步修复")
    
    return frontend_ok and backend_ok

if __name__ == "__main__":
    success = main()
    print("\n" + "=" * 80)
    print("🎯 添加学生按钮问题诊断完成！")
    print("=" * 80)
