import requests
import json
import time

def print_header(title):
    print("\n" + "="*70)
    print(f"🎓 {title}")
    print("="*70)

def print_success(message):
    print(f"✅ {message}")

def print_error(message):
    print(f"❌ {message}")

def print_info(message):
    print(f"ℹ️ {message}")

def test_course_class_association():
    """Test complete course-class association functionality"""
    print_header("课程班级关联功能完整测试")
    
    api_url = "http://localhost:8000/api/v1"
    
    # Step 1: Teacher login
    print("\n📋 Step 1: 教师登录")
    try:
        login_response = requests.post(f"{api_url}/auth/token", 
                                     json={"username": "teacher_zhang", "password": "teacher123456"},
                                     timeout=10)
        
        if login_response.status_code == 200:
            teacher_token = login_response.json()["access_token"]
            teacher_headers = {"Authorization": f"Bearer {teacher_token}"}
            print_success("教师登录成功")
        else:
            print_error(f"教师登录失败: {login_response.json()}")
            return False
    except Exception as e:
        print_error(f"教师登录异常: {e}")
        return False
    
    # Step 2: Student login
    print("\n📋 Step 2: 学生登录")
    try:
        student_login = requests.post(f"{api_url}/auth/token", 
                                    json={"username": "testuser", "password": "testpass123"},
                                    timeout=10)
        
        if student_login.status_code == 200:
            student_token = student_login.json()["access_token"]
            student_headers = {"Authorization": f"Bearer {student_token}"}
            print_success("学生登录成功")
        else:
            print_error(f"学生登录失败: {student_login.json()}")
            return False
    except Exception as e:
        print_error(f"学生登录异常: {e}")
        return False
    
    # Step 3: Get teacher's courses and classes
    print("\n📋 Step 3: 获取教师的课程和班级")
    try:
        # Get courses
        courses_response = requests.get(f"{api_url}/courses/", headers=teacher_headers, timeout=10)
        if courses_response.status_code == 200:
            courses = courses_response.json()
            print_success(f"获取到 {len(courses)} 个课程")
            for course in courses[:3]:  # Show first 3
                print_info(f"  📚 {course['title']} (ID: {course['id']})")
        else:
            print_error("获取课程失败")
            return False
        
        # Get classes
        classes_response = requests.get(f"{api_url}/classes/", headers=teacher_headers, timeout=10)
        if classes_response.status_code == 200:
            classes = classes_response.json()
            print_success(f"获取到 {len(classes)} 个班级")
            for class_item in classes[:3]:  # Show first 3
                print_info(f"  👥 {class_item['name']} (ID: {class_item['id']})")
        else:
            print_error("获取班级失败")
            return False
            
    except Exception as e:
        print_error(f"获取数据异常: {e}")
        return False
    
    if not courses or not classes:
        print_error("没有可用的课程或班级进行测试")
        return False
    
    test_course = courses[0]
    test_class = classes[0] if len(classes) > 0 else None
    
    # Step 4: Test getting course classes
    print("\n📋 Step 4: 测试获取课程的班级")
    try:
        course_classes_response = requests.get(f"{api_url}/courses/{test_course['id']}/classes", 
                                             headers=teacher_headers, timeout=10)
        
        if course_classes_response.status_code == 200:
            course_classes = course_classes_response.json()
            print_success(f"课程 '{test_course['title']}' 已分配 {len(course_classes)} 个班级")
            for class_item in course_classes:
                print_info(f"  👥 {class_item['name']} (ID: {class_item['id']})")
        else:
            print_error(f"获取课程班级失败: {course_classes_response.json()}")
            return False
    except Exception as e:
        print_error(f"获取课程班级异常: {e}")
        return False
    
    # Step 5: Test getting available classes for course
    print("\n📋 Step 5: 测试获取可分配的班级")
    try:
        available_classes_response = requests.get(f"{api_url}/courses/{test_course['id']}/available-classes", 
                                                headers=teacher_headers, timeout=10)
        
        if available_classes_response.status_code == 200:
            available_classes = available_classes_response.json()
            print_success(f"课程 '{test_course['title']}' 可分配 {len(available_classes)} 个班级")
            for class_item in available_classes:
                print_info(f"  👥 {class_item['name']} (ID: {class_item['id']})")
        else:
            print_error(f"获取可分配班级失败: {available_classes_response.json()}")
            return False
    except Exception as e:
        print_error(f"获取可分配班级异常: {e}")
        return False
    
    # Step 6: Test assigning classes to course (if available)
    if available_classes and len(available_classes) > 0:
        print("\n📋 Step 6: 测试分配班级到课程")
        try:
            class_to_assign = available_classes[0]
            assign_data = {"class_ids": [class_to_assign['id']]}
            
            assign_response = requests.post(f"{api_url}/courses/{test_course['id']}/assign-classes",
                                          json=assign_data, headers=teacher_headers, timeout=10)
            
            if assign_response.status_code == 201:
                result = assign_response.json()
                print_success(f"成功分配班级: {result['message']}")
            else:
                print_error(f"分配班级失败: {assign_response.json()}")
                return False
        except Exception as e:
            print_error(f"分配班级异常: {e}")
            return False
        
        # Verify assignment
        print("\n📋 Step 6.1: 验证班级分配")
        try:
            course_classes_response = requests.get(f"{api_url}/courses/{test_course['id']}/classes", 
                                                 headers=teacher_headers, timeout=10)
            
            if course_classes_response.status_code == 200:
                updated_course_classes = course_classes_response.json()
                print_success(f"分配后课程包含 {len(updated_course_classes)} 个班级")
                
                # Check if our assigned class is in the list
                assigned_class_ids = [c['id'] for c in updated_course_classes]
                if class_to_assign['id'] in assigned_class_ids:
                    print_success(f"班级 '{class_to_assign['name']}' 已成功分配到课程")
                else:
                    print_error("班级分配验证失败")
                    return False
            else:
                print_error("验证班级分配失败")
                return False
        except Exception as e:
            print_error(f"验证班级分配异常: {e}")
            return False
    else:
        print_info("没有可分配的班级，跳过分配测试")
    
    # Step 7: Test student access to courses through class
    print("\n📋 Step 7: 测试学生通过班级访问课程")
    try:
        student_courses_response = requests.get(f"{api_url}/students/my-courses", 
                                              headers=student_headers, timeout=10)
        
        if student_courses_response.status_code == 200:
            student_courses = student_courses_response.json()
            print_success(f"学生可访问 {len(student_courses)} 个课程")
            for course in student_courses:
                print_info(f"  📚 {course['title']} (ID: {course['id']})")
        else:
            print_error(f"获取学生课程失败: {student_courses_response.json()}")
            return False
    except Exception as e:
        print_error(f"获取学生课程异常: {e}")
        return False
    
    # Step 8: Test removing class from course (cleanup)
    if available_classes and len(available_classes) > 0:
        print("\n📋 Step 8: 测试从课程中移除班级")
        try:
            class_to_remove = class_to_assign  # Use the class we just assigned
            
            remove_response = requests.delete(f"{api_url}/courses/{test_course['id']}/classes/{class_to_remove['id']}",
                                            headers=teacher_headers, timeout=10)
            
            if remove_response.status_code == 204:
                print_success(f"成功从课程中移除班级 '{class_to_remove['name']}'")
            else:
                print_error(f"移除班级失败: {remove_response.status_code}")
                return False
        except Exception as e:
            print_error(f"移除班级异常: {e}")
            return False
        
        # Verify removal
        print("\n📋 Step 8.1: 验证班级移除")
        try:
            course_classes_response = requests.get(f"{api_url}/courses/{test_course['id']}/classes", 
                                                 headers=teacher_headers, timeout=10)
            
            if course_classes_response.status_code == 200:
                final_course_classes = course_classes_response.json()
                print_success(f"移除后课程包含 {len(final_course_classes)} 个班级")
                
                # Check if our removed class is no longer in the list
                final_class_ids = [c['id'] for c in final_course_classes]
                if class_to_remove['id'] not in final_class_ids:
                    print_success(f"班级 '{class_to_remove['name']}' 已成功从课程中移除")
                else:
                    print_error("班级移除验证失败")
                    return False
            else:
                print_error("验证班级移除失败")
                return False
        except Exception as e:
            print_error(f"验证班级移除异常: {e}")
            return False
    
    return True

def check_frontend_accessibility():
    """Check if frontend is accessible"""
    print_header("前端可访问性检查")
    
    ports = [3000, 3001, 3002, 3003]
    
    for port in ports:
        try:
            response = requests.get(f"http://localhost:{port}", timeout=3)
            if response.status_code == 200:
                print_success(f"前端运行在端口 {port}")
                return port
        except:
            continue
    
    print_error("未找到运行中的前端应用")
    return None

def main():
    print("🎓 课程班级关联功能完整测试")
    print("=" * 80)
    
    print_info("此测试验证课程与班级关联的完整功能:")
    print_info("1. 教师可以将课程分配给班级")
    print_info("2. 学生可以通过班级访问课程")
    print_info("3. 完整的CRUD操作")
    
    # Test backend functionality
    backend_success = test_course_class_association()
    
    # Check frontend
    frontend_port = check_frontend_accessibility()
    
    # Generate final report
    print_header("完整功能测试结果")
    
    print(f"📊 测试结果:")
    print(f"   🔧 后端功能: {'✅ 完全正常' if backend_success else '❌ 存在问题'}")
    print(f"   🎨 前端应用: {'✅ 运行正常' if frontend_port else '❌ 无法访问'}")
    
    if backend_success and frontend_port:
        print("\n🎉 课程班级关联功能开发完成！")
        print("\n🚀 系统已就绪，可以上线使用！")
        
        print("\n🔗 访问地址:")
        print(f"   🎨 前端应用: http://localhost:{frontend_port}")
        print(f"   📚 课程管理: http://localhost:{frontend_port}/teacher/courses")
        print(f"   🔧 后端API: http://localhost:8000")
        print(f"   📖 API文档: http://localhost:8000/docs")
        
        print("\n👥 测试账号:")
        print("   👨‍🏫 教师账号: teacher_zhang / teacher123456")
        print("   👨‍🎓 学生账号: testuser / testpass123")
        
        print("\n✨ 新增功能:")
        print("   📚 课程班级关联管理")
        print("   👥 班级分配到课程")
        print("   🔄 学生通过班级访问课程")
        print("   🎯 精确的权限控制")
        print("   📊 完整的管理界面")
        
        print("\n🎯 使用指南:")
        print("   1. 教师登录后进入课程管理页面")
        print("   2. 在每个课程下可以看到'分配班级'部分")
        print("   3. 点击'分配班级'按钮选择要分配的班级")
        print("   4. 学生登录后只能看到其班级被分配的课程")
        print("   5. 教师可以随时移除班级分配")
        
    else:
        print("\n⚠️ 系统存在问题，需要进一步检查")
        if not backend_success:
            print("   🔧 后端功能需要修复")
        if not frontend_port:
            print("   🎨 前端应用需要启动")
    
    print("\n" + "=" * 80)
    print("🎯 课程班级关联功能测试完成！")
    print("=" * 80)

if __name__ == "__main__":
    main()
