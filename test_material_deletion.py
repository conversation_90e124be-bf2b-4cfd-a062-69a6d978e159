import requests
import time

def test_material_deletion():
    """Test material deletion functionality"""
    api_url = "http://localhost:8000/api/v1"
    
    # Login
    login_response = requests.post(f"{api_url}/auth/token", 
                                 json={"username": "teacher_zhang", "password": "teacher123456"},
                                 timeout=10)
    
    if login_response.status_code != 200:
        print("❌ 登录失败")
        return False
    
    token = login_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    
    # Get courses
    courses_response = requests.get(f"{api_url}/courses/", headers=headers, timeout=10)
    courses = courses_response.json()
    
    if not courses:
        print("❌ 没有可用的课程")
        return False
    
    course_id = courses[0]['id']
    print(f"✅ 使用课程ID: {course_id}")
    
    # Create a test material
    params = {
        "course_id": course_id,
        "original_filename": "deletion_test.txt",
        "file_type": "text"
    }
    
    upload_creds_response = requests.post(f"{api_url}/materials/upload-credentials", 
                                        params=params, headers=headers, timeout=10)
    
    if upload_creds_response.status_code != 200:
        print("❌ 获取上传凭证失败")
        return False
    
    upload_info = upload_creds_response.json()
    upload_url = upload_info['upload_url']
    object_name = upload_info['object_name']
    
    # Upload file
    test_content = "这是一个用于测试删除功能的文件"
    upload_response = requests.put(upload_url, 
                                 data=test_content.encode('utf-8'),
                                 headers={"Content-Type": "text/plain"},
                                 timeout=30)
    
    if upload_response.status_code not in [200, 204]:
        print("❌ 文件上传失败")
        return False
    
    # Create material record
    material_data = {
        "title": "删除测试材料",
        "description": "用于测试删除功能",
        "file_type": "text",
        "file_path_minio": object_name,
        "file_size_bytes": len(test_content.encode('utf-8'))
    }
    
    material_response = requests.post(f"{api_url}/materials/{course_id}",
                                    json=material_data, headers=headers, timeout=10)
    
    if material_response.status_code != 201:
        print("❌ 材料创建失败")
        return False
    
    material = material_response.json()
    material_id = material['id']
    print(f"✅ 材料创建成功，ID: {material_id}")
    
    # Verify material exists
    get_response = requests.get(f"{api_url}/materials/{material_id}",
                              headers=headers, timeout=10)
    
    if get_response.status_code != 200:
        print("❌ 材料创建后无法获取")
        return False
    
    print("✅ 材料存在确认")
    
    # Delete the material
    delete_response = requests.delete(f"{api_url}/materials/{material_id}",
                                    headers=headers, timeout=10)
    
    print(f"🗑️ 删除请求状态码: {delete_response.status_code}")
    
    if delete_response.status_code not in [200, 204]:
        print("❌ 材料删除失败")
        return False
    
    print("✅ 删除请求成功")
    
    # Wait a moment for the deletion to be processed
    time.sleep(2)
    
    # Verify material is deleted
    get_response_after_delete = requests.get(f"{api_url}/materials/{material_id}",
                                           headers=headers, timeout=10)
    
    print(f"🔍 删除后查询状态码: {get_response_after_delete.status_code}")
    
    if get_response_after_delete.status_code == 404:
        print("✅ 材料已成功删除")
        return True
    else:
        print("❌ 材料删除后仍然存在")
        print(f"响应内容: {get_response_after_delete.text}")
        return False

if __name__ == "__main__":
    print("🗑️ 材料删除功能测试")
    print("=" * 40)
    
    success = test_material_deletion()
    
    if success:
        print("\n🎉 材料删除功能正常工作！")
    else:
        print("\n⚠️ 材料删除功能存在问题")
    
    print("=" * 40)
