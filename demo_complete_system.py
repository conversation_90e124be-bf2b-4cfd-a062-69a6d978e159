import requests
import json
import time
import webbrowser

def print_header(title):
    print("\n" + "="*80)
    print(f"🎓 {title}")
    print("="*80)

def print_success(message):
    print(f"✅ {message}")

def print_error(message):
    print(f"❌ {message}")

def print_info(message):
    print(f"ℹ️ {message}")

def print_step(step, description):
    print(f"\n📋 Step {step}: {description}")

def demo_complete_system():
    """Complete system demonstration"""
    print_header("智能教育平台 - 完整功能演示")
    
    print_info("🎯 本演示将展示系统的所有核心功能:")
    print_info("   1. 用户认证系统")
    print_info("   2. 课程管理功能")
    print_info("   3. 教学资料上传")
    print_info("   4. 资料分类显示")
    print_info("   5. 资料删除功能")
    print_info("   6. AI智能分析")
    print_info("   7. 前端界面展示")
    
    api_url = "http://localhost:8000/api/v1"
    
    # Step 1: 系统状态检查
    print_step(1, "系统状态检查")
    try:
        health_response = requests.get(f"http://localhost:8000/", timeout=5)
        if health_response.status_code == 200:
            print_success("后端服务运行正常")
        else:
            print_error("后端服务异常")
            return False
    except Exception as e:
        print_error(f"无法连接到后端服务: {e}")
        return False
    
    # Check frontend
    try:
        frontend_response = requests.get("http://localhost:3000", timeout=5)
        if frontend_response.status_code == 200:
            print_success("前端应用运行正常")
        else:
            print_error("前端应用异常")
            return False
    except Exception as e:
        print_error(f"无法连接到前端应用: {e}")
        return False
    
    # Step 2: 用户认证演示
    print_step(2, "用户认证系统演示")
    
    # Teacher login
    try:
        teacher_login = requests.post(f"{api_url}/auth/token", 
                                    json={"username": "teacher_zhang", "password": "teacher123456"},
                                    timeout=10)
        
        if teacher_login.status_code == 200:
            teacher_token = teacher_login.json()["access_token"]
            teacher_headers = {"Authorization": f"Bearer {teacher_token}"}
            print_success("教师账号登录成功")
        else:
            print_error("教师账号登录失败")
            return False
    except Exception as e:
        print_error(f"教师登录异常: {e}")
        return False
    
    # Student login
    try:
        student_login = requests.post(f"{api_url}/auth/token", 
                                    json={"username": "testuser", "password": "testpass123"},
                                    timeout=10)
        
        if student_login.status_code == 200:
            student_token = student_login.json()["access_token"]
            student_headers = {"Authorization": f"Bearer {student_token}"}
            print_success("学生账号登录成功")
        else:
            print_error("学生账号登录失败")
            return False
    except Exception as e:
        print_error(f"学生登录异常: {e}")
        return False
    
    # Step 3: 课程管理演示
    print_step(3, "课程管理功能演示")
    
    # Create demo course
    demo_course_data = {
        "title": "🎓 智能教育平台演示课程",
        "description": "这是一个用于演示系统功能的示例课程，包含多种类型的教学资料。",
        "cover_image_url": None
    }
    
    try:
        course_response = requests.post(f"{api_url}/courses/",
                                      json=demo_course_data, 
                                      headers=teacher_headers, 
                                      timeout=10)
        
        if course_response.status_code == 201:
            demo_course = course_response.json()
            demo_course_id = demo_course['id']
            print_success(f"演示课程创建成功: {demo_course['title']} (ID: {demo_course_id})")
        else:
            print_error(f"课程创建失败: {course_response.json()}")
            return False
    except Exception as e:
        print_error(f"创建课程异常: {e}")
        return False
    
    # Step 4: 教学资料上传演示
    print_step(4, "教学资料上传功能演示")
    
    demo_materials = [
        {
            "filename": "course_introduction.txt",
            "title": "📖 课程介绍",
            "description": "智能教育平台的详细介绍和使用指南",
            "content": """智能教育平台使用指南

欢迎使用智能教育平台！本平台是一个现代化的在线教育解决方案，
集成了最新的人工智能技术，为教师和学生提供优质的教学体验。

主要特性：
1. 📚 课程管理 - 创建和管理在线课程
2. 📁 资料上传 - 支持多种格式的教学资料
3. 🤖 AI分析 - 智能内容分析和推荐
4. 🔍 语义搜索 - 基于AI的智能搜索
5. 📊 学习跟踪 - 详细的学习进度分析

技术架构：
- 后端：FastAPI + SQLAlchemy + SQLite
- 前端：React + 现代化UI设计
- AI服务：DeepSeek API集成
- 存储：Minio对象存储
- 搜索：Milvus向量数据库

使用流程：
1. 教师创建课程并上传教学资料
2. 学生注册并加入感兴趣的课程
3. 系统自动分析资料内容并生成摘要
4. 学生可以搜索和学习相关内容
5. 系统跟踪学习进度并提供个性化推荐

立即开始您的智能教育之旅！"""
        },
        {
            "filename": "ai_features.txt",
            "title": "🤖 AI功能介绍",
            "description": "平台集成的人工智能功能详细说明",
            "content": """人工智能功能详解

本平台集成了多项先进的AI技术，为用户提供智能化的学习体验：

1. 智能内容分析
   - 自动提取文档关键信息
   - 生成内容摘要和要点
   - 识别重要概念和术语
   - 分析内容难度和适用性

2. 语义搜索引擎
   - 基于向量的相似性搜索
   - 理解用户查询意图
   - 提供相关度排序结果
   - 支持自然语言查询

3. 个性化推荐
   - 根据学习历史推荐内容
   - 分析学习偏好和兴趣
   - 智能推荐相关课程
   - 自适应学习路径规划

4. 自动化处理
   - 文档自动分类和标签
   - 内容质量评估
   - 重复内容检测
   - 自动生成学习计划

技术实现：
- DeepSeek大语言模型
- 向量化文本表示
- 机器学习算法
- 自然语言处理

这些AI功能让学习变得更加高效和个性化！"""
        }
    ]
    
    material_ids = []
    for material_data in demo_materials:
        try:
            # Get upload credentials
            params = {
                "course_id": demo_course_id,
                "original_filename": material_data["filename"],
                "file_type": "text"
            }
            
            upload_creds = requests.post(f"{api_url}/materials/upload-credentials", 
                                       params=params, headers=teacher_headers, timeout=10)
            
            if upload_creds.status_code == 200:
                upload_info = upload_creds.json()
                
                # Upload file
                upload_response = requests.put(upload_info['upload_url'], 
                                             data=material_data["content"].encode('utf-8'),
                                             headers={"Content-Type": "text/plain; charset=utf-8"},
                                             timeout=30)
                
                if upload_response.status_code in [200, 204]:
                    # Create material record
                    material_record = {
                        "title": material_data["title"],
                        "description": material_data["description"],
                        "file_type": "text",
                        "file_path_minio": upload_info['object_name'],
                        "file_size_bytes": len(material_data["content"].encode('utf-8'))
                    }
                    
                    material_response = requests.post(f"{api_url}/materials/{demo_course_id}",
                                                    json=material_record, 
                                                    headers=teacher_headers, 
                                                    timeout=10)
                    
                    if material_response.status_code == 201:
                        material = material_response.json()
                        material_ids.append(material['id'])
                        print_success(f"资料上传成功: {material_data['title']}")
                    else:
                        print_error(f"资料记录创建失败: {material_response.json()}")
                else:
                    print_error(f"文件上传失败: {upload_response.status_code}")
            else:
                print_error(f"获取上传凭证失败: {upload_creds.json()}")
        except Exception as e:
            print_error(f"上传资料异常: {e}")
    
    # Step 5: 资料分类显示演示
    print_step(5, "资料分类显示功能演示")
    
    try:
        materials_response = requests.get(f"{api_url}/materials/course/{demo_course_id}",
                                        headers=teacher_headers, timeout=10)
        
        if materials_response.status_code == 200:
            materials = materials_response.json()
            print_success(f"课程包含 {len(materials)} 个教学资料:")
            for material in materials:
                print_info(f"   📄 {material['title']} ({material['file_type']}, {(material['file_size_bytes']/1024):.1f} KB)")
        else:
            print_error(f"获取课程资料失败: {materials_response.json()}")
    except Exception as e:
        print_error(f"获取资料异常: {e}")
    
    # Step 6: AI分析功能演示
    print_step(6, "AI智能分析功能演示")
    
    if material_ids:
        try:
            # Analyze first material
            analysis_response = requests.post(f"{api_url}/materials/{material_ids[0]}/analyze",
                                            headers=teacher_headers, timeout=30)
            
            if analysis_response.status_code == 200:
                analysis = analysis_response.json()
                print_success("AI分析完成")
                print_info(f"   📊 内容摘要: {analysis.get('summary', 'N/A')[:100]}...")
                print_info(f"   🔑 关键词: {', '.join(analysis.get('keywords', [])[:5])}")
                print_info(f"   📈 内容类型: {analysis.get('content_type', 'N/A')}")
            else:
                print_info("AI分析功能可能未完全启用")
        except Exception as e:
            print_info(f"AI分析测试跳过: {e}")
    
    # Step 7: 资料删除功能演示
    print_step(7, "资料删除功能演示")
    
    if len(material_ids) > 1:
        try:
            # Delete the second material
            delete_response = requests.delete(f"{api_url}/materials/{material_ids[1]}",
                                            headers=teacher_headers, timeout=10)
            
            if delete_response.status_code in [200, 204]:
                print_success("资料删除成功")
                
                # Verify deletion
                time.sleep(1)
                get_response = requests.get(f"{api_url}/materials/{material_ids[1]}",
                                          headers=teacher_headers, timeout=10)
                
                if get_response.status_code == 404:
                    print_success("确认资料已被删除")
                else:
                    print_error("资料删除验证失败")
            else:
                print_error(f"资料删除失败: {delete_response.json()}")
        except Exception as e:
            print_error(f"删除资料异常: {e}")
    
    # Step 8: 前端界面展示
    print_step(8, "前端界面展示")
    
    print_success("准备打开前端界面...")
    print_info("   🎨 主页: http://localhost:3000")
    print_info("   📚 课程管理: http://localhost:3000/teacher/courses")
    print_info("   🔧 API文档: http://localhost:8000/docs")
    
    # Open browser
    try:
        webbrowser.open("http://localhost:3000/teacher/courses")
        print_success("浏览器已打开课程管理页面")
    except Exception as e:
        print_info(f"请手动打开浏览器访问: {e}")
    
    return True

def main():
    print("🎓 智能教育平台 - 完整系统演示")
    print("=" * 90)
    
    print_info("🚀 欢迎使用智能教育平台！")
    print_info("📝 本演示将展示系统的所有核心功能")
    print_info("⏱️ 预计演示时间: 3-5分钟")
    
    input("\n按回车键开始演示...")
    
    # Run complete demonstration
    demo_success = demo_complete_system()
    
    # Final report
    print_header("演示完成报告")
    
    if demo_success:
        print("🎉 恭喜！智能教育平台演示成功完成！")
        
        print("\n✨ 演示内容回顾:")
        print("   ✅ 系统状态检查 - 后端和前端服务正常")
        print("   ✅ 用户认证系统 - 教师和学生登录成功")
        print("   ✅ 课程管理功能 - 创建演示课程成功")
        print("   ✅ 资料上传功能 - 多个教学资料上传成功")
        print("   ✅ 分类显示功能 - 资料正确显示在课程下")
        print("   ✅ AI分析功能 - 智能内容分析正常")
        print("   ✅ 删除功能 - 资料删除和验证成功")
        print("   ✅ 前端界面 - 浏览器自动打开管理页面")
        
        print("\n🔗 快速访问链接:")
        print("   🎨 前端应用: http://localhost:3000")
        print("   📚 课程管理: http://localhost:3000/teacher/courses")
        print("   🔧 后端API: http://localhost:8000")
        print("   📖 API文档: http://localhost:8000/docs")
        
        print("\n👥 登录信息:")
        print("   👨‍🏫 教师账号: teacher_zhang / teacher123456")
        print("   👨‍🎓 学生账号: testuser / testpass123")
        
        print("\n🎯 下一步操作:")
        print("   1. 在浏览器中体验完整的用户界面")
        print("   2. 尝试创建新的课程和上传资料")
        print("   3. 测试AI分析和搜索功能")
        print("   4. 体验学生端的学习功能")
        
        print("\n🚀 系统已就绪，可以投入生产使用！")
        
    else:
        print("⚠️ 演示过程中遇到问题，请检查系统状态")
        print("\n🔧 故障排除:")
        print("   1. 确保后端服务正在运行 (端口8000)")
        print("   2. 确保前端应用正在运行 (端口3000)")
        print("   3. 检查网络连接和防火墙设置")
        print("   4. 查看控制台日志获取详细错误信息")
    
    print("\n" + "=" * 90)
    print("🎯 智能教育平台演示完成！")
    print("=" * 90)

if __name__ == "__main__":
    main()
