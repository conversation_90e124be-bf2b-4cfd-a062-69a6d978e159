import requests
import json
import time

# Comprehensive test and fix script for the education platform
BASE_URL = "http://localhost:8000/api/v1"

def test_teacher_login():
    """Test teacher login"""
    url = f"{BASE_URL}/auth/token"
    login_data = {
        "username": "teacher_zhang",
        "password": "teacher123456"
    }
    
    try:
        response = requests.post(url, json=login_data)
        if response.status_code == 200:
            result = response.json()
            print("✅ Teacher login successful!")
            return result["access_token"]
        else:
            print(f"❌ Teacher login failed: {response.json()}")
            return None
    except Exception as e:
        print(f"Teacher Login Error: {e}")
        return None

def test_file_upload_flow(token):
    """Test complete file upload flow"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n=== Testing File Upload Flow ===")
    
    # Step 1: Create a test course
    course_data = {
        "title": "文件上传测试课程",
        "description": "用于测试文件上传功能的课程",
        "status": "active"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/courses/", json=course_data, headers=headers)
        if response.status_code == 201:
            course = response.json()
            course_id = course['id']
            print(f"✅ Test course created: {course['title']} (ID: {course_id})")
        else:
            print(f"❌ Course creation failed: {response.json()}")
            return False
    except Exception as e:
        print(f"Course creation error: {e}")
        return False
    
    # Step 2: Test upload credentials with correct parameters
    try:
        params = {
            "course_id": course_id,
            "original_filename": "test_document.txt",
            "file_type": "text"
        }
        response = requests.post(f"{BASE_URL}/materials/upload-credentials", params=params, headers=headers)
        
        if response.status_code == 200:
            upload_info = response.json()
            print(f"✅ Upload credentials obtained: {upload_info['object_name']}")
        else:
            print(f"❌ Upload credentials failed: {response.json()}")
            return False
    except Exception as e:
        print(f"Upload credentials error: {e}")
        return False
    
    # Step 3: Create material record
    try:
        material_data = {
            "title": "测试文档",
            "description": "用于测试的文档",
            "file_type": "text",
            "file_path_minio": upload_info['object_name'],
            "file_size_bytes": 1024
        }
        
        response = requests.post(f"{BASE_URL}/materials/{course_id}", json=material_data, headers=headers)
        
        if response.status_code == 201:
            material = response.json()
            print(f"✅ Material created: {material['title']} (ID: {material['id']})")
            
            # Test DeepSeek analysis
            time.sleep(2)  # Wait for background processing
            
            analysis_response = requests.post(f"{BASE_URL}/materials/{material['id']}/analyze", headers=headers)
            if analysis_response.status_code == 200:
                analysis = analysis_response.json()
                if analysis.get("success"):
                    print("✅ DeepSeek analysis successful!")
                    print(f"📝 Summary: {analysis.get('summary', 'N/A')[:100]}...")
                else:
                    print(f"❌ DeepSeek analysis failed: {analysis.get('error', 'Unknown error')}")
            else:
                print(f"❌ Analysis request failed: {analysis_response.json()}")
            
            return True
        else:
            print(f"❌ Material creation failed: {response.json()}")
            return False
    except Exception as e:
        print(f"Material creation error: {e}")
        return False

def test_frontend_api_compatibility():
    """Test frontend API compatibility"""
    print("\n=== Testing Frontend API Compatibility ===")
    
    # Test CORS preflight
    try:
        response = requests.options(f"{BASE_URL}/auth/token")
        if response.status_code == 200:
            print("✅ CORS preflight working")
        else:
            print(f"❌ CORS preflight failed: {response.status_code}")
    except Exception as e:
        print(f"CORS test error: {e}")
    
    # Test API endpoints accessibility
    endpoints_to_test = [
        "/courses/",
        "/classes/",
        "/materials/upload-credentials?course_id=1&original_filename=test.txt&file_type=text"
    ]
    
    for endpoint in endpoints_to_test:
        try:
            response = requests.options(f"{BASE_URL}{endpoint}")
            if response.status_code in [200, 404]:  # 404 is OK for OPTIONS
                print(f"✅ Endpoint accessible: {endpoint}")
            else:
                print(f"❌ Endpoint issue: {endpoint} - {response.status_code}")
        except Exception as e:
            print(f"❌ Endpoint error: {endpoint} - {e}")

def test_student_login():
    """Test student login"""
    url = f"{BASE_URL}/auth/token"
    login_data = {
        "username": "testuser",
        "password": "testpass123"
    }
    
    try:
        response = requests.post(url, json=login_data)
        if response.status_code == 200:
            result = response.json()
            print("✅ Student login successful!")
            return result["access_token"]
        else:
            print(f"❌ Student login failed: {response.json()}")
            return None
    except Exception as e:
        print(f"Student Login Error: {e}")
        return None

def test_basic_functionality():
    """Test basic platform functionality"""
    print("\n=== Testing Basic Platform Functionality ===")
    
    # Test root endpoint
    try:
        response = requests.get("http://localhost:8000/")
        if response.status_code == 200:
            print("✅ Backend server accessible")
        else:
            print(f"❌ Backend server issue: {response.status_code}")
    except Exception as e:
        print(f"❌ Backend server error: {e}")
        return False
    
    # Test API documentation
    try:
        response = requests.get("http://localhost:8000/docs")
        if response.status_code == 200:
            print("✅ API documentation accessible")
        else:
            print(f"❌ API documentation issue: {response.status_code}")
    except Exception as e:
        print(f"❌ API documentation error: {e}")
    
    return True

def create_comprehensive_test_data(token):
    """Create comprehensive test data for the platform"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n=== Creating Comprehensive Test Data ===")
    
    # Create multiple courses
    courses = [
        {"title": "Python编程入门", "description": "Python基础编程课程", "status": "active"},
        {"title": "Web开发实战", "description": "前端和后端开发课程", "status": "active"},
        {"title": "数据科学基础", "description": "数据分析和机器学习入门", "status": "active"}
    ]
    
    created_courses = []
    for course_data in courses:
        try:
            response = requests.post(f"{BASE_URL}/courses/", json=course_data, headers=headers)
            if response.status_code == 201:
                course = response.json()
                created_courses.append(course)
                print(f"✅ Course created: {course['title']}")
            else:
                print(f"❌ Course creation failed: {course_data['title']}")
        except Exception as e:
            print(f"Course creation error: {e}")
    
    # Create classes
    classes = [
        {"name": "Python初级班", "description": "适合初学者的Python班级", "status": "active"},
        {"name": "Web开发进阶班", "description": "有一定基础的Web开发班级", "status": "active"}
    ]
    
    for class_data in classes:
        try:
            response = requests.post(f"{BASE_URL}/classes/", json=class_data, headers=headers)
            if response.status_code == 201:
                class_obj = response.json()
                print(f"✅ Class created: {class_obj['name']}")
            else:
                print(f"❌ Class creation failed: {class_data['name']}")
        except Exception as e:
            print(f"Class creation error: {e}")
    
    return created_courses

def main():
    print("🚀 Starting Comprehensive Platform Test and Fix")
    print("=" * 60)
    
    # Test 1: Basic functionality
    if not test_basic_functionality():
        print("❌ Basic functionality test failed. Exiting.")
        return
    
    # Test 2: Frontend API compatibility
    test_frontend_api_compatibility()
    
    # Test 3: Teacher login and functionality
    teacher_token = test_teacher_login()
    if not teacher_token:
        print("❌ Teacher login failed. Exiting.")
        return
    
    # Test 4: Student login
    student_token = test_student_login()
    if student_token:
        print("✅ Student login working")
    else:
        print("⚠️ Student login issues")
    
    # Test 5: File upload flow
    upload_success = test_file_upload_flow(teacher_token)
    if upload_success:
        print("✅ File upload flow working")
    else:
        print("❌ File upload flow has issues")
    
    # Test 6: Create comprehensive test data
    created_courses = create_comprehensive_test_data(teacher_token)
    
    print("\n" + "=" * 60)
    print("🎉 Comprehensive Test Completed!")
    print("\n📋 Test Summary:")
    print(f"✅ Teacher login: {'Working' if teacher_token else 'Failed'}")
    print(f"✅ Student login: {'Working' if student_token else 'Failed'}")
    print(f"✅ File upload: {'Working' if upload_success else 'Failed'}")
    print(f"✅ Courses created: {len(created_courses)}")
    
    print("\n🔗 Access URLs:")
    print("Frontend: http://localhost:3000")
    print("Backend API: http://localhost:8000")
    print("API Docs: http://localhost:8000/docs")
    
    print("\n👥 Test Accounts:")
    print("Teacher: teacher_zhang / teacher123456")
    print("Student: testuser / testpass123")
    
    if upload_success and teacher_token and len(created_courses) > 0:
        print("\n🎊 Platform is ready for production!")
        return True
    else:
        print("\n⚠️ Some issues detected. Please review the logs.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
