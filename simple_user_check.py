import sqlite3
import requests
import json

def check_database_users():
    """Check users in the database using direct SQLite"""
    print("🔍 Checking Database Users")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('education_platform.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, username, email, role, is_active, password_hash FROM users")
        users = cursor.fetchall()
        
        print(f"📊 Found {len(users)} users in database:")
        for user in users:
            user_id, username, email, role, is_active, password_hash = user
            print(f"   👤 ID: {user_id}, Username: {username}, Role: {role}, Active: {bool(is_active)}")
            print(f"      📧 Email: {email}")
            print(f"      🔐 Password hash: {password_hash[:50]}...")
        
        conn.close()
        return users
        
    except Exception as e:
        print(f"❌ SQLite query error: {e}")
        return []

def test_login_api():
    """Test login API directly"""
    print("\n🌐 Testing Login API")
    print("=" * 40)
    
    test_credentials = [
        {"username": "teacher_zhang", "password": "teacher123456"},
        {"username": "testuser", "password": "testpass123"},
        {"username": "admin", "password": "admin123456"}
    ]
    
    for creds in test_credentials:
        try:
            response = requests.post("http://localhost:8000/api/v1/auth/token", 
                                   json=creds, timeout=5)
            
            print(f"\n🔑 Testing {creds['username']}:")
            print(f"   📊 Status Code: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ Login successful")
                print(f"   🎫 Token: {result['access_token'][:50]}...")
                print(f"   🏷️ Token Type: {result['token_type']}")
            else:
                try:
                    error = response.json()
                    print(f"   ❌ Login failed: {error.get('detail', 'Unknown error')}")
                    print(f"   📄 Full response: {json.dumps(error, indent=2)}")
                except:
                    print(f"   ❌ Login failed: {response.text}")
                    
        except Exception as e:
            print(f"   ❌ API error: {e}")

def create_teacher_user_via_api():
    """Create teacher user via registration API"""
    print("\n🔧 Creating Teacher User via API")
    print("=" * 40)
    
    teacher_data = {
        "username": "teacher_zhang",
        "email": "<EMAIL>",
        "password": "teacher123456",
        "full_name": "张老师",
        "role": "teacher"
    }
    
    try:
        response = requests.post("http://localhost:8000/api/v1/auth/register", 
                               json=teacher_data, timeout=10)
        
        print(f"📊 Registration Status Code: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()
            print(f"✅ Teacher user created successfully!")
            print(f"   👤 ID: {result['id']}")
            print(f"   📧 Email: {result['email']}")
            print(f"   🏷️ Role: {result['role']}")
            print(f"   ✅ Active: {result['is_active']}")
            return True
        else:
            try:
                error = response.json()
                print(f"❌ Registration failed: {error.get('detail', 'Unknown error')}")
                print(f"📄 Full response: {json.dumps(error, indent=2)}")
            except:
                print(f"❌ Registration failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Registration error: {e}")
        return False

def delete_existing_teacher():
    """Delete existing teacher user from database"""
    print("\n🗑️ Deleting Existing Teacher User")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('education_platform.db')
        cursor = conn.cursor()
        
        # Check if teacher_zhang exists
        cursor.execute("SELECT id FROM users WHERE username = 'teacher_zhang'")
        existing = cursor.fetchone()
        
        if existing:
            cursor.execute("DELETE FROM users WHERE username = 'teacher_zhang'")
            conn.commit()
            print(f"✅ Deleted existing teacher_zhang user (ID: {existing[0]})")
        else:
            print("ℹ️ No existing teacher_zhang user found")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Delete error: {e}")
        return False

def check_backend_status():
    """Check if backend is running"""
    print("🔍 Checking Backend Status")
    print("=" * 40)
    
    try:
        response = requests.get("http://localhost:8000/", timeout=5)
        if response.status_code == 200:
            print("✅ Backend is running")
            return True
        else:
            print(f"⚠️ Backend response: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend not accessible: {e}")
        return False

def main():
    print("🔍 Simple User Authentication Diagnosis")
    print("=" * 50)
    
    # Step 1: Check backend status
    if not check_backend_status():
        print("\n❌ Backend is not running. Please start the backend first.")
        return
    
    # Step 2: Check existing users
    users = check_database_users()
    
    # Step 3: Test login with existing users
    test_login_api()
    
    # Step 4: Check if we need to create teacher user
    teacher_exists = any(user[1] == 'teacher_zhang' for user in users)
    
    if not teacher_exists:
        print("\n🤔 Teacher user not found. Creating new teacher user...")
        if create_teacher_user_via_api():
            print("\n🔄 Testing login after user creation:")
            test_login_api()
    else:
        print("\n🤔 Teacher user exists but login failed. Recreating...")
        if delete_existing_teacher():
            if create_teacher_user_via_api():
                print("\n🔄 Testing login after user recreation:")
                test_login_api()
    
    print("\n" + "=" * 50)
    print("🎯 Diagnosis Complete!")
    print("\n📋 Summary:")
    print("   🔗 Frontend: http://localhost:3002/teacher/courses")
    print("   🔧 Backend: http://localhost:8000")
    print("   👤 Teacher: teacher_zhang / teacher123456")
    print("   👤 Student: testuser / testpass123")

if __name__ == "__main__":
    main()
