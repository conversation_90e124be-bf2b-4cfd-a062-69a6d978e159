import requests
import time
import json
from datetime import datetime

# Production readiness test for Education Platform
BACKEND_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:3003"
API_BASE = f"{BACKEND_URL}/api/v1"

def print_header(title):
    print("\n" + "="*70)
    print(f"🚀 {title}")
    print("="*70)

def print_success(message):
    print(f"✅ {message}")

def print_error(message):
    print(f"❌ {message}")

def print_warning(message):
    print(f"⚠️ {message}")

def print_info(message):
    print(f"ℹ️ {message}")

def test_system_health():
    """Test overall system health"""
    print_header("系统健康检查")
    
    health_status = {}
    
    # Backend health
    try:
        response = requests.get(f"{BACKEND_URL}/", timeout=5)
        if response.status_code == 200:
            print_success("后端服务运行正常")
            health_status['backend'] = True
        else:
            print_error(f"后端服务异常: {response.status_code}")
            health_status['backend'] = False
    except Exception as e:
        print_error(f"后端服务连接失败: {e}")
        health_status['backend'] = False
    
    # Frontend health
    try:
        response = requests.get(FRONTEND_URL, timeout=5)
        if response.status_code == 200:
            print_success("前端应用运行正常")
            health_status['frontend'] = True
        else:
            print_error(f"前端应用异常: {response.status_code}")
            health_status['frontend'] = False
    except Exception as e:
        print_error(f"前端应用连接失败: {e}")
        health_status['frontend'] = False
    
    # API documentation
    try:
        response = requests.get(f"{BACKEND_URL}/docs", timeout=5)
        if response.status_code == 200:
            print_success("API文档可访问")
            health_status['docs'] = True
        else:
            print_warning("API文档访问异常")
            health_status['docs'] = False
    except Exception as e:
        print_warning(f"API文档访问失败: {e}")
        health_status['docs'] = False
    
    return health_status

def test_authentication_system():
    """Test authentication system comprehensively"""
    print_header("认证系统测试")
    
    auth_status = {}
    
    # Test teacher authentication
    print("\n📋 教师认证测试")
    try:
        response = requests.post(f"{API_BASE}/auth/token", 
                               json={"username": "teacher_zhang", "password": "teacher123456"},
                               timeout=10)
        
        if response.status_code == 200:
            teacher_token = response.json()["access_token"]
            print_success("教师登录成功")
            
            # Validate token
            headers = {"Authorization": f"Bearer {teacher_token}"}
            profile_response = requests.get(f"{API_BASE}/users/me", headers=headers, timeout=10)
            
            if profile_response.status_code == 200:
                user_info = profile_response.json()
                print_success(f"Token验证成功 - 用户: {user_info['username']}, 角色: {user_info['role']}")
                auth_status['teacher'] = True
            else:
                print_error("教师Token验证失败")
                auth_status['teacher'] = False
        else:
            print_error(f"教师登录失败: {response.json().get('detail', 'Unknown error')}")
            auth_status['teacher'] = False
            
    except Exception as e:
        print_error(f"教师认证测试异常: {e}")
        auth_status['teacher'] = False
    
    # Test student authentication
    print("\n📋 学生认证测试")
    try:
        response = requests.post(f"{API_BASE}/auth/token", 
                               json={"username": "testuser", "password": "testpass123"},
                               timeout=10)
        
        if response.status_code == 200:
            student_token = response.json()["access_token"]
            print_success("学生登录成功")
            
            # Validate token
            headers = {"Authorization": f"Bearer {student_token}"}
            profile_response = requests.get(f"{API_BASE}/users/me", headers=headers, timeout=10)
            
            if profile_response.status_code == 200:
                user_info = profile_response.json()
                print_success(f"Token验证成功 - 用户: {user_info['username']}, 角色: {user_info['role']}")
                auth_status['student'] = True
            else:
                print_error("学生Token验证失败")
                auth_status['student'] = False
        else:
            print_error(f"学生登录失败: {response.json().get('detail', 'Unknown error')}")
            auth_status['student'] = False
            
    except Exception as e:
        print_error(f"学生认证测试异常: {e}")
        auth_status['student'] = False
    
    return auth_status

def test_core_functionality():
    """Test core platform functionality"""
    print_header("核心功能测试")
    
    # Get teacher token first
    try:
        response = requests.post(f"{API_BASE}/auth/token", 
                               json={"username": "teacher_zhang", "password": "teacher123456"},
                               timeout=10)
        teacher_token = response.json()["access_token"]
        headers = {"Authorization": f"Bearer {teacher_token}"}
    except:
        print_error("无法获取教师Token，跳过核心功能测试")
        return {}
    
    functionality_status = {}
    
    # Test courses
    print("\n📋 课程管理测试")
    try:
        response = requests.get(f"{API_BASE}/courses/", headers=headers, timeout=10)
        if response.status_code == 200:
            courses = response.json()
            print_success(f"课程管理正常 (共{len(courses)}门课程)")
            functionality_status['courses'] = True
        else:
            print_error(f"课程管理异常: {response.status_code}")
            functionality_status['courses'] = False
    except Exception as e:
        print_error(f"课程管理测试失败: {e}")
        functionality_status['courses'] = False
    
    # Test classes
    print("\n📋 班级管理测试")
    try:
        response = requests.get(f"{API_BASE}/classes/", headers=headers, timeout=10)
        if response.status_code == 200:
            classes = response.json()
            print_success(f"班级管理正常 (共{len(classes)}个班级)")
            functionality_status['classes'] = True
        else:
            print_error(f"班级管理异常: {response.status_code}")
            functionality_status['classes'] = False
    except Exception as e:
        print_error(f"班级管理测试失败: {e}")
        functionality_status['classes'] = False
    
    # Test file upload
    print("\n📋 文件上传测试")
    try:
        if courses and len(courses) > 0:
            course_id = courses[0]['id']
            response = requests.post(
                f"{API_BASE}/materials/upload-credentials",
                params={
                    "course_id": course_id,
                    "original_filename": "production_test.txt",
                    "file_type": "text"
                },
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                print_success("文件上传功能正常")
                functionality_status['upload'] = True
            else:
                print_error(f"文件上传功能异常: {response.status_code}")
                functionality_status['upload'] = False
        else:
            print_warning("没有可用课程进行文件上传测试")
            functionality_status['upload'] = False
    except Exception as e:
        print_error(f"文件上传测试失败: {e}")
        functionality_status['upload'] = False
    
    return functionality_status

def test_ai_integration():
    """Test AI integration"""
    print_header("AI集成测试")
    
    try:
        from app.utils.deepseek_client import deepseek_client
        
        sample_content = """
        智能教育平台生产就绪测试
        
        本测试验证以下功能：
        1. 用户认证和授权
        2. 课程和班级管理
        3. 文件上传和处理
        4. AI文档分析
        
        测试目标：
        - 确保所有核心功能正常工作
        - 验证系统稳定性和性能
        - 确认生产环境就绪状态
        """
        
        result = deepseek_client.analyze_document(
            file_content=sample_content.encode('utf-8'),
            file_name="production_test.txt",
            file_type="text"
        )
        
        if result.get("success"):
            print_success("DeepSeek AI集成正常")
            print_info(f"摘要: {result.get('summary', 'N/A')[:100]}...")
            print_info(f"关键点: {len(result.get('key_points', []))}个")
            print_info(f"主题: {len(result.get('topics', []))}个")
            print_info(f"难度: {result.get('difficulty_level', 'N/A')}")
            return True
        else:
            print_error(f"DeepSeek AI分析失败: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print_error(f"AI集成测试异常: {e}")
        return False

def test_security_features():
    """Test security features"""
    print_header("安全特性测试")
    
    security_status = {}
    
    # Test CORS
    print("\n📋 CORS配置测试")
    try:
        response = requests.options(f"{API_BASE}/auth/token", 
                                  headers={
                                      "Origin": FRONTEND_URL,
                                      "Access-Control-Request-Method": "POST",
                                      "Access-Control-Request-Headers": "Content-Type"
                                  }, timeout=5)
        
        if response.status_code in [200, 204]:
            print_success("CORS配置正常")
            security_status['cors'] = True
        else:
            print_warning(f"CORS配置可能有问题: {response.status_code}")
            security_status['cors'] = False
    except Exception as e:
        print_warning(f"CORS测试失败: {e}")
        security_status['cors'] = False
    
    # Test invalid credentials
    print("\n📋 安全认证测试")
    try:
        response = requests.post(f"{API_BASE}/auth/token", 
                               json={"username": "invalid", "password": "invalid"},
                               timeout=10)
        
        if response.status_code == 401:
            print_success("无效凭证正确拒绝")
            security_status['auth_security'] = True
        else:
            print_warning(f"安全认证可能有问题: {response.status_code}")
            security_status['auth_security'] = False
    except Exception as e:
        print_warning(f"安全认证测试失败: {e}")
        security_status['auth_security'] = False
    
    return security_status

def generate_production_report(health, auth, functionality, ai, security):
    """Generate comprehensive production readiness report"""
    print_header("生产就绪报告")
    
    print(f"\n📊 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔗 后端地址: {BACKEND_URL}")
    print(f"🔗 前端地址: {FRONTEND_URL}")
    
    # Calculate overall scores
    health_score = sum(health.values()) / len(health) * 100 if health else 0
    auth_score = sum(auth.values()) / len(auth) * 100 if auth else 0
    func_score = sum(functionality.values()) / len(functionality) * 100 if functionality else 0
    ai_score = 100 if ai else 0
    security_score = sum(security.values()) / len(security) * 100 if security else 0
    
    overall_score = (health_score + auth_score + func_score + ai_score + security_score) / 5
    
    print(f"\n📊 测试结果评分:")
    print(f"   🏥 系统健康: {health_score:.1f}%")
    print(f"   🔐 认证系统: {auth_score:.1f}%")
    print(f"   ⚙️ 核心功能: {func_score:.1f}%")
    print(f"   🤖 AI集成: {ai_score:.1f}%")
    print(f"   🛡️ 安全特性: {security_score:.1f}%")
    print(f"   📈 总体评分: {overall_score:.1f}%")
    
    # Production readiness assessment
    if overall_score >= 90:
        status = "🎉 生产就绪 - 优秀"
        recommendation = "系统完全准备好投入生产使用"
    elif overall_score >= 80:
        status = "✅ 生产就绪 - 良好"
        recommendation = "系统基本准备好投入生产使用，建议监控运行状况"
    elif overall_score >= 70:
        status = "⚠️ 需要改进"
        recommendation = "系统需要解决一些问题后才能投入生产使用"
    else:
        status = "❌ 不适合生产"
        recommendation = "系统存在严重问题，需要大量修复工作"
    
    print(f"\n🎯 生产就绪状态: {status}")
    print(f"💡 建议: {recommendation}")
    
    # Detailed recommendations
    print(f"\n📋 详细建议:")
    
    if health_score < 100:
        print("   🏥 系统健康: 确保所有服务正常运行")
    
    if auth_score < 100:
        print("   🔐 认证系统: 检查用户认证和授权功能")
    
    if func_score < 100:
        print("   ⚙️ 核心功能: 验证所有核心功能正常工作")
    
    if ai_score < 100:
        print("   🤖 AI集成: 检查DeepSeek AI服务连接")
    
    if security_score < 100:
        print("   🛡️ 安全特性: 加强安全配置和验证")
    
    # Access information
    print(f"\n🔗 访问信息:")
    print(f"   🎨 前端应用: {FRONTEND_URL}")
    print(f"   🔧 后端API: {BACKEND_URL}")
    print(f"   📚 API文档: {BACKEND_URL}/docs")
    print(f"   🔍 调试面板: {BACKEND_URL}/api/v1/debug/api-calls")
    print(f"   🧪 前端调试: {FRONTEND_URL}/debug")
    
    print(f"\n👥 测试账号:")
    print(f"   👨‍🏫 教师: teacher_zhang / teacher123456")
    print(f"   👨‍🎓 学生: testuser / testpass123")
    
    return overall_score >= 80

def main():
    print("🚀 智能教育平台生产就绪测试")
    print("=" * 70)
    print("正在进行全面的生产环境准备验证...")
    
    # Run all tests
    health_status = test_system_health()
    auth_status = test_authentication_system()
    functionality_status = test_core_functionality()
    ai_status = test_ai_integration()
    security_status = test_security_features()
    
    # Generate final report
    is_production_ready = generate_production_report(
        health_status, auth_status, functionality_status, ai_status, security_status
    )
    
    print("\n" + "=" * 70)
    if is_production_ready:
        print("🎉 恭喜！智能教育平台已准备好投入生产使用！")
        print("✅ 所有关键系统和功能都已验证正常工作")
        print("🚀 您可以安全地将应用部署到生产环境")
    else:
        print("⚠️ 智能教育平台需要进一步改进才能投入生产使用")
        print("🔧 请根据上述建议修复相关问题")
        print("🔄 修复后请重新运行此测试")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
