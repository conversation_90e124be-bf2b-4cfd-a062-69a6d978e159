import requests
import time

def test_frontend_login_flow():
    """Test the exact frontend login flow"""
    print("🔍 测试前端登录流程")
    print("=" * 50)
    
    # Test the exact API call that frontend makes
    frontend_url = "http://localhost:3003"
    api_url = "http://localhost:8000/api/v1"
    
    print(f"📍 前端地址: {frontend_url}")
    print(f"📍 API地址: {api_url}")
    
    # Test 1: Check if frontend can reach backend
    print("\n📋 Step 1: 测试前端到后端的连接")
    try:
        response = requests.post(f"{api_url}/auth/token", 
                               json={"username": "teacher_zhang", "password": "teacher123456"},
                               headers={
                                   "Content-Type": "application/json",
                                   "Origin": frontend_url
                               },
                               timeout=10)
        
        print(f"📊 状态码: {response.status_code}")
        print(f"📊 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 后端API调用成功")
            print(f"🎫 Token: {result['access_token'][:50]}...")
            return result['access_token']
        else:
            try:
                error = response.json()
                print(f"❌ 登录失败: {error}")
            except:
                print(f"❌ 登录失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return None

def test_frontend_cors():
    """Test CORS from frontend perspective"""
    print("\n📋 Step 2: 测试CORS配置")
    
    frontend_url = "http://localhost:3003"
    api_url = "http://localhost:8000/api/v1"
    
    # Test preflight request
    try:
        response = requests.options(f"{api_url}/auth/token",
                                  headers={
                                      "Origin": frontend_url,
                                      "Access-Control-Request-Method": "POST",
                                      "Access-Control-Request-Headers": "Content-Type"
                                  },
                                  timeout=5)
        
        print(f"📊 Preflight状态码: {response.status_code}")
        
        cors_headers = {k: v for k, v in response.headers.items() if 'access-control' in k.lower()}
        if cors_headers:
            print("📋 CORS响应头:")
            for header, value in cors_headers.items():
                print(f"   {header}: {value}")
        
        if response.status_code in [200, 204]:
            print("✅ CORS Preflight成功")
        else:
            print(f"⚠️ CORS Preflight问题: {response.status_code}")
            
    except Exception as e:
        print(f"❌ CORS测试错误: {e}")

def test_user_creation():
    """Ensure users exist and are valid"""
    print("\n📋 Step 3: 确保用户存在且有效")
    
    api_url = "http://localhost:8000/api/v1"
    
    # Test creating/updating teacher user
    teacher_data = {
        "username": "teacher_zhang",
        "email": "<EMAIL>",
        "password": "teacher123456",
        "full_name": "张老师",
        "role": "teacher"
    }
    
    try:
        response = requests.post(f"{api_url}/auth/register", json=teacher_data, timeout=10)
        
        if response.status_code == 201:
            print("✅ 教师用户创建成功")
        elif response.status_code == 400:
            print("ℹ️ 教师用户已存在")
            
            # Try to login to verify
            login_response = requests.post(f"{api_url}/auth/token",
                                         json={"username": "teacher_zhang", "password": "teacher123456"},
                                         timeout=10)
            
            if login_response.status_code == 200:
                print("✅ 教师用户登录验证成功")
            else:
                print("❌ 教师用户登录验证失败")
                print(f"   错误: {login_response.json()}")
        else:
            print(f"❌ 教师用户创建失败: {response.status_code}")
            print(f"   错误: {response.json()}")
            
    except Exception as e:
        print(f"❌ 教师用户测试错误: {e}")

def create_frontend_test_page():
    """Create a test page to debug frontend login"""
    print("\n📋 Step 4: 创建前端登录测试页面")
    
    html_content = '''
<!DOCTYPE html>
<html>
<head>
    <title>前端登录测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { padding: 10px; width: 100%; box-sizing: border-box; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 12px 24px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin: 15px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 前端登录测试</h1>
        
        <div class="form-group">
            <label>用户名:</label>
            <input type="text" id="username" value="teacher_zhang">
        </div>
        
        <div class="form-group">
            <label>密码:</label>
            <input type="password" id="password" value="teacher123456">
        </div>
        
        <button onclick="testLogin()">测试登录</button>
        <button onclick="testCORS()">测试CORS</button>
        <button onclick="testAPI()">测试API连接</button>
        
        <div id="result"></div>
    </div>
    
    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        
        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = '<div class="info">正在测试登录...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/auth/token`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ 登录成功！<br>
                            Token: ${data.access_token.substring(0, 50)}...<br>
                            类型: ${data.token_type}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ 登录失败<br>
                            状态码: ${response.status}<br>
                            错误: ${data.detail || '未知错误'}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ 网络错误: ${error.message}
                    </div>
                `;
            }
        }
        
        async function testCORS() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info">正在测试CORS...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/auth/token`, {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': window.location.origin,
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type'
                    }
                });
                
                resultDiv.innerHTML = `
                    <div class="success">
                        ✅ CORS测试完成<br>
                        状态码: ${response.status}<br>
                        允许的方法: ${response.headers.get('Access-Control-Allow-Methods') || '未设置'}<br>
                        允许的头: ${response.headers.get('Access-Control-Allow-Headers') || '未设置'}
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ CORS测试失败: ${error.message}
                    </div>
                `;
            }
        }
        
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info">正在测试API连接...</div>';
            
            try {
                const response = await fetch('http://localhost:8000/', {
                    method: 'GET'
                });
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ API连接成功<br>
                            状态码: ${response.status}<br>
                            后端服务正常运行
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ API连接失败<br>
                            状态码: ${response.status}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ API连接错误: ${error.message}
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
    '''
    
    with open('frontend_login_test.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("✅ 前端登录测试页面已创建: frontend_login_test.html")
    print("📍 请在浏览器中打开此文件进行测试")

def main():
    print("🔍 前端登录问题诊断")
    print("=" * 60)
    
    # Test backend API directly
    token = test_frontend_login_flow()
    
    # Test CORS
    test_frontend_cors()
    
    # Ensure users exist
    test_user_creation()
    
    # Create test page
    create_frontend_test_page()
    
    print("\n" + "=" * 60)
    print("🎯 诊断总结")
    print("=" * 60)
    
    if token:
        print("✅ 后端API登录功能正常")
        print("✅ 用户认证系统工作正常")
        print("✅ Token生成和验证正常")
        
        print("\n📋 如果前端仍然登录失败，请检查:")
        print("   1. 前端是否使用正确的API地址")
        print("   2. 前端是否正确处理响应")
        print("   3. 浏览器控制台是否有错误信息")
        print("   4. 网络请求是否被阻止")
        
        print(f"\n🔗 测试地址:")
        print(f"   🎨 前端应用: http://localhost:3003")
        print(f"   🔧 后端API: http://localhost:8000")
        print(f"   🧪 登录测试页: frontend_login_test.html")
        
        print(f"\n👥 测试账号:")
        print(f"   👨‍🏫 教师: teacher_zhang / teacher123456")
        print(f"   👨‍🎓 学生: testuser / testpass123")
        
    else:
        print("❌ 后端API登录功能异常")
        print("需要先修复后端登录问题")

if __name__ == "__main__":
    main()
