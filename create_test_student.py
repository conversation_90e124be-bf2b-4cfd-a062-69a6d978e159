"""
Simple script to create a test student user
"""

import requests
import json

def print_header(title):
    print("\n" + "="*60)
    print(f"🎓 {title}")
    print("="*60)

def print_success(message):
    print(f"✅ {message}")

def print_error(message):
    print(f"❌ {message}")

def print_info(message):
    print(f"ℹ️ {message}")

def create_test_student_via_api():
    """Create test student using the registration API"""
    print_header("通过API创建测试学生")
    
    api_url = "http://localhost:8000/api/v1"
    
    # Test student data
    student_data = {
        "username": "teststudent",
        "password": "student123",
        "email": "<EMAIL>",
        "full_name": "测试学生",
        "role": "student"
    }
    
    try:
        print_info(f"创建学生: {student_data['username']}")
        
        # Try to register the student
        register_response = requests.post(f"{api_url}/auth/register",
                                        json=student_data,
                                        timeout=10)
        
        if register_response.status_code == 201:
            student = register_response.json()
            print_success(f"学生创建成功: {student['username']} (ID: {student['id']})")
            return student
        else:
            error_detail = register_response.json()
            if "already exists" in str(error_detail).lower():
                print_info(f"学生 {student_data['username']} 已存在，尝试登录验证")
                
                # Try to login to verify the student exists
                login_response = requests.post(f"{api_url}/auth/token",
                                             json={"username": student_data['username'], "password": student_data['password']},
                                             timeout=10)
                
                if login_response.status_code == 200:
                    token = login_response.json()["access_token"]
                    headers = {"Authorization": f"Bearer {token}"}
                    
                    profile_response = requests.get(f"{api_url}/auth/me", headers=headers, timeout=10)
                    if profile_response.status_code == 200:
                        student = profile_response.json()
                        print_success(f"现有学生验证成功: {student['username']} (ID: {student['id']})")
                        return student
                
                print_error("无法验证现有学生")
                return None
            else:
                print_error(f"创建学生失败: {error_detail}")
                return None
    except Exception as e:
        print_error(f"创建学生异常: {e}")
        return None

def test_student_login(username, password):
    """Test student login"""
    print_header(f"测试学生登录: {username}")
    
    api_url = "http://localhost:8000/api/v1"
    
    try:
        login_response = requests.post(f"{api_url}/auth/token",
                                     json={"username": username, "password": password},
                                     timeout=10)
        
        if login_response.status_code == 200:
            token = login_response.json()["access_token"]
            headers = {"Authorization": f"Bearer {token}"}
            
            profile_response = requests.get(f"{api_url}/auth/me", headers=headers, timeout=10)
            if profile_response.status_code == 200:
                student = profile_response.json()
                print_success(f"学生登录成功: {student['username']} (ID: {student['id']}, 角色: {student['role']})")
                return student
            else:
                print_error("获取学生信息失败")
                return None
        else:
            print_error(f"学生登录失败: {login_response.json()}")
            return None
    except Exception as e:
        print_error(f"学生登录异常: {e}")
        return None

def test_add_student_to_class(student_id):
    """Test adding student to class"""
    print_header(f"测试添加学生 ID {student_id} 到班级")
    
    api_url = "http://localhost:8000/api/v1"
    
    # Teacher login
    try:
        login_response = requests.post(f"{api_url}/auth/token", 
                                     json={"username": "teacher_zhang", "password": "teacher123456"},
                                     timeout=10)
        
        if login_response.status_code == 200:
            teacher_token = login_response.json()["access_token"]
            teacher_headers = {"Authorization": f"Bearer {teacher_token}"}
            print_success("教师登录成功")
        else:
            print_error("教师登录失败")
            return False
    except Exception as e:
        print_error(f"教师登录异常: {e}")
        return False
    
    # Get classes
    try:
        classes_response = requests.get(f"{api_url}/classes/", headers=teacher_headers, timeout=10)
        if classes_response.status_code == 200:
            classes = classes_response.json()
            if classes:
                test_class = classes[0]
                print_success(f"使用班级: {test_class['name']} (ID: {test_class['id']})")
            else:
                print_error("没有可用的班级")
                return False
        else:
            print_error("获取班级失败")
            return False
    except Exception as e:
        print_error(f"获取班级异常: {e}")
        return False
    
    # Add student to class
    try:
        add_data = {"student_ids": [student_id]}
        print_info(f"添加学生数据: {add_data}")
        
        add_response = requests.post(f"{api_url}/classes/{test_class['id']}/students",
                                   json=add_data,
                                   headers=teacher_headers,
                                   timeout=10)
        
        if add_response.status_code == 200:
            result = add_response.json()
            print_success(f"成功添加学生到班级")
            print_info(f"结果: {result}")
            return True
        else:
            print_error(f"添加学生失败: {add_response.status_code}")
            try:
                error_detail = add_response.json()
                print_error(f"错误详情: {error_detail}")
            except:
                print_error(f"响应内容: {add_response.text}")
            return False
    except Exception as e:
        print_error(f"添加学生异常: {e}")
        return False

def main():
    print("🎓 创建测试学生并验证添加学生功能")
    print("=" * 70)
    
    print_info("此脚本将:")
    print_info("1. 创建一个测试学生用户")
    print_info("2. 验证学生登录功能")
    print_info("3. 测试添加学生到班级功能")
    
    # Step 1: Create test student
    student = create_test_student_via_api()
    
    if not student:
        # Try existing students
        print_info("尝试使用现有学生账号...")
        existing_accounts = [
            {"username": "testuser", "password": "testpass123"},
            {"username": "teststudent", "password": "student123"}
        ]
        
        for account in existing_accounts:
            student = test_student_login(account["username"], account["password"])
            if student:
                break
    
    if not student:
        print_error("无法创建或找到可用的学生用户")
        return False
    
    # Step 2: Test adding student to class
    success = test_add_student_to_class(student['id'])
    
    # Generate final report
    print_header("测试结果报告")
    
    print(f"📊 测试结果:")
    print(f"   👥 学生用户: ✅ 可用 (ID: {student['id']}, 用户名: {student['username']})")
    print(f"   ➕ 添加功能: {'✅ 正常' if success else '❌ 异常'}")
    
    if success:
        print("\n🎉 添加学生功能测试成功！")
        
        print("\n🎯 前端测试指南:")
        print("   1. 访问: http://localhost:3001/teacher/classes")
        print("   2. 登录教师账号: teacher_zhang / teacher123456")
        print("   3. 在'添加学生到班级'部分:")
        print("      - 选择一个班级")
        print(f"      - 输入学生ID: {student['id']}")
        print("   4. 观察按钮从灰色变为可用状态")
        print("   5. 点击'添加学生'按钮")
        
        print("\n✅ 问题已解决！")
        print("   - 按钮变灰是正常的输入验证行为")
        print("   - 选择班级并输入学生ID后，按钮会变为可用")
        print("   - 现在有可用的测试数据进行测试")
        
        return True
    else:
        print("\n❌ 添加学生功能仍有问题")
        return False

if __name__ == "__main__":
    success = main()
    print("\n" + "=" * 70)
    print("🎯 测试学生创建和验证完成！")
    print("=" * 70)
    
    if success:
        print("\n🎊 恭喜！问题已解决，可以正常使用添加学生功能！")
    else:
        print("\n⚠️ 仍有问题需要进一步解决")
