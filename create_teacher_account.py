import requests
import json

# Create teacher account and test teacher functionality
BASE_URL = "http://localhost:8000/api/v1"

def create_teacher_account():
    """Create a teacher account"""
    url = f"{BASE_URL}/auth/register"
    teacher_data = {
        "username": "teacher001",
        "email": "<EMAIL>",
        "password": "teacher123456",
        "full_name": "张老师",
        "role": "teacher"
    }
    
    try:
        response = requests.post(url, json=teacher_data)
        print(f"Teacher Registration Status: {response.status_code}")
        result = response.json()
        print(f"Teacher Registration Response: {result}")
        
        if response.status_code == 201:
            print("✅ Teacher account created successfully!")
            return True
        elif response.status_code == 400 and "already registered" in result.get("detail", ""):
            print("⚠️ Teacher account already exists")
            return True
        else:
            print("❌ Teacher account creation failed")
            return False
    except Exception as e:
        print(f"Teacher Registration Error: {e}")
        return False

def login_teacher():
    """Login as teacher"""
    url = f"{BASE_URL}/auth/token"
    login_data = {
        "username": "teacher001",
        "password": "teacher123456"
    }
    
    try:
        response = requests.post(url, json=login_data)
        print(f"Teacher Login Status: {response.status_code}")
        result = response.json()
        
        if response.status_code == 200 and "access_token" in result:
            print("✅ Teacher login successful!")
            print(f"Token Type: {result['token_type']}")
            return result["access_token"]
        else:
            print("❌ Teacher login failed")
            print(f"Error: {result}")
            return None
    except Exception as e:
        print(f"Teacher Login Error: {e}")
        return None

def test_teacher_endpoints(token):
    """Test teacher-specific endpoints"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n=== Testing Teacher Endpoints ===")
    
    # Test protected endpoint
    try:
        response = requests.get(f"{BASE_URL}/protected-test", headers=headers)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Protected endpoint: {data['message']}")
        else:
            print(f"❌ Protected endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Protected endpoint error: {e}")
    
    # Test teacher courses endpoint
    try:
        response = requests.get(f"{BASE_URL}/courses", headers=headers)
        print(f"Courses endpoint status: {response.status_code}")
        if response.status_code == 200:
            courses = response.json()
            print(f"✅ Teacher courses endpoint accessible. Found {len(courses)} courses.")
        else:
            print(f"❌ Teacher courses endpoint failed: {response.json()}")
    except Exception as e:
        print(f"❌ Teacher courses endpoint error: {e}")
    
    # Test teacher classes endpoint
    try:
        response = requests.get(f"{BASE_URL}/classes", headers=headers)
        print(f"Classes endpoint status: {response.status_code}")
        if response.status_code == 200:
            classes = response.json()
            print(f"✅ Teacher classes endpoint accessible. Found {len(classes)} classes.")
        else:
            print(f"❌ Teacher classes endpoint failed: {response.json()}")
    except Exception as e:
        print(f"❌ Teacher classes endpoint error: {e}")

def create_sample_course(token):
    """Create a sample course for the teacher"""
    headers = {"Authorization": f"Bearer {token}"}
    course_data = {
        "title": "Python编程基础",
        "description": "面向初学者的Python编程课程，涵盖基础语法、数据结构和面向对象编程。",
        "status": "active"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/courses", json=course_data, headers=headers)
        print(f"Create Course Status: {response.status_code}")
        result = response.json()
        
        if response.status_code == 201:
            print(f"✅ Sample course created successfully!")
            print(f"Course ID: {result['id']}, Title: {result['title']}")
            return result['id']
        else:
            print(f"❌ Course creation failed: {result}")
            return None
    except Exception as e:
        print(f"Course creation error: {e}")
        return None

def create_sample_class(token):
    """Create a sample class for the teacher"""
    headers = {"Authorization": f"Bearer {token}"}
    class_data = {
        "name": "Python编程班级A",
        "description": "2024年春季Python编程班级",
        "status": "active"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/classes", json=class_data, headers=headers)
        print(f"Create Class Status: {response.status_code}")
        result = response.json()
        
        if response.status_code == 201:
            print(f"✅ Sample class created successfully!")
            print(f"Class ID: {result['id']}, Name: {result['name']}")
            return result['id']
        else:
            print(f"❌ Class creation failed: {result}")
            return None
    except Exception as e:
        print(f"Class creation error: {e}")
        return None

def main():
    print("=== Creating Teacher Account and Testing Teacher Features ===")
    
    # Step 1: Create teacher account
    if not create_teacher_account():
        print("Failed to create teacher account. Exiting.")
        return
    
    # Step 2: Login as teacher
    token = login_teacher()
    if not token:
        print("Failed to login as teacher. Exiting.")
        return
    
    # Step 3: Test teacher endpoints
    test_teacher_endpoints(token)
    
    # Step 4: Create sample course
    print("\n=== Creating Sample Course ===")
    course_id = create_sample_course(token)
    
    # Step 5: Create sample class
    print("\n=== Creating Sample Class ===")
    class_id = create_sample_class(token)
    
    print("\n🎉 Teacher account setup and testing completed!")
    print("\n📋 Teacher Account Details:")
    print("Username: teacher001")
    print("Password: teacher123456")
    print("Role: teacher")
    print("Full Name: 张老师")
    print("Email: <EMAIL>")
    
    print("\n🔗 Teacher Dashboard Access:")
    print("Frontend URL: http://localhost:3000/teacher")
    print("Or: http://localhost:3001/teacher")

if __name__ == "__main__":
    main()
