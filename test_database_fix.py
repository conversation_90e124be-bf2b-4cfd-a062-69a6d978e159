import requests
import json
import time

def print_header(title):
    print("\n" + "="*60)
    print(f"🔧 {title}")
    print("="*60)

def print_success(message):
    print(f"✅ {message}")

def print_error(message):
    print(f"❌ {message}")

def print_info(message):
    print(f"ℹ️ {message}")

def test_database_integrity_fix():
    """Test the database integrity constraint fix"""
    print_header("数据库完整性约束修复测试")
    
    api_url = "http://localhost:8000/api/v1"
    
    # Step 1: Login as teacher
    print("\n📋 Step 1: 教师登录")
    try:
        login_response = requests.post(f"{api_url}/auth/token", 
                                     json={"username": "teacher_zhang", "password": "teacher123456"},
                                     timeout=10)
        
        if login_response.status_code == 200:
            token = login_response.json()["access_token"]
            headers = {"Authorization": f"Bearer {token}"}
            print_success("教师登录成功")
        else:
            print_error(f"教师登录失败: {login_response.json()}")
            return False
    except Exception as e:
        print_error(f"登录异常: {e}")
        return False
    
    # Step 2: Get courses
    print("\n📋 Step 2: 获取课程列表")
    try:
        courses_response = requests.get(f"{api_url}/courses/", headers=headers, timeout=10)
        
        if courses_response.status_code == 200:
            courses = courses_response.json()
            if courses:
                course_id = courses[0]['id']
                print_success(f"获取课程成功，使用课程ID: {course_id}")
            else:
                print_error("没有可用的课程")
                return False
        else:
            print_error(f"获取课程失败: {courses_response.json()}")
            return False
    except Exception as e:
        print_error(f"获取课程异常: {e}")
        return False
    
    # Step 3: Create a material to test updates
    print("\n📋 Step 3: 创建测试材料")
    try:
        # First get upload credentials
        params = {
            "course_id": course_id,
            "original_filename": "test_integrity.txt",
            "file_type": "text"
        }
        
        upload_creds_response = requests.post(f"{api_url}/materials/upload-credentials", 
                                            params=params, headers=headers, timeout=10)
        
        if upload_creds_response.status_code == 200:
            upload_info = upload_creds_response.json()
            upload_url = upload_info['upload_url']
            object_name = upload_info['object_name']
            
            # Upload file
            test_content = "Database integrity test content"
            upload_response = requests.put(upload_url, 
                                         data=test_content.encode('utf-8'),
                                         headers={"Content-Type": "text/plain"},
                                         timeout=30)
            
            if upload_response.status_code in [200, 204]:
                # Create material record
                material_data = {
                    "title": "数据库完整性测试材料",
                    "description": "用于测试数据库约束修复的材料",
                    "file_type": "text",
                    "file_path_minio": object_name,
                    "file_size_bytes": len(test_content.encode('utf-8'))
                }
                
                material_response = requests.post(f"{api_url}/materials/{course_id}",
                                                json=material_data, headers=headers, timeout=10)
                
                if material_response.status_code == 201:
                    material = material_response.json()
                    material_id = material['id']
                    print_success(f"测试材料创建成功，ID: {material_id}")
                else:
                    print_error(f"材料创建失败: {material_response.json()}")
                    return False
            else:
                print_error(f"文件上传失败: {upload_response.status_code}")
                return False
        else:
            print_error(f"获取上传凭证失败: {upload_creds_response.json()}")
            return False
    except Exception as e:
        print_error(f"创建测试材料异常: {e}")
        return False
    
    # Step 4: Test material update (this should not cause integrity constraint error)
    print("\n📋 Step 4: 测试材料更新（修复后应该正常工作）")
    try:
        update_data = {
            "title": "更新后的材料标题",
            "description": "更新后的材料描述"
        }
        
        update_response = requests.put(f"{api_url}/materials/{material_id}",
                                     json=update_data, headers=headers, timeout=10)
        
        print_info(f"更新请求状态码: {update_response.status_code}")
        
        if update_response.status_code == 200:
            updated_material = update_response.json()
            print_success("材料更新成功")
            print_info(f"新标题: {updated_material['title']}")
            print_info(f"新描述: {updated_material['description']}")
            print_info(f"Course ID保持不变: {updated_material['course_id']}")
            return True
        else:
            print_error(f"材料更新失败: {update_response.json()}")
            return False
    except Exception as e:
        print_error(f"材料更新异常: {e}")
        return False

def test_quiz_update():
    """Test quiz update functionality"""
    print_header("测验更新功能测试")
    
    api_url = "http://localhost:8000/api/v1"
    
    # Login
    try:
        login_response = requests.post(f"{api_url}/auth/token", 
                                     json={"username": "teacher_zhang", "password": "teacher123456"},
                                     timeout=10)
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
    except:
        print_error("无法登录，跳过测验更新测试")
        return False
    
    # Get courses
    try:
        courses_response = requests.get(f"{api_url}/courses/", headers=headers, timeout=10)
        courses = courses_response.json()
        course_id = courses[0]['id']
    except:
        print_error("无法获取课程，跳过测验更新测试")
        return False
    
    # Create a test quiz
    print("\n📋 创建测试测验")
    try:
        quiz_data = {
            "course_id": course_id,
            "title": "数据库完整性测试测验",
            "description": "用于测试数据库约束修复的测验",
            "quiz_type": "quiz",
            "is_published": False
        }
        
        quiz_response = requests.post(f"{api_url}/quizzes/",
                                    json=quiz_data, headers=headers, timeout=10)
        
        if quiz_response.status_code == 201:
            quiz = quiz_response.json()
            quiz_id = quiz['id']
            print_success(f"测试测验创建成功，ID: {quiz_id}")
        else:
            print_error(f"测验创建失败: {quiz_response.json()}")
            return False
    except Exception as e:
        print_error(f"创建测试测验异常: {e}")
        return False
    
    # Test quiz update
    print("\n📋 测试测验更新")
    try:
        update_data = {
            "title": "更新后的测验标题",
            "description": "更新后的测验描述",
            "is_published": True
        }
        
        update_response = requests.put(f"{api_url}/quizzes/{quiz_id}",
                                     json=update_data, headers=headers, timeout=10)
        
        if update_response.status_code == 200:
            updated_quiz = update_response.json()
            print_success("测验更新成功")
            print_info(f"新标题: {updated_quiz['title']}")
            print_info(f"Course ID保持不变: {updated_quiz['course_id']}")
            return True
        else:
            print_error(f"测验更新失败: {update_response.json()}")
            return False
    except Exception as e:
        print_error(f"测验更新异常: {e}")
        return False

def main():
    print("🔧 数据库完整性约束修复验证")
    print("=" * 70)
    
    print_info("此测试验证修复后的CRUD操作不会触发数据库完整性约束错误")
    
    # Test material update
    material_test_success = test_database_integrity_fix()
    
    # Test quiz update
    quiz_test_success = test_quiz_update()
    
    # Generate report
    print_header("修复验证结果")
    
    print(f"📊 测试结果:")
    print(f"   📄 材料更新测试: {'✅ 通过' if material_test_success else '❌ 失败'}")
    print(f"   📝 测验更新测试: {'✅ 通过' if quiz_test_success else '❌ 失败'}")
    
    if material_test_success and quiz_test_success:
        print("\n🎉 数据库完整性约束问题已修复！")
        print("\n🔧 修复内容:")
        print("   ✅ update_material函数 - 保护course_id等关键字段")
        print("   ✅ update_quiz函数 - 保护course_id等关键字段")
        print("   ✅ update_question函数 - 保护quiz_id等关键字段")
        print("   ✅ 防止意外更新外键约束字段")
        
        print("\n💡 技术说明:")
        print("   🛡️ 添加了protected_fields保护机制")
        print("   🔒 防止更新id, course_id, created_by_user_id等字段")
        print("   ✨ 保持数据完整性和一致性")
        print("   🚀 提高系统稳定性")
        
        print("\n🎯 问题解决:")
        print("   ❌ 原问题: NOT NULL constraint failed: materials.course_id")
        print("   ✅ 现状态: 更新操作安全执行，不会触发约束错误")
        print("   🔧 原因: MaterialUpdate等schema可能包含不应更新的字段")
        print("   💡 解决: 在CRUD函数中过滤掉保护字段")
        
    else:
        print("\n⚠️ 仍有问题需要进一步修复")
        if not material_test_success:
            print("   📄 材料更新功能需要检查")
        if not quiz_test_success:
            print("   📝 测验更新功能需要检查")
    
    print("\n" + "=" * 70)
    print("🎯 数据库完整性约束修复验证完成！")
    print("=" * 70)

if __name__ == "__main__":
    main()
