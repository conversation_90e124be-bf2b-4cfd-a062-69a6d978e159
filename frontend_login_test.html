<!DOCTYPE html>
<html>
<head>
    <title>Frontend Login Test - Education Platform</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group { 
            margin: 15px 0; 
        }
        label { 
            display: block; 
            margin-bottom: 5px; 
            font-weight: bold;
        }
        input { 
            padding: 10px; 
            width: 100%; 
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button { 
            padding: 12px 24px; 
            background: #007bff; 
            color: white; 
            border: none; 
            cursor: pointer;
            border-radius: 4px;
            font-size: 16px;
            margin: 10px 5px 10px 0;
        }
        button:hover {
            background: #0056b3;
        }
        .result { 
            margin: 20px 0; 
            padding: 15px; 
            border-radius: 4px;
            border: 1px solid #ddd; 
        }
        .success { 
            background: #d4edda; 
            border-color: #c3e6cb; 
            color: #155724;
        }
        .error { 
            background: #f8d7da; 
            border-color: #f5c6cb; 
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            background: #e9ecef;
            border-radius: 4px;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        h2 {
            color: #666;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        .token-display {
            word-break: break-all;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎓 Education Platform Login Test</h1>
        
        <div class="status" id="connectionStatus">
            <strong>Connection Status:</strong> <span id="statusText">Checking...</span>
        </div>
        
        <h2>📝 User Registration</h2>
        <form id="registerForm">
            <div class="form-group">
                <label>Username:</label>
                <input type="text" id="regUsername" value="testuser2" required>
            </div>
            <div class="form-group">
                <label>Email:</label>
                <input type="email" id="regEmail" value="<EMAIL>" required>
            </div>
            <div class="form-group">
                <label>Password:</label>
                <input type="password" id="regPassword" value="testpass123" required>
            </div>
            <div class="form-group">
                <label>Full Name:</label>
                <input type="text" id="regFullName" value="Test User 2" required>
            </div>
            <div class="form-group">
                <label>Role:</label>
                <select id="regRole" style="padding: 10px; width: 100%; border: 1px solid #ddd; border-radius: 4px;">
                    <option value="student">Student</option>
                    <option value="teacher">Teacher</option>
                </select>
            </div>
            <button type="submit">Register</button>
        </form>
        
        <h2>🔐 User Login</h2>
        <form id="loginForm">
            <div class="form-group">
                <label>Username:</label>
                <input type="text" id="username" value="testuser" required>
            </div>
            <div class="form-group">
                <label>Password:</label>
                <input type="password" id="password" value="testpass123" required>
            </div>
            <button type="submit">Login</button>
            <button type="button" onclick="testExistingUser()">Test with testuser2</button>
        </form>
        
        <div id="result"></div>
    </div>
    
    <script>
        const API_BASE_URL = 'http://localhost:8000/api/v1';
        
        // Check connection status on page load
        window.addEventListener('load', async () => {
            await checkConnectionStatus();
        });
        
        async function checkConnectionStatus() {
            const statusText = document.getElementById('statusText');
            const statusDiv = document.getElementById('connectionStatus');
            
            try {
                // Test backend connectivity
                const backendResponse = await fetch('http://localhost:8000/');
                if (backendResponse.ok) {
                    statusText.innerHTML = '✅ Backend connected (Port 8000)';
                    statusDiv.className = 'status success';
                } else {
                    statusText.innerHTML = '❌ Backend error: ' + backendResponse.status;
                    statusDiv.className = 'status error';
                }
                
                // Test API endpoint
                const apiResponse = await fetch(`${API_BASE_URL}/protected-test`);
                if (apiResponse.status === 401) {
                    statusText.innerHTML += ' | ✅ API endpoints accessible';
                }
                
            } catch (error) {
                statusText.innerHTML = '❌ Connection failed: ' + error.message;
                statusDiv.className = 'status error';
            }
        }
        
        // Registration form handler
        document.getElementById('registerForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('regUsername').value;
            const email = document.getElementById('regEmail').value;
            const password = document.getElementById('regPassword').value;
            const fullName = document.getElementById('regFullName').value;
            const role = document.getElementById('regRole').value;
            
            const resultDiv = document.getElementById('result');
            
            try {
                resultDiv.innerHTML = '<div class="info"><p>Registering user...</p></div>';
                
                const response = await fetch(`${API_BASE_URL}/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        username, 
                        email, 
                        password, 
                        full_name: fullName, 
                        role 
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ Registration Successful!</h3>
                            <p><strong>User ID:</strong> ${data.id}</p>
                            <p><strong>Username:</strong> ${data.username}</p>
                            <p><strong>Role:</strong> ${data.role}</p>
                            <p>You can now login with this user.</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ Registration Failed</h3>
                            <p><strong>Error:</strong> ${data.detail || 'Unknown error'}</p>
                            <p><strong>Status:</strong> ${response.status}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ Network Error</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
            }
        });
        
        // Login form handler
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            await performLogin();
        });
        
        function testExistingUser() {
            document.getElementById('username').value = 'testuser2';
            document.getElementById('password').value = 'testpass123';
            performLogin();
        }
        
        async function performLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            try {
                resultDiv.innerHTML = '<div class="info"><p>Logging in...</p></div>';
                
                const response = await fetch(`${API_BASE_URL}/auth/token`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ Login Successful!</h3>
                            <p><strong>Token Type:</strong> ${data.token_type}</p>
                            <p><strong>Access Token:</strong></p>
                            <div class="token-display">${data.access_token}</div>
                        </div>
                    `;
                    
                    // Test protected endpoint
                    await testProtectedEndpoint(data.access_token);
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ Login Failed</h3>
                            <p><strong>Error:</strong> ${data.detail || 'Unknown error'}</p>
                            <p><strong>Status:</strong> ${response.status}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ Network Error</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p>Make sure the backend server is running on port 8000</p>
                    </div>
                `;
            }
        }
        
        async function testProtectedEndpoint(token) {
            try {
                const response = await fetch(`${API_BASE_URL}/protected-test`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById('result').innerHTML += `
                        <div class="result success">
                            <h3>✅ Protected Endpoint Test Successful!</h3>
                            <p><strong>Message:</strong> ${data.message}</p>
                            <p>🎉 <strong>All tests passed! Login system is working correctly!</strong></p>
                        </div>
                    `;
                } else {
                    document.getElementById('result').innerHTML += `
                        <div class="result error">
                            <h3>❌ Protected Endpoint Test Failed</h3>
                            <p><strong>Error:</strong> ${data.detail || 'Unknown error'}</p>
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('result').innerHTML += `
                    <div class="result error">
                        <h3>❌ Protected Endpoint Network Error</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
