
<!DOCTYPE html>
<html>
<head>
    <title>前端登录测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { padding: 10px; width: 100%; box-sizing: border-box; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 12px 24px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin: 15px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 前端登录测试</h1>
        
        <div class="form-group">
            <label>用户名:</label>
            <input type="text" id="username" value="teacher_zhang">
        </div>
        
        <div class="form-group">
            <label>密码:</label>
            <input type="password" id="password" value="teacher123456">
        </div>
        
        <button onclick="testLogin()">测试登录</button>
        <button onclick="testCORS()">测试CORS</button>
        <button onclick="testAPI()">测试API连接</button>
        
        <div id="result"></div>
    </div>
    
    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        
        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = '<div class="info">正在测试登录...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/auth/token`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ 登录成功！<br>
                            Token: ${data.access_token.substring(0, 50)}...<br>
                            类型: ${data.token_type}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ 登录失败<br>
                            状态码: ${response.status}<br>
                            错误: ${data.detail || '未知错误'}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ 网络错误: ${error.message}
                    </div>
                `;
            }
        }
        
        async function testCORS() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info">正在测试CORS...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/auth/token`, {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': window.location.origin,
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type'
                    }
                });
                
                resultDiv.innerHTML = `
                    <div class="success">
                        ✅ CORS测试完成<br>
                        状态码: ${response.status}<br>
                        允许的方法: ${response.headers.get('Access-Control-Allow-Methods') || '未设置'}<br>
                        允许的头: ${response.headers.get('Access-Control-Allow-Headers') || '未设置'}
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ CORS测试失败: ${error.message}
                    </div>
                `;
            }
        }
        
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info">正在测试API连接...</div>';
            
            try {
                const response = await fetch('http://localhost:8000/', {
                    method: 'GET'
                });
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ API连接成功<br>
                            状态码: ${response.status}<br>
                            后端服务正常运行
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ API连接失败<br>
                            状态码: ${response.status}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ API连接错误: ${error.message}
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
    