import requests

def check_available_routes():
    """Check what routes are available"""
    print("🔍 Checking Available Routes")
    print("=" * 40)
    
    # Test root endpoint
    try:
        response = requests.get("http://localhost:8000/", timeout=5)
        print(f"✅ Root endpoint: {response.status_code}")
    except Exception as e:
        print(f"❌ Root endpoint error: {e}")
    
    # Test docs endpoint
    try:
        response = requests.get("http://localhost:8000/docs", timeout=5)
        print(f"✅ Docs endpoint: {response.status_code}")
    except Exception as e:
        print(f"❌ Docs endpoint error: {e}")
    
    # Test debug endpoints
    debug_endpoints = [
        "/api/v1/debug/api-calls",
        "/api/v1/debug/api-calls/json",
        "/api/v1/debug/clear-logs"
    ]
    
    for endpoint in debug_endpoints:
        try:
            response = requests.get(f"http://localhost:8000{endpoint}", timeout=5)
            print(f"✅ Debug endpoint {endpoint}: {response.status_code}")
        except Exception as e:
            print(f"❌ Debug endpoint {endpoint} error: {e}")
    
    # Test other API endpoints
    api_endpoints = [
        "/api/v1/auth/token",
        "/api/v1/courses/",
        "/api/v1/classes/"
    ]
    
    for endpoint in api_endpoints:
        try:
            if endpoint == "/api/v1/auth/token":
                response = requests.post(f"http://localhost:8000{endpoint}", 
                                       json={"username": "test", "password": "test"}, timeout=5)
            else:
                response = requests.get(f"http://localhost:8000{endpoint}", timeout=5)
            print(f"✅ API endpoint {endpoint}: {response.status_code}")
        except Exception as e:
            print(f"❌ API endpoint {endpoint} error: {e}")

if __name__ == "__main__":
    check_available_routes()
