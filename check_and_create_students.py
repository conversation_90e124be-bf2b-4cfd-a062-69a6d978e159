import requests
import json

def print_header(title):
    print("\n" + "="*70)
    print(f"🎓 {title}")
    print("="*70)

def print_success(message):
    print(f"✅ {message}")

def print_error(message):
    print(f"❌ {message}")

def print_info(message):
    print(f"ℹ️ {message}")

def check_existing_students():
    """Check if there are existing students in the system"""
    print_header("检查现有学生用户")
    
    api_url = "http://localhost:8000/api/v1"
    
    # Try to login as admin to check all users
    print("\n📋 尝试管理员登录")
    try:
        admin_login = requests.post(f"{api_url}/auth/token", 
                                  json={"username": "admin", "password": "admin123456"},
                                  timeout=10)
        
        if admin_login.status_code == 200:
            admin_token = admin_login.json()["access_token"]
            admin_headers = {"Authorization": f"Bearer {admin_token}"}
            print_success("管理员登录成功")
            
            # Get all users
            users_response = requests.get(f"{api_url}/admin/users?role=student", 
                                        headers=admin_headers, timeout=10)
            
            if users_response.status_code == 200:
                students = users_response.json()
                print_success(f"找到 {len(students)} 个学生用户")
                for student in students:
                    print_info(f"  学生: {student['username']} (ID: {student['id']}) - {student.get('email', 'N/A')}")
                return students, admin_headers
            else:
                print_error(f"获取学生列表失败: {users_response.json()}")
                return [], admin_headers
        else:
            print_error("管理员登录失败")
            return [], None
    except Exception as e:
        print_error(f"检查学生异常: {e}")
        return [], None

def create_test_students(admin_headers):
    """Create test students if none exist"""
    print_header("创建测试学生用户")
    
    api_url = "http://localhost:8000/api/v1"
    
    test_students = [
        {
            "username": "student001",
            "password": "student123",
            "email": "<EMAIL>",
            "full_name": "张三",
            "role": "student"
        },
        {
            "username": "student002", 
            "password": "student123",
            "email": "<EMAIL>",
            "full_name": "李四",
            "role": "student"
        },
        {
            "username": "student003",
            "password": "student123", 
            "email": "<EMAIL>",
            "full_name": "王五",
            "role": "student"
        }
    ]
    
    created_students = []
    
    for student_data in test_students:
        try:
            print_info(f"创建学生: {student_data['username']}")
            
            create_response = requests.post(f"{api_url}/admin/users",
                                          json=student_data,
                                          headers=admin_headers,
                                          timeout=10)
            
            if create_response.status_code == 201:
                student = create_response.json()
                created_students.append(student)
                print_success(f"成功创建学生: {student['username']} (ID: {student['id']})")
            else:
                error_detail = create_response.json()
                if "already exists" in str(error_detail).lower():
                    print_info(f"学生 {student_data['username']} 已存在")
                else:
                    print_error(f"创建学生失败: {error_detail}")
        except Exception as e:
            print_error(f"创建学生 {student_data['username']} 异常: {e}")
    
    return created_students

def test_student_login_and_get_info():
    """Test student login and get their info"""
    print_header("测试学生登录和信息获取")
    
    api_url = "http://localhost:8000/api/v1"
    
    # Test students to try
    test_accounts = [
        {"username": "testuser", "password": "testpass123"},
        {"username": "student001", "password": "student123"},
        {"username": "student002", "password": "student123"}
    ]
    
    student_info = []
    
    for account in test_accounts:
        try:
            print_info(f"尝试登录: {account['username']}")
            
            login_response = requests.post(f"{api_url}/auth/token",
                                         json=account,
                                         timeout=10)
            
            if login_response.status_code == 200:
                token = login_response.json()["access_token"]
                headers = {"Authorization": f"Bearer {token}"}
                
                # Get user info
                profile_response = requests.get(f"{api_url}/auth/me", 
                                              headers=headers, timeout=10)
                
                if profile_response.status_code == 200:
                    user_info = profile_response.json()
                    if user_info.get('role') == 'student':
                        student_info.append(user_info)
                        print_success(f"学生登录成功: {user_info['username']} (ID: {user_info['id']})")
                    else:
                        print_info(f"用户 {account['username']} 不是学生角色: {user_info.get('role')}")
                else:
                    print_error(f"获取用户信息失败: {profile_response.json()}")
            else:
                print_info(f"登录失败: {account['username']}")
        except Exception as e:
            print_error(f"测试登录 {account['username']} 异常: {e}")
    
    return student_info

def test_add_student_to_class(student_id):
    """Test adding a specific student to a class"""
    print_header(f"测试添加学生 ID {student_id} 到班级")
    
    api_url = "http://localhost:8000/api/v1"
    
    # Teacher login
    try:
        login_response = requests.post(f"{api_url}/auth/token", 
                                     json={"username": "teacher_zhang", "password": "teacher123456"},
                                     timeout=10)
        
        if login_response.status_code == 200:
            teacher_token = login_response.json()["access_token"]
            teacher_headers = {"Authorization": f"Bearer {teacher_token}"}
            print_success("教师登录成功")
        else:
            print_error("教师登录失败")
            return False
    except Exception as e:
        print_error(f"教师登录异常: {e}")
        return False
    
    # Get classes
    try:
        classes_response = requests.get(f"{api_url}/classes/", headers=teacher_headers, timeout=10)
        if classes_response.status_code == 200:
            classes = classes_response.json()
            if classes:
                test_class = classes[0]
                print_success(f"使用班级: {test_class['name']} (ID: {test_class['id']})")
            else:
                print_error("没有可用的班级")
                return False
        else:
            print_error("获取班级失败")
            return False
    except Exception as e:
        print_error(f"获取班级异常: {e}")
        return False
    
    # Add student to class
    try:
        add_data = {"student_ids": [student_id]}
        print_info(f"添加学生数据: {add_data}")
        
        add_response = requests.post(f"{api_url}/classes/{test_class['id']}/students",
                                   json=add_data,
                                   headers=teacher_headers,
                                   timeout=10)
        
        if add_response.status_code == 200:
            result = add_response.json()
            print_success(f"成功添加学生到班级")
            print_info(f"结果: {result}")
            return True
        else:
            print_error(f"添加学生失败: {add_response.status_code}")
            try:
                error_detail = add_response.json()
                print_error(f"错误详情: {error_detail}")
            except:
                print_error(f"响应内容: {add_response.text}")
            return False
    except Exception as e:
        print_error(f"添加学生异常: {e}")
        return False

def main():
    print("🎓 学生用户检查和创建")
    print("=" * 80)
    
    print_info("此脚本将检查和创建学生用户，解决'添加学生'按钮问题")
    
    # Step 1: Check existing students
    existing_students, admin_headers = check_existing_students()
    
    # Step 2: Create test students if needed
    if len(existing_students) == 0 and admin_headers:
        print_info("没有找到学生用户，创建测试学生...")
        created_students = create_test_students(admin_headers)
    else:
        print_info(f"已有 {len(existing_students)} 个学生用户")
    
    # Step 3: Test student login
    student_info = test_student_login_and_get_info()
    
    # Step 4: Test adding student to class
    if student_info:
        test_student = student_info[0]
        success = test_add_student_to_class(test_student['id'])
    else:
        print_error("没有可用的学生进行测试")
        success = False
    
    # Generate report
    print_header("学生用户状态报告")
    
    print(f"📊 检查结果:")
    print(f"   👥 现有学生数量: {len(existing_students)}")
    print(f"   🔑 可登录学生数量: {len(student_info)}")
    print(f"   ✅ 添加学生测试: {'成功' if success else '失败'}")
    
    if student_info:
        print("\n👥 可用的学生账号:")
        for student in student_info:
            print_info(f"   用户名: {student['username']} (ID: {student['id']})")
        
        print("\n🎯 前端测试指南:")
        print("   1. 访问: http://localhost:3001/teacher/classes")
        print("   2. 登录教师账号: teacher_zhang / teacher123456")
        print("   3. 在'添加学生到班级'部分:")
        print("      - 选择一个班级")
        print(f"      - 输入学生ID: {student_info[0]['id']}")
        print("   4. 观察'添加学生'按钮是否变为可用")
        
        print("\n✅ 问题应该已解决！")
    else:
        print("\n❌ 仍然没有可用的学生用户")
        print("   请检查用户创建过程或手动创建学生用户")
    
    return len(student_info) > 0

if __name__ == "__main__":
    success = main()
    print("\n" + "=" * 80)
    print("🎯 学生用户检查和创建完成！")
    print("=" * 80)
