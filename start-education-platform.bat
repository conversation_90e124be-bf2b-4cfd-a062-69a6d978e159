@echo off
chcp 65001 >nul
title 智能教育平台启动器

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🎓 智能教育平台启动器                      ║
echo ║                  Education Platform Launcher                 ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 正在启动智能教育平台...
echo.

REM 检查Python虚拟环境
if not exist ".venv\Scripts\activate.bat" (
    echo ❌ 错误: 未找到Python虚拟环境
    echo 请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

REM 检查前端目录
if not exist "frontend\package.json" (
    echo ❌ 错误: 未找到前端项目
    echo 请确保frontend目录存在
    pause
    exit /b 1
)

echo 📋 系统检查通过
echo.

REM 启动后端服务
echo 🔧 启动后端服务 (端口 8000)...
start "🔧 后端服务 - FastAPI" cmd /k "title 后端服务 - FastAPI && cd /d %~dp0 && .venv\Scripts\activate.bat && echo 🐍 Python虚拟环境已激活 && echo 🚀 启动FastAPI服务器... && python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload"

REM 等待后端启动
echo ⏳ 等待后端服务启动...
timeout /t 5 /nobreak >nul

REM 启动前端服务
echo 🎨 启动前端服务 (端口 3000)...
start "🎨 前端服务 - React" cmd /k "title 前端服务 - React && cd /d %~dp0\frontend && echo 📦 启动React开发服务器... && npm start"

REM 等待前端启动
echo ⏳ 等待前端服务启动...
timeout /t 3 /nobreak >nul

echo.
echo ✅ 服务启动完成!
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        🔗 访问地址                           ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║ 🎨 前端应用:  http://localhost:3000                         ║
echo ║ 🔧 后端API:   http://localhost:8000                         ║
echo ║ 📚 API文档:   http://localhost:8000/docs                    ║
echo ║ 📖 ReDoc文档: http://localhost:8000/redoc                   ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        👥 测试账号                           ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║ 👨‍🏫 教师账号: teacher_zhang / teacher123456                  ║
echo ║ 👨‍🎓 学生账号: testuser / testpass123                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        🎯 主要功能                           ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║ ✅ 用户认证 (学生/教师角色)                                   ║
echo ║ ✅ 课程管理 (创建、编辑、删除)                                ║
echo ║ ✅ 班级管理 (学生管理)                                        ║
echo ║ ✅ 测验系统 (创建、参与、评分)                                ║
echo ║ ✅ 文件上传 (多格式支持)                                      ║
echo ║ ✅ 🤖 DeepSeek AI文档分析                                     ║
echo ║ ✅ 智能内容提取和问题生成                                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 等待用户选择
echo 请选择操作:
echo [1] 打开前端应用
echo [2] 打开API文档
echo [3] 运行系统测试
echo [4] 查看DeepSeek演示
echo [5] 退出
echo.
set /p choice="请输入选择 (1-5): "

if "%choice%"=="1" (
    echo 🌐 正在打开前端应用...
    start http://localhost:3000
) else if "%choice%"=="2" (
    echo 📚 正在打开API文档...
    start http://localhost:8000/docs
) else if "%choice%"=="3" (
    echo 🧪 正在运行系统测试...
    .venv\Scripts\activate.bat && python comprehensive_test_and_fix.py
) else if "%choice%"=="4" (
    echo 🤖 正在打开DeepSeek演示...
    start deepseek_demo.html
) else if "%choice%"=="5" (
    echo 👋 感谢使用智能教育平台!
    exit /b 0
) else (
    echo ⚠️ 无效选择，默认打开前端应用
    start http://localhost:3000
)

echo.
echo 💡 提示: 
echo    - 前端和后端服务将在后台继续运行
echo    - 关闭对应的命令行窗口可停止服务
echo    - 如需重启，请重新运行此脚本
echo.
echo 🎉 智能教育平台已成功启动!
echo.
pause
