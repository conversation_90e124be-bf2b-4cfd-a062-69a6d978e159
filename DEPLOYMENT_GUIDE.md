# 🎓 智能教育平台 - 教学资料管理系统

## 📋 项目概述

这是一个完整的智能教育平台，专注于教学资料管理功能。系统支持教师上传、管理和分析教学资料，学生可以访问和学习相关内容。

### ✨ 核心功能

- 📚 **课程管理**: 创建、编辑、删除课程
- 📁 **资料上传**: 支持多种文件格式的教学资料上传
- 🗂️ **分类显示**: 资料按课程自动分类显示
- 🗑️ **资料删除**: 安全的资料删除功能
- 🤖 **AI分析**: 基于DeepSeek的智能内容分析
- 🔍 **语义搜索**: 基于向量的智能搜索功能
- 👥 **用户管理**: 教师和学生角色管理
- 📊 **学习进度**: 学生学习进度跟踪

## 🚀 快速启动

### 环境要求

- Python 3.8+
- Node.js 14+
- SQLite (内置)

### 1. 后端启动

```bash
# 进入项目目录
cd D:\PycharmProjects\autoapi

# 激活虚拟环境并启动后端
cmd /c ".venv\Scripts\activate && python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload"
```

### 2. 前端启动

```bash
# 进入前端目录
cd frontend

# 启动前端应用
npm start
```

### 3. 访问应用

- 🎨 **前端应用**: http://localhost:3000
- 📚 **课程管理**: http://localhost:3000/teacher/courses
- 🔧 **后端API**: http://localhost:8000
- 📖 **API文档**: http://localhost:8000/docs

## 👥 测试账号

### 教师账号
- **用户名**: teacher_zhang
- **密码**: teacher123456
- **权限**: 创建课程、上传资料、管理内容

### 学生账号
- **用户名**: testuser
- **密码**: testpass123
- **权限**: 查看课程、下载资料、学习进度

## 📖 使用指南

### 教师操作流程

1. **登录系统**
   - 访问 http://localhost:3000
   - 使用教师账号登录

2. **创建课程**
   - 进入课程管理页面
   - 点击"创建新课程"
   - 填写课程信息并保存

3. **上传教学资料**
   - 在课程列表中选择目标课程
   - 点击"上传资料"按钮
   - 选择文件并填写资料信息
   - 点击上传完成

4. **管理资料**
   - 查看课程下的所有资料
   - 使用AI分析功能获取内容洞察
   - 删除不需要的资料

### 学生操作流程

1. **登录系统**
   - 使用学生账号登录

2. **浏览课程**
   - 查看可访问的课程列表
   - 进入感兴趣的课程

3. **学习资料**
   - 下载或在线查看教学资料
   - 系统自动记录学习进度

## 🔧 技术架构

### 后端技术栈

- **框架**: FastAPI
- **数据库**: SQLite + SQLAlchemy ORM
- **认证**: JWT Token
- **文件存储**: Mock Minio (开发环境)
- **向量搜索**: Mock Milvus
- **AI分析**: DeepSeek API

### 前端技术栈

- **框架**: React
- **路由**: React Router
- **状态管理**: React Hooks
- **样式**: CSS + 内联样式
- **HTTP客户端**: Fetch API

### 数据库设计

```
Users (用户表)
├── id: 主键
├── username: 用户名
├── email: 邮箱
├── role: 角色 (teacher/student/admin)
└── password_hash: 密码哈希

Courses (课程表)
├── id: 主键
├── title: 课程标题
├── description: 课程描述
├── teacher_id: 教师ID (外键)
└── status: 课程状态

Materials (资料表)
├── id: 主键
├── title: 资料标题
├── description: 资料描述
├── file_type: 文件类型
├── file_path_minio: 存储路径
├── course_id: 课程ID (外键)
└── created_by_user_id: 创建者ID (外键)
```

## 📁 支持的文件类型

- 📝 **文档**: .txt, .pdf, .doc, .docx
- 🖼️ **图片**: .jpg, .jpeg, .png, .gif
- 🎥 **视频**: .mp4, .avi, .mov
- 🎵 **音频**: .mp3, .wav, .m4a
- 📊 **演示**: .ppt, .pptx

## 🛡️ 安全特性

- **身份认证**: JWT Token认证
- **权限控制**: 基于角色的访问控制
- **数据验证**: 输入数据严格验证
- **文件安全**: 文件类型和大小限制
- **SQL注入防护**: ORM参数化查询

## 🔍 API接口

### 认证接口
- `POST /api/v1/auth/token` - 用户登录
- `POST /api/v1/auth/register` - 用户注册

### 课程接口
- `GET /api/v1/courses/` - 获取课程列表
- `POST /api/v1/courses/` - 创建新课程
- `DELETE /api/v1/courses/{course_id}` - 删除课程

### 资料接口
- `POST /api/v1/materials/upload-credentials` - 获取上传凭证
- `POST /api/v1/materials/{course_id}` - 创建资料记录
- `GET /api/v1/materials/course/{course_id}` - 获取课程资料
- `GET /api/v1/materials/{material_id}` - 获取单个资料
- `DELETE /api/v1/materials/{material_id}` - 删除资料
- `POST /api/v1/materials/{material_id}/analyze` - AI分析资料

## 🧪 测试功能

系统包含完整的测试脚本：

```bash
# 测试完整功能
python test_complete_material_management.py

# 测试资料删除
python test_material_deletion.py

# 测试数据库修复
python test_database_fix.py

# 测试Milvus修复
python test_milvus_fix.py
```

## 📊 性能特性

- **文件上传**: 支持大文件上传，进度显示
- **并发处理**: 异步处理，支持多用户并发
- **缓存机制**: 智能缓存，提高响应速度
- **数据库优化**: 索引优化，查询性能良好

## 🔄 部署选项

### 开发环境 (当前配置)
- Mock Minio存储
- SQLite数据库
- 本地文件系统

### 生产环境 (可扩展)
- 真实Minio对象存储
- PostgreSQL/MySQL数据库
- Redis缓存
- Docker容器化部署

## 📝 更新日志

### v1.0.0 (当前版本)
- ✅ 完整的教学资料管理功能
- ✅ 课程创建和管理
- ✅ 文件上传和删除
- ✅ AI智能分析
- ✅ 用户认证和权限控制
- ✅ 响应式前端界面

## 🎯 未来规划

- 📱 移动端适配
- 🔔 实时通知系统
- 📈 数据分析仪表板
- 🎮 互动学习功能
- 🌐 多语言支持

## 📞 技术支持

如有问题或建议，请联系开发团队。

---

**🎉 恭喜！您的智能教育平台已经准备就绪，可以投入使用！**
