import requests
import json

# Test the fixed login endpoint
BASE_URL = "http://localhost:8000"

def test_fixed_login():
    """Test login with the new API path"""
    url = f"{BASE_URL}/api/v1/auth/token"
    data = {
        "username": "testuser",
        "password": "testpass123"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"Login Status: {response.status_code}")
        result = response.json()
        print(f"Login Response: {result}")
        
        if response.status_code == 200 and "access_token" in result:
            print("✅ Login successful with new API path!")
            return result["access_token"]
        else:
            print("❌ Login failed")
            return None
    except Exception as e:
        print(f"Login Error: {e}")
        return None

def test_protected_endpoint_new_path(token):
    """Test protected endpoint with new path"""
    url = f"{BASE_URL}/api/v1/protected-test"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(url, headers=headers)
        print(f"Protected Endpoint Status: {response.status_code}")
        print(f"Protected Endpoint Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Protected Endpoint Error: {e}")
        return False

def main():
    print("=== Testing Fixed Login API ===")
    
    # Test login with new path
    token = test_fixed_login()
    if token:
        print("\n=== Testing Protected Endpoint ===")
        if test_protected_endpoint_new_path(token):
            print("✅ All tests passed! Login fix successful!")
        else:
            print("❌ Protected endpoint test failed")
    else:
        print("❌ Login test failed")

if __name__ == "__main__":
    main()
