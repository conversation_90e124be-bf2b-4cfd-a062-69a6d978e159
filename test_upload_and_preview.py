import requests
import json
import os
import tempfile
from PIL import Image
import time

def print_header(title):
    print("\n" + "="*70)
    print(f"🎓 {title}")
    print("="*70)

def print_success(message):
    print(f"✅ {message}")

def print_error(message):
    print(f"❌ {message}")

def print_info(message):
    print(f"ℹ️ {message}")

def create_test_files():
    """Create test files for upload testing"""
    test_files = {}
    temp_dir = tempfile.mkdtemp()
    
    # 1. Create a test image
    image_file = os.path.join(temp_dir, "test_image.png")
    img = Image.new('RGB', (300, 200), color='lightblue')
    img.save(image_file, 'PNG')
    test_files['image'] = image_file
    
    # 2. Create a test text file
    text_file = os.path.join(temp_dir, "test_document.txt")
    with open(text_file, 'w', encoding='utf-8') as f:
        f.write("""这是一个测试文档

内容包括：
1. 中文文本测试
2. 英文 English text test
3. 数字测试 123456
4. 特殊符号测试 !@#$%^&*()

这个文件用于测试文件上传和预览功能。
""")
    test_files['text'] = text_file
    
    return test_files, temp_dir

def test_backend_api():
    """Test backend API endpoints"""
    print_header("后端API测试")
    
    api_url = "http://localhost:8000/api/v1"
    
    # Test 1: Check backend health
    print("\n📋 Test 1: 后端健康检查")
    try:
        response = requests.get("http://localhost:8000/", timeout=10)
        if response.status_code == 200:
            print_success("后端服务正常运行")
        else:
            print_error(f"后端服务异常: {response.status_code}")
            return False
    except Exception as e:
        print_error(f"后端连接失败: {e}")
        return False
    
    # Test 2: Teacher login
    print("\n📋 Test 2: 教师登录测试")
    try:
        login_response = requests.post(f"{api_url}/auth/token", 
                                     json={"username": "teacher_zhang", "password": "teacher123456"},
                                     timeout=10)
        
        if login_response.status_code == 200:
            teacher_token = login_response.json()["access_token"]
            teacher_headers = {"Authorization": f"Bearer {teacher_token}"}
            print_success("教师登录成功")
        else:
            print_error(f"教师登录失败: {login_response.json()}")
            return False
    except Exception as e:
        print_error(f"教师登录异常: {e}")
        return False
    
    # Test 3: Get courses
    print("\n📋 Test 3: 获取课程列表")
    try:
        courses_response = requests.get(f"{api_url}/courses/", headers=teacher_headers, timeout=10)
        if courses_response.status_code == 200:
            courses = courses_response.json()
            if courses:
                test_course = courses[0]
                print_success(f"获取课程成功: {test_course['title']} (ID: {test_course['id']})")
                return teacher_headers, test_course
            else:
                print_error("没有可用的课程")
                return False
        else:
            print_error(f"获取课程失败: {courses_response.json()}")
            return False
    except Exception as e:
        print_error(f"获取课程异常: {e}")
        return False

def test_file_upload(teacher_headers, test_course):
    """Test file upload functionality"""
    print_header("文件上传功能测试")
    
    api_url = "http://localhost:8000/api/v1"
    
    # Create test files
    print("\n📋 Step 1: 创建测试文件")
    try:
        test_files, temp_dir = create_test_files()
        print_success(f"创建了 {len(test_files)} 个测试文件")
        for file_type, file_path in test_files.items():
            file_size = os.path.getsize(file_path)
            print_info(f"  {file_type}: {os.path.basename(file_path)} ({file_size} bytes)")
    except Exception as e:
        print_error(f"创建测试文件失败: {e}")
        return False
    
    uploaded_materials = []
    
    # Test uploading each file type
    for file_type, file_path in test_files.items():
        print(f"\n📋 Step 2.{len(uploaded_materials)+1}: 上传 {file_type} 文件")
        
        try:
            # Get upload credentials
            filename = os.path.basename(file_path)
            material_type = 'image' if file_type == 'image' else 'text'
            
            params = {
                "course_id": test_course['id'],
                "original_filename": filename,
                "file_type": material_type
            }
            
            print_info(f"请求上传凭证: {params}")
            upload_creds_response = requests.post(f"{api_url}/materials/upload-credentials", 
                                                params=params, headers=teacher_headers, timeout=10)
            
            if upload_creds_response.status_code == 200:
                upload_info = upload_creds_response.json()
                upload_url = upload_info['upload_url']
                object_name = upload_info['object_name']
                print_success(f"获取上传凭证成功: {object_name}")
                
                # Upload file
                with open(file_path, 'rb') as f:
                    file_content = f.read()
                
                mime_types = {
                    'image': 'image/png',
                    'text': 'text/plain'
                }
                
                print_info(f"上传文件到: {upload_url}")
                upload_response = requests.put(upload_url, 
                                             data=file_content,
                                             headers={"Content-Type": mime_types[file_type]},
                                             timeout=30)
                
                if upload_response.status_code in [200, 204]:
                    print_success(f"{file_type}文件上传成功")
                    
                    # Create material record
                    material_record = {
                        "title": f"测试{file_type}文件",
                        "description": f"这是一个用于测试的{file_type}格式文件",
                        "file_type": material_type,
                        "file_path_minio": object_name,
                        "file_size_bytes": len(file_content)
                    }
                    
                    print_info(f"创建材料记录: {material_record['title']}")
                    material_response = requests.post(f"{api_url}/materials/{test_course['id']}",
                                                    json=material_record, 
                                                    headers=teacher_headers, 
                                                    timeout=10)
                    
                    if material_response.status_code == 201:
                        material = material_response.json()
                        uploaded_materials.append(material)
                        print_success(f"{file_type}文件记录创建成功: {material['title']} (ID: {material['id']})")
                    else:
                        print_error(f"{file_type}文件记录创建失败: {material_response.json()}")
                else:
                    print_error(f"{file_type}文件上传失败: {upload_response.status_code} - {upload_response.text}")
            else:
                print_error(f"获取{file_type}文件上传凭证失败: {upload_creds_response.json()}")
                
        except Exception as e:
            print_error(f"上传{file_type}文件异常: {e}")
    
    # Cleanup
    try:
        import shutil
        shutil.rmtree(temp_dir)
        print_success("临时文件清理完成")
    except Exception as e:
        print_info(f"清理临时文件时出现问题: {e}")
    
    return uploaded_materials

def test_frontend_accessibility():
    """Test frontend accessibility"""
    print_header("前端可访问性测试")
    
    ports = [3001, 3000, 3002, 3003]
    
    for port in ports:
        try:
            response = requests.get(f"http://localhost:{port}", timeout=5)
            if response.status_code == 200:
                print_success(f"前端运行在端口 {port}")
                return port
        except:
            continue
    
    print_error("未找到运行中的前端应用")
    return None

def test_material_preview(teacher_headers, uploaded_materials):
    """Test material preview functionality"""
    print_header("文件预览功能测试")
    
    api_url = "http://localhost:8000/api/v1"
    
    if not uploaded_materials:
        print_error("没有上传的材料可供测试")
        return False
    
    for material in uploaded_materials:
        print(f"\n📋 测试材料预览: {material['title']}")
        
        try:
            # Test getting material details
            material_response = requests.get(f"{api_url}/materials/{material['id']}", 
                                           headers=teacher_headers, timeout=10)
            
            if material_response.status_code == 200:
                material_details = material_response.json()
                print_success(f"获取材料详情成功")
                print_info(f"  标题: {material_details['title']}")
                print_info(f"  类型: {material_details['file_type']}")
                print_info(f"  大小: {material_details['file_size_bytes']} bytes")
                print_info(f"  路径: {material_details['file_path_minio']}")
            else:
                print_error(f"获取材料详情失败: {material_response.json()}")
                
        except Exception as e:
            print_error(f"测试材料预览异常: {e}")
    
    return True

def main():
    print("🎓 文件上传和预览功能完整测试")
    print("=" * 80)
    
    print_info("此测试验证以下功能:")
    print_info("1. 后端API连接和认证")
    print_info("2. 文件上传功能")
    print_info("3. 文件预览功能")
    print_info("4. 前端应用可访问性")
    
    # Test 1: Backend API
    backend_result = test_backend_api()
    if not backend_result:
        print_error("后端API测试失败，停止测试")
        return False
    
    teacher_headers, test_course = backend_result
    
    # Test 2: File upload
    uploaded_materials = test_file_upload(teacher_headers, test_course)
    
    # Test 3: Material preview
    preview_success = test_material_preview(teacher_headers, uploaded_materials)
    
    # Test 4: Frontend accessibility
    frontend_port = test_frontend_accessibility()
    
    # Generate final report
    print_header("完整功能测试结果")
    
    print(f"📊 测试结果:")
    print(f"   🔧 后端API: ✅ 正常")
    print(f"   📁 文件上传: {'✅ 正常' if uploaded_materials else '❌ 异常'}")
    print(f"   👁️ 文件预览: {'✅ 正常' if preview_success else '❌ 异常'}")
    print(f"   🎨 前端应用: {'✅ 正常' if frontend_port else '❌ 异常'}")
    
    if uploaded_materials and preview_success and frontend_port:
        print("\n🎉 所有功能测试通过！")
        print("\n🔗 访问地址:")
        print(f"   🎨 前端应用: http://localhost:{frontend_port}")
        print(f"   📚 课程管理: http://localhost:{frontend_port}/teacher/courses")
        print(f"   🔧 后端API: http://localhost:8000")
        print(f"   📖 API文档: http://localhost:8000/docs")
        
        print("\n👥 测试账号:")
        print("   👨‍🏫 教师账号: teacher_zhang / teacher123456")
        
        print("\n🎯 测试建议:")
        print("   1. 打开前端应用进行手动测试")
        print("   2. 尝试上传不同格式的文件")
        print("   3. 检查文件预览功能")
        print("   4. 验证拖拽上传功能")
        
        return True
    else:
        print("\n⚠️ 部分功能存在问题，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    print("\n" + "=" * 80)
    print("🎯 文件上传和预览功能测试完成！")
    print("=" * 80)
