import React, { useEffect, useState } from 'react';
import { teacherApi, authApi } from '../api';
import { formatDateTime } from '../utils';

function ClassManagement() {
  const [classes, setClasses] = useState([]);
  const [newClassName, setNewClassName] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [createError, setCreateError] = useState('');

  const [selectedClassId, setSelectedClassId] = useState(null);
  const [studentsToAdd, setStudentsToAdd] = useState(''); // Comma-separated student IDs
  const [addStudentError, setAddStudentError] = useState('');
  const [addStudentSuccess, setAddStudentSuccess] = useState('');
  const [studentsInSelectedClass, setStudentsInSelectedClass] = useState([]);
  const [showStudentsModal, setShowStudentsModal] = useState(false);

  const fetchClasses = async () => {
    try {
      const data = await teacherApi.getClasses();
      setClasses(data);
      setLoading(false);
    } catch (err) {
      setError(err.detail || 'Failed to fetch classes');
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchClasses();
  }, []);

  const handleCreateClass = async (e) => {
    e.preventDefault();
    setCreateError('');
    try {
      await teacherApi.createClass({ name: newClassName });
      setNewClassName('');
      await fetchClasses();
      alert('班级创建成功！');
    } catch (err) {
      setCreateError(err.detail || '创建班级失败');
    }
  };

  const handleDeleteClass = async (classId) => {
    if (window.confirm('确定删除此班级及其所有学生关联吗？此操作不可逆！')) {
      try {
        await teacherApi.deleteClass(classId);
        await fetchClasses();
        alert('班级删除成功！');
      } catch (err) {
        alert('删除班级失败: ' + (err.detail || err.message));
      }
    }
  };

  const handleAddStudents = async (e) => {
    e.preventDefault();
    setAddStudentError('');
    setAddStudentSuccess('');
    if (!selectedClassId || !studentsToAdd.trim()) {
      setAddStudentError('请选择班级并输入学生ID。');
      return;
    }

    const studentIds = studentsToAdd.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
    if (studentIds.length === 0) {
        setAddStudentError('请输入有效的学生ID列表 (逗号分隔)。');
        return;
    }

    try {
      await teacherApi.addStudentsToClass(selectedClassId, studentIds);
      setAddStudentSuccess('学生添加成功！');
      setStudentsToAdd('');
      await fetchClasses(); // Refresh class list to update student count
      if (showStudentsModal) { // If modal is open, refresh student list in modal
        await fetchStudentsInClass(selectedClassId);
      }
    } catch (err) {
      setAddStudentError(err.detail || '添加学生失败');
    }
  };

  const fetchStudentsInClass = async (classId) => {
    try {
      const students = await teacherApi.getStudentsInClass(classId);
      setStudentsInSelectedClass(students);
    } catch (err) {
      setAddStudentError(err.detail || '无法获取班级学生列表');
    }
  };

  const handleViewStudents = async (classId) => {
    setSelectedClassId(classId);
    await fetchStudentsInClass(classId);
    setShowStudentsModal(true);
  };

  const handleRemoveStudent = async (classId, studentId) => {
    if (window.confirm('确定将此学生从班级中移除吗？')) {
      try {
        await teacherApi.removeStudentFromClass(classId, studentId);
        alert('学生移除成功！');
        await fetchStudentsInClass(classId); // Refresh student list in modal
        await fetchClasses(); // Refresh class list to update student count
      } catch (err) {
        alert('移除学生失败: ' + (err.detail || err.message));
      }
    }
  };


  if (loading) return <div>Loading classes...</div>;
  if (error) return <div className="error">{error}</div>;

  return (
    <div className="container">
      <h2>班级管理</h2>

      <div className="dashboard-card" style={{ marginBottom: '20px' }}>
        <h3>创建新班级</h3>
        <form onSubmit={handleCreateClass}>
          <div>
            <label>班级名称:</label>
            <input type="text" value={newClassName} onChange={(e) => setNewClassName(e.target.value)} required />
          </div>
          {createError && <p className="error">{createError}</p>}
          <button type="submit" disabled={!newClassName}>创建班级</button>
        </form>
      </div>

      <div className="dashboard-card" style={{ marginBottom: '20px', backgroundColor: '#f8f9fa', border: '2px solid #007bff' }}>
        <h3 style={{ color: '#007bff', marginBottom: '15px' }}>📚 添加学生到班级</h3>

        {/* Help Information */}
        <div style={{
          backgroundColor: '#e3f2fd',
          padding: '15px',
          borderRadius: '6px',
          marginBottom: '20px',
          border: '1px solid #2196f3'
        }}>
          <h4 style={{ margin: '0 0 10px 0', color: '#1976d2' }}>💡 使用说明</h4>
          <ul style={{ margin: '0', paddingLeft: '20px', color: '#333' }}>
            <li>首先选择要添加学生的班级</li>
            <li>然后输入学生ID（可以输入多个，用逗号分隔）</li>
            <li>可用的测试学生ID: <strong>1</strong> (testuser)</li>
            <li>示例输入: 1 或 1,2,3</li>
          </ul>
        </div>

        <form onSubmit={handleAddStudents} style={{ display: 'grid', gap: '15px' }}>
          <div>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold', color: '#495057' }}>
              选择班级:
            </label>
            <select
              value={selectedClassId || ''}
              onChange={(e) => setSelectedClassId(e.target.value)}
              required
              style={{
                width: '100%',
                padding: '8px 12px',
                border: '1px solid #ced4da',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            >
              <option value="">-- 请选择班级 --</option>
              {classes.map(cls => (
                <option key={cls.id} value={cls.id}>
                  {cls.name} (当前学生数: {cls.student_count || 0})
                </option>
              ))}
            </select>
          </div>

          <div>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold', color: '#495057' }}>
              学生ID (逗号分隔):
            </label>
            <input
              type="text"
              value={studentsToAdd}
              onChange={(e) => setStudentsToAdd(e.target.value)}
              placeholder="例如: 1 或 1,2,3"
              required
              style={{
                width: '100%',
                padding: '8px 12px',
                border: '1px solid #ced4da',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            />
            <small style={{ color: '#6c757d', fontSize: '12px' }}>
              提示: 输入数字ID，多个ID用逗号分隔
            </small>
          </div>

          {/* Status Messages */}
          {addStudentError && (
            <div style={{
              padding: '10px',
              backgroundColor: '#f8d7da',
              color: '#721c24',
              border: '1px solid #f5c6cb',
              borderRadius: '4px'
            }}>
              ❌ {addStudentError}
            </div>
          )}

          {addStudentSuccess && (
            <div style={{
              padding: '10px',
              backgroundColor: '#d4edda',
              color: '#155724',
              border: '1px solid #c3e6cb',
              borderRadius: '4px'
            }}>
              ✅ {addStudentSuccess}
            </div>
          )}

          {/* Button Status Indicator */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
            <button
              type="submit"
              disabled={!selectedClassId || !studentsToAdd.trim()}
              style={{
                padding: '10px 20px',
                backgroundColor: (!selectedClassId || !studentsToAdd.trim()) ? '#6c757d' : '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: (!selectedClassId || !studentsToAdd.trim()) ? 'not-allowed' : 'pointer',
                fontSize: '14px',
                fontWeight: 'bold'
              }}
            >
              {(!selectedClassId || !studentsToAdd.trim()) ? '🔒 添加学生 (请完成输入)' : '✅ 添加学生'}
            </button>

            {/* Status Indicator */}
            <div style={{ fontSize: '12px', color: '#6c757d' }}>
              {!selectedClassId && !studentsToAdd.trim() && '请选择班级并输入学生ID'}
              {!selectedClassId && studentsToAdd.trim() && '请选择班级'}
              {selectedClassId && !studentsToAdd.trim() && '请输入学生ID'}
              {selectedClassId && studentsToAdd.trim() && '✅ 可以添加学生'}
            </div>
          </div>
        </form>
      </div>

      <h3>我的班级列表</h3>
      {classes.length === 0 ? (
        <p>您还没有创建任何班级。</p>
      ) : (
        <ul>
          {classes.map((cls) => (
            <li key={cls.id} className="material-item">
              <span>
                {cls.name} (学生人数: {cls.student_count}) - {cls.teacher_name}
              </span>
              <div>
                <button onClick={() => handleViewStudents(cls.id)}>查看学生</button>
                <button onClick={() => alert('编辑功能待实现')}>编辑</button>
                <button onClick={() => handleDeleteClass(cls.id)} style={{ backgroundColor: 'red' }}>删除</button>
              </div>
            </li>
          ))}
        </ul>
      )}

      {showStudentsModal && (
        <div style={{ position: 'fixed', top: 0, left: 0, width: '100%', height: '100%', backgroundColor: 'rgba(0,0,0,0.5)', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <div style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px', width: '600px', maxHeight: '80%', overflowY: 'auto' }}>
            <h3>班级 {selectedClassId} 学生列表</h3>
            {studentsInSelectedClass.length === 0 ? (
              <p>班级中没有学生。</p>
            ) : (
              <ul>
                {studentsInSelectedClass.map(student => (
                  <li key={student.id} className="material-item">
                    <span>{student.full_name || student.username} (ID: {student.id}) - {student.email}</span>
                    <button onClick={() => handleRemoveStudent(selectedClassId, student.id)} style={{ backgroundColor: 'orange' }}>移除</button>
                  </li>
                ))}
              </ul>
            )}
            <button onClick={() => setShowStudentsModal(false)}>关闭</button>
          </div>
        </div>
      )}
    </div>
  );
}

export default ClassManagement;