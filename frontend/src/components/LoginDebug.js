import React, { useState } from 'react';
import { authApi } from '../api';

const LoginDebug = () => {
    const [username, setUsername] = useState('teacher_zhang');
    const [password, setPassword] = useState('teacher123456');
    const [result, setResult] = useState('');
    const [loading, setLoading] = useState(false);

    const testLogin = async () => {
        setLoading(true);
        setResult('正在测试登录...');

        try {
            console.log('🔍 开始登录测试');
            console.log('API_BASE_URL:', process.env.REACT_APP_BACKEND_API_URL);
            console.log('用户名:', username);
            console.log('密码:', password ? '***' : '空');

            const response = await authApi.login(username, password);
            
            console.log('✅ 登录成功:', response);
            
            setResult(`✅ 登录成功！
Token: ${response.access_token.substring(0, 50)}...
Token类型: ${response.token_type}
API地址: ${process.env.REACT_APP_BACKEND_API_URL}`);

        } catch (error) {
            console.error('❌ 登录失败:', error);
            
            let errorMessage = '❌ 登录失败\n';
            
            if (error.response) {
                errorMessage += `状态码: ${error.response.status}\n`;
                errorMessage += `错误信息: ${error.response.data?.detail || '未知错误'}\n`;
                errorMessage += `响应数据: ${JSON.stringify(error.response.data, null, 2)}`;
            } else if (error.request) {
                errorMessage += `网络错误: 无法连接到服务器\n`;
                errorMessage += `请求配置: ${JSON.stringify(error.config, null, 2)}`;
            } else {
                errorMessage += `错误: ${error.message}`;
            }
            
            errorMessage += `\nAPI地址: ${process.env.REACT_APP_BACKEND_API_URL}`;
            
            setResult(errorMessage);
        } finally {
            setLoading(false);
        }
    };

    const testApiConnection = async () => {
        setLoading(true);
        setResult('正在测试API连接...');

        try {
            const response = await fetch('http://localhost:8000/', {
                method: 'GET'
            });

            if (response.ok) {
                setResult(`✅ API连接成功
状态码: ${response.status}
后端服务正常运行
API地址: ${process.env.REACT_APP_BACKEND_API_URL}`);
            } else {
                setResult(`❌ API连接失败
状态码: ${response.status}
API地址: ${process.env.REACT_APP_BACKEND_API_URL}`);
            }
        } catch (error) {
            setResult(`❌ API连接错误: ${error.message}
API地址: ${process.env.REACT_APP_BACKEND_API_URL}`);
        } finally {
            setLoading(false);
        }
    };

    const testDirectFetch = async () => {
        setLoading(true);
        setResult('正在测试直接fetch调用...');

        try {
            const response = await fetch('http://localhost:8000/api/v1/auth/token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username, password })
            });

            const data = await response.json();

            if (response.ok) {
                setResult(`✅ 直接fetch登录成功！
Token: ${data.access_token.substring(0, 50)}...
Token类型: ${data.token_type}
状态码: ${response.status}`);
            } else {
                setResult(`❌ 直接fetch登录失败
状态码: ${response.status}
错误: ${data.detail || '未知错误'}
响应数据: ${JSON.stringify(data, null, 2)}`);
            }
        } catch (error) {
            setResult(`❌ 直接fetch错误: ${error.message}`);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div style={{ 
            padding: '20px', 
            maxWidth: '800px', 
            margin: '0 auto',
            fontFamily: 'monospace',
            backgroundColor: '#f5f5f5',
            borderRadius: '10px'
        }}>
            <h2>🔍 登录调试工具</h2>
            
            <div style={{ marginBottom: '20px' }}>
                <div style={{ marginBottom: '10px' }}>
                    <label>用户名:</label>
                    <input 
                        type="text" 
                        value={username} 
                        onChange={(e) => setUsername(e.target.value)}
                        style={{ marginLeft: '10px', padding: '5px', width: '200px' }}
                    />
                </div>
                <div style={{ marginBottom: '10px' }}>
                    <label>密码:</label>
                    <input 
                        type="password" 
                        value={password} 
                        onChange={(e) => setPassword(e.target.value)}
                        style={{ marginLeft: '10px', padding: '5px', width: '200px' }}
                    />
                </div>
            </div>

            <div style={{ marginBottom: '20px' }}>
                <button 
                    onClick={testLogin} 
                    disabled={loading}
                    style={{ 
                        padding: '10px 20px', 
                        marginRight: '10px',
                        backgroundColor: '#007bff',
                        color: 'white',
                        border: 'none',
                        borderRadius: '5px',
                        cursor: loading ? 'not-allowed' : 'pointer'
                    }}
                >
                    测试API登录
                </button>
                
                <button 
                    onClick={testApiConnection} 
                    disabled={loading}
                    style={{ 
                        padding: '10px 20px', 
                        marginRight: '10px',
                        backgroundColor: '#28a745',
                        color: 'white',
                        border: 'none',
                        borderRadius: '5px',
                        cursor: loading ? 'not-allowed' : 'pointer'
                    }}
                >
                    测试API连接
                </button>

                <button 
                    onClick={testDirectFetch} 
                    disabled={loading}
                    style={{ 
                        padding: '10px 20px',
                        backgroundColor: '#ffc107',
                        color: 'black',
                        border: 'none',
                        borderRadius: '5px',
                        cursor: loading ? 'not-allowed' : 'pointer'
                    }}
                >
                    测试直接Fetch
                </button>
            </div>

            <div style={{ marginBottom: '20px' }}>
                <h3>环境信息:</h3>
                <div>API地址: {process.env.REACT_APP_BACKEND_API_URL || '未设置'}</div>
                <div>WebSocket地址: {process.env.REACT_APP_BACKEND_WS_URL || '未设置'}</div>
                <div>当前URL: {window.location.href}</div>
            </div>

            <div style={{ 
                backgroundColor: '#fff', 
                padding: '15px', 
                borderRadius: '5px',
                border: '1px solid #ddd',
                minHeight: '200px',
                whiteSpace: 'pre-wrap',
                fontSize: '12px'
            }}>
                <h3>测试结果:</h3>
                {result || '点击按钮开始测试...'}
            </div>

            <div style={{ marginTop: '20px', fontSize: '12px', color: '#666' }}>
                <h3>调试提示:</h3>
                <ul>
                    <li>检查浏览器控制台的网络请求</li>
                    <li>确认后端服务在 http://localhost:8000 运行</li>
                    <li>检查CORS配置是否正确</li>
                    <li>验证环境变量是否正确设置</li>
                </ul>
            </div>
        </div>
    );
};

export default LoginDebug;
