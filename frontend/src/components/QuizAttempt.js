import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { studentApi } from '../api';
import { getQuizTypeDisplay, getQuestionTypeDisplay, formatDateTime } from '../utils';

function QuizAttempt() {
  const { quizId } = useParams();
  const navigate = useNavigate();
  const [quiz, setQuiz] = useState(null);
  const [answers, setAnswers] = useState({}); // {questionId: answer}
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [submitError, setSubmitError] = useState('');

  useEffect(() => {
    const fetchQuiz = async () => {
      try {
        const data = await studentApi.getQuizForStudent(quizId);
        setQuiz(data);
        setLoading(false);
      } catch (err) {
        setError(err.detail || 'Failed to fetch quiz');
        setLoading(false);
      }
    };

    fetchQuiz();
  }, [quizId]);

  const handleAnswerChange = (questionId, value, questionType) => {
    setAnswers(prev => {
      if (questionType === 'multiple_choice') {
        const currentAnswers = prev[questionId] || [];
        if (currentAnswers.includes(value)) {
          return { ...prev, [questionId]: currentAnswers.filter(item => item !== value) };
        } else {
          return { ...prev, [questionId]: [...currentAnswers, value] };
        }
      } else {
        return { ...prev, [questionId]: value };
      }
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitError('');

    const submittedAnswers = quiz.questions.map(q => ({
      question_id: q.id,
      answer: answers[q.id] || (q.question_type === 'multiple_choice' ? [] : '') // 确保未作答时有默认值
    }));

    try {
      const result = await studentApi.submitQuizAttempt(quizId, { submitted_answers: submittedAnswers });
      alert('测验提交成功！');
      navigate(`/student/quizzes/results/${result.id}`); // 导航到结果页
    } catch (err) {
      setSubmitError(err.detail || '提交失败');
    }
  };

  if (loading) return <div>Loading quiz...</div>;
  if (error) return <div className="error">{error}</div>;
  if (!quiz) return <div>Quiz not found.</div>;

  return (
    <div className="container">
      <h2>{quiz.title} ({getQuizTypeDisplay(quiz.quiz_type)})</h2>
      <p>描述: {quiz.description}</p>
      {quiz.due_date && <p>截止日期: {formatDateTime(quiz.due_date)}</p>}

      <form onSubmit={handleSubmit}>
        {quiz.questions.length === 0 ? (
          <p>此测验暂无问题。</p>
        ) : (
          quiz.questions.map((question, index) => (
            <div key={question.id} style={{ marginBottom: '20px', border: '1px solid #ddd', padding: '15px', borderRadius: '8px' }}>
              <h3>{index + 1}. {question.question_text} (分值: {question.score})</h3>
              <p>类型: {getQuestionTypeDisplay(question.question_type)}</p>

              {question.question_type === 'single_choice' && (
                <div>
                  {question.options && Object.entries(question.options).map(([key, value]) => (
                    <div key={key}>
                      <input
                        type="radio"
                        id={`q${question.id}_${key}`}
                        name={`q${question.id}`}
                        value={key}
                        checked={answers[question.id] === key}
                        onChange={(e) => handleAnswerChange(question.id, e.target.value, question.question_type)}
                      />
                      <label htmlFor={`q${question.id}_${key}`}>{key}. {value}</label>
                    </div>
                  ))}
                </div>
              )}

              {question.question_type === 'multiple_choice' && (
                <div>
                  {question.options && Object.entries(question.options).map(([key, value]) => (
                    <div key={key}>
                      <input
                        type="checkbox"
                        id={`q${question.id}_${key}`}
                        name={`q${question.id}`}
                        value={key}
                        checked={(answers[question.id] || []).includes(key)}
                        onChange={(e) => handleAnswerChange(question.id, e.target.value, question.question_type)}
                      />
                      <label htmlFor={`q${question.id}_${key}`}>{key}. {value}</label>
                    </div>
                  ))}
                </div>
              )}

              {question.question_type === 'fill_in_blank' && (
                <div>
                  <input
                    type="text"
                    value={answers[question.id] || ''}
                    onChange={(e) => handleAnswerChange(question.id, e.target.value, question.question_type)}
                  />
                </div>
              )}

              {question.question_type === 'short_answer' && (
                <div>
                  <textarea
                    value={answers[question.id] || ''}
                    onChange={(e) => handleAnswerChange(question.id, e.target.value, question.question_type)}
                    rows="5"
                  ></textarea>
                </div>
              )}
            </div>
          ))
        )}
        {submitError && <p className="error">{submitError}</p>}
        <button type="submit">提交测验</button>
      </form>
    </div>
  );
}

export default QuizAttempt;