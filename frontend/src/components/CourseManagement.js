import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { teacherApi, materialApi } from '../api';
import { formatDateTime } from '../utils';

function CourseManagement() {
  const [courses, setCourses] = useState([]);
  const [newCourseTitle, setNewCourseTitle] = useState('');
  const [newCourseDesc, setNewCourseDesc] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [createError, setCreateError] = useState('');

  // States for selected course for material upload
  const [selectedCourseId, setSelectedCourseId] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [uploadError, setUploadError] = useState('');
  const [uploadSuccess, setUploadSuccess] = useState('');
  const [materialTitle, setMaterialTitle] = useState('');
  const [materialDescription, setMaterialDescription] = useState('');

  // DeepSeek analysis states
  const [analyzingMaterial, setAnalyzingMaterial] = useState(null);
  const [analysisResults, setAnalysisResults] = useState({});
  const [showAnalysisModal, setShowAnalysisModal] = useState(false);
  const [currentAnalysis, setCurrentAnalysis] = useState(null);

  // Material deletion states
  const [deletingMaterial, setDeletingMaterial] = useState(null);

  // Course materials view states
  const [courseMaterials, setCourseMaterials] = useState({});

  const fetchCourses = async () => {
    try {
      const data = await teacherApi.getCourses();
      setCourses(data);
      setLoading(false);
    } catch (err) {
      setError(err.detail || 'Failed to fetch courses');
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCourses();
  }, []);

  useEffect(() => {
    if (courses.length > 0) {
      fetchAllMaterials();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [courses]);

  const fetchAllMaterials = async () => {
    try {
      const courseMatMap = {};

      for (const course of courses) {
        try {
          const courseMats = await materialApi.getMaterialsByCourse(course.id);
          courseMatMap[course.id] = courseMats;
        } catch (err) {
          console.error(`Failed to fetch materials for course ${course.id}:`, err);
          courseMatMap[course.id] = [];
        }
      }

      setCourseMaterials(courseMatMap);
    } catch (err) {
      console.error('Failed to fetch materials:', err);
    }
  };

  const handleCreateCourse = async (e) => {
    e.preventDefault();
    setCreateError('');
    try {
      await teacherApi.createCourse({ title: newCourseTitle, description: newCourseDesc });
      setNewCourseTitle('');
      setNewCourseDesc('');
      await fetchCourses(); // Refresh list
      alert('课程创建成功！');
    } catch (err) {
      setCreateError(err.detail || '创建课程失败');
    }
  };

  const handleDeleteCourse = async (courseId) => {
    if (window.confirm('确定删除此课程及其所有内容吗？此操作不可逆！')) {
      try {
        await teacherApi.deleteCourse(courseId);
        await fetchCourses(); // Refresh list
        alert('课程删除成功！');
      } catch (err) {
        alert('删除课程失败: ' + (err.detail || err.message));
      }
    }
  };

  // Material Upload Logic
  const handleFileChange = (e) => {
    const file = e.target.files[0];
    setSelectedFile(file);
    setUploadError('');
    setUploadSuccess('');

    // Auto-fill title from filename if not set
    if (file && !materialTitle) {
      setMaterialTitle(file.name.split('.')[0]);
    }
  };

  const handleMaterialUpload = async (e) => {
    e.preventDefault();
    if (!selectedFile || !selectedCourseId) {
      setUploadError('请选择文件和课程');
      return;
    }

    setUploading(true);
    setUploadError('');
    setUploadSuccess('');

    try {
      const materialType = mapMimeToMaterialType(selectedFile.type);
      const { upload_url, object_name } = await materialApi.getUploadCredentials(
        selectedCourseId,
        selectedFile.name,
        materialType
      );

      // 直接上传到 Minio
      await materialApi.uploadFileToMinio(upload_url, selectedFile, selectedFile.type); // file.type 是完整的MIME类型

      // 通知后端创建资料记录
      const materialData = {
        title: materialTitle || selectedFile.name.split('.')[0],
        description: materialDescription || `Uploaded from ${selectedFile.name}`,
        file_type: materialType,
        file_path_minio: object_name,
        file_size_bytes: selectedFile.size,
        // duration_seconds: getDurationOfMedia(selectedFile), // For video/audio, requires more complex client-side logic
      };
      await materialApi.createMaterialRecord(selectedCourseId, materialData);

      setUploadSuccess('文件上传并记录成功！');
      setSelectedFile(null);
      setSelectedCourseId(null);
      setMaterialTitle('');
      setMaterialDescription('');
      // Reset file input
      const fileInput = document.querySelector('input[type="file"]');
      if (fileInput) fileInput.value = '';
      // Refresh materials list
      await fetchAllMaterials();
    } catch (err) {
      setUploadError(err.detail || '文件上传失败');
      console.error(err);
    } finally {
      setUploading(false);
    }
  };

  // Material Deletion Function
  const handleDeleteMaterial = async (materialId, materialTitle) => {
    if (window.confirm(`确定删除材料 "${materialTitle}" 吗？此操作不可逆！`)) {
      setDeletingMaterial(materialId);
      try {
        await materialApi.deleteMaterial(materialId);
        setUploadSuccess('材料删除成功！');
        // Refresh materials list
        await fetchAllMaterials();
      } catch (err) {
        setUploadError('删除材料失败: ' + (err.detail || err.message));
      } finally {
        setDeletingMaterial(null);
      }
    }
  };

  // DeepSeek Analysis Functions
  const handleAnalyzeMaterial = async (materialId) => {
    setAnalyzingMaterial(materialId);
    try {
      const analysis = await materialApi.analyzeMaterialWithDeepSeek(materialId);
      setAnalysisResults(prev => ({ ...prev, [materialId]: analysis }));
      setCurrentAnalysis(analysis);
      setShowAnalysisModal(true);
    } catch (err) {
      alert('分析失败: ' + (err.detail || err.message));
    } finally {
      setAnalyzingMaterial(null);
    }
  };

  const closeAnalysisModal = () => {
    setShowAnalysisModal(false);
    setCurrentAnalysis(null);
  };

  // Helper function to get file type icon
  const getFileTypeIcon = (fileType) => {
    const icons = {
      'pdf': '📄',
      'word': '📝',
      'text': '📃',
      'image': '🖼️',
      'ppt': '📊'
    };
    return icons[fileType] || '📄';
  };

  if (loading) return <div>Loading courses...</div>;
  if (error) return <div className="error">{error}</div>;

  return (
    <div className="container">
      <h2>课程管理</h2>

      <div className="dashboard-card" style={{ marginBottom: '20px' }}>
        <h3>创建新课程</h3>
        <form onSubmit={handleCreateCourse}>
          <div>
            <label>课程标题:</label>
            <input type="text" value={newCourseTitle} onChange={(e) => setNewCourseTitle(e.target.value)} required />
          </div>
          <div>
            <label>描述:</label>
            <textarea value={newCourseDesc} onChange={(e) => setNewCourseDesc(e.target.value)} rows="3"></textarea>
          </div>
          {createError && <p className="error">{createError}</p>}
          <button type="submit" disabled={!newCourseTitle}>创建课程</button>
        </form>
      </div>

      <div className="dashboard-card" style={{ marginBottom: '20px' }}>
        <h3>📁 上传教学资料</h3>
        <form onSubmit={handleMaterialUpload} style={{ display: 'grid', gap: '15px' }}>
          <div>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>选择课程:</label>
            <select
              value={selectedCourseId || ''}
              onChange={(e) => setSelectedCourseId(e.target.value)}
              required
              style={{ width: '100%', padding: '8px', borderRadius: '4px', border: '1px solid #ddd' }}
            >
              <option value="">-- 请选择课程 --</option>
              {courses.map(course => (
                <option key={course.id} value={course.id}>{course.title}</option>
              ))}
            </select>
          </div>

          <div>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>选择文件:</label>
            <input
              type="file"
              onChange={handleFileChange}
              required
              style={{ width: '100%', padding: '8px', borderRadius: '4px', border: '1px solid #ddd' }}
            />
          </div>

          <div>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>资料标题:</label>
            <input
              type="text"
              value={materialTitle}
              onChange={(e) => setMaterialTitle(e.target.value)}
              placeholder="输入资料标题（可选，默认使用文件名）"
              style={{ width: '100%', padding: '8px', borderRadius: '4px', border: '1px solid #ddd' }}
            />
          </div>

          <div>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>资料描述:</label>
            <textarea
              value={materialDescription}
              onChange={(e) => setMaterialDescription(e.target.value)}
              placeholder="输入资料描述（可选）"
              rows="3"
              style={{ width: '100%', padding: '8px', borderRadius: '4px', border: '1px solid #ddd', resize: 'vertical' }}
            />
          </div>

          {uploadError && <p className="error" style={{ color: '#dc3545', margin: '0' }}>{uploadError}</p>}
          {uploadSuccess && <p className="success" style={{ color: '#28a745', margin: '0' }}>{uploadSuccess}</p>}

          <button
            type="submit"
            disabled={!selectedFile || !selectedCourseId || uploading}
            style={{
              padding: '12px 24px',
              backgroundColor: uploading ? '#6c757d' : '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: uploading ? 'not-allowed' : 'pointer',
              fontSize: '16px',
              fontWeight: 'bold'
            }}
          >
            {uploading ? '📤 上传中...' : '📤 上传资料'}
          </button>
        </form>
      </div>

      <div className="dashboard-card" style={{ marginBottom: '30px' }}>
        <h3>📚 我的课程与资料管理</h3>
        {courses.length === 0 ? (
          <p>您还没有创建任何课程。</p>
        ) : (
          <div style={{ display: 'grid', gap: '20px' }}>
            {courses.map((course) => (
              <div key={course.id} style={{
                border: '1px solid #e0e0e0',
                borderRadius: '8px',
                padding: '20px',
                backgroundColor: '#f8f9fa'
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                  <div>
                    <h4 style={{ margin: '0 0 5px 0', color: '#333' }}>
                      📖 {course.title}
                    </h4>
                    <p style={{ margin: '0', color: '#666', fontSize: '14px' }}>
                      状态: {course.status} | 创建时间: {formatDateTime(course.created_at)}
                    </p>
                    {course.description && (
                      <p style={{ margin: '5px 0 0 0', color: '#555', fontSize: '14px' }}>
                        {course.description}
                      </p>
                    )}
                  </div>
                  <div style={{ display: 'flex', gap: '10px' }}>
                    <Link
                      to={`/teacher/quizzes/${course.id}`}
                      style={{
                        padding: '8px 16px',
                        backgroundColor: '#17a2b8',
                        color: 'white',
                        textDecoration: 'none',
                        borderRadius: '4px',
                        fontSize: '14px'
                      }}
                    >
                      📝 管理测验
                    </Link>
                    <button
                      onClick={() => alert('编辑功能待实现')}
                      style={{
                        padding: '8px 16px',
                        backgroundColor: '#ffc107',
                        color: '#212529',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        fontSize: '14px'
                      }}
                    >
                      ✏️ 编辑
                    </button>
                    <button
                      onClick={() => handleDeleteCourse(course.id)}
                      style={{
                        padding: '8px 16px',
                        backgroundColor: '#dc3545',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        fontSize: '14px'
                      }}
                    >
                      🗑️ 删除
                    </button>
                  </div>
                </div>

                {/* Course Materials Section */}
                <div style={{
                  borderTop: '1px solid #dee2e6',
                  paddingTop: '15px',
                  backgroundColor: 'white',
                  borderRadius: '4px',
                  padding: '15px'
                }}>
                  <h5 style={{ margin: '0 0 10px 0', color: '#495057' }}>
                    📁 课程资料 ({courseMaterials[course.id]?.length || 0})
                  </h5>
                  {courseMaterials[course.id]?.length > 0 ? (
                    <div style={{ display: 'grid', gap: '10px' }}>
                      {courseMaterials[course.id].map((material) => (
                        <div key={material.id} style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          padding: '10px',
                          backgroundColor: '#f1f3f4',
                          borderRadius: '4px',
                          border: '1px solid #e9ecef'
                        }}>
                          <div style={{ flex: 1 }}>
                            <span style={{ fontWeight: 'bold', color: '#333' }}>
                              {getFileTypeIcon(material.file_type)} {material.title}
                            </span>
                            <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
                              {material.file_type} • {(material.file_size_bytes / 1024 / 1024).toFixed(2)} MB • {formatDateTime(material.created_at)}
                            </div>
                          </div>
                          <div style={{ display: 'flex', gap: '8px' }}>
                            <button
                              onClick={() => handleAnalyzeMaterial(material.id)}
                              disabled={analyzingMaterial === material.id}
                              style={{
                                padding: '4px 8px',
                                backgroundColor: analyzingMaterial === material.id ? '#6c757d' : '#007bff',
                                color: 'white',
                                border: 'none',
                                borderRadius: '3px',
                                cursor: analyzingMaterial === material.id ? 'not-allowed' : 'pointer',
                                fontSize: '12px'
                              }}
                            >
                              {analyzingMaterial === material.id ? '🤖 分析中...' : '🤖 AI分析'}
                            </button>
                            {analysisResults[material.id] && (
                              <button
                                onClick={() => {
                                  setCurrentAnalysis(analysisResults[material.id]);
                                  setShowAnalysisModal(true);
                                }}
                                style={{
                                  padding: '4px 8px',
                                  backgroundColor: '#28a745',
                                  color: 'white',
                                  border: 'none',
                                  borderRadius: '3px',
                                  cursor: 'pointer',
                                  fontSize: '12px'
                                }}
                              >
                                📊 查看分析
                              </button>
                            )}
                            <button
                              onClick={() => handleDeleteMaterial(material.id, material.title)}
                              disabled={deletingMaterial === material.id}
                              style={{
                                padding: '4px 8px',
                                backgroundColor: deletingMaterial === material.id ? '#6c757d' : '#dc3545',
                                color: 'white',
                                border: 'none',
                                borderRadius: '3px',
                                cursor: deletingMaterial === material.id ? 'not-allowed' : 'pointer',
                                fontSize: '12px'
                              }}
                            >
                              {deletingMaterial === material.id ? '🗑️ 删除中...' : '🗑️ 删除'}
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p style={{ margin: '0', color: '#6c757d', fontSize: '14px', fontStyle: 'italic' }}>
                      暂无上传的资料
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>



      {/* Analysis Results Modal */}
      {showAnalysisModal && currentAnalysis && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0,0,0,0.5)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            padding: '30px',
            borderRadius: '12px',
            maxWidth: '800px',
            maxHeight: '80vh',
            overflow: 'auto',
            width: '90%'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
              <h3 style={{ margin: 0, color: '#333' }}>🤖 DeepSeek AI 分析结果</h3>
              <button
                onClick={closeAnalysisModal}
                style={{
                  backgroundColor: '#dc3545',
                  color: 'white',
                  border: 'none',
                  padding: '8px 12px',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                ✕ 关闭
              </button>
            </div>

            {currentAnalysis.success ? (
              <div>
                <div style={{ marginBottom: '20px' }}>
                  <h4 style={{ color: '#007bff', borderBottom: '2px solid #007bff', paddingBottom: '5px' }}>
                    📝 内容摘要
                  </h4>
                  <p style={{ lineHeight: '1.6', color: '#333' }}>{currentAnalysis.summary}</p>
                </div>

                <div style={{ marginBottom: '20px' }}>
                  <h4 style={{ color: '#28a745', borderBottom: '2px solid #28a745', paddingBottom: '5px' }}>
                    🎯 关键知识点
                  </h4>
                  <ul style={{ lineHeight: '1.6' }}>
                    {currentAnalysis.key_points?.map((point, index) => (
                      <li key={index} style={{ margin: '8px 0', color: '#333' }}>{point}</li>
                    ))}
                  </ul>
                </div>

                <div style={{ marginBottom: '20px' }}>
                  <h4 style={{ color: '#ffc107', borderBottom: '2px solid #ffc107', paddingBottom: '5px' }}>
                    📚 主要主题
                  </h4>
                  <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                    {currentAnalysis.topics?.map((topic, index) => (
                      <span key={index} style={{
                        backgroundColor: '#fff3cd',
                        color: '#856404',
                        padding: '4px 12px',
                        borderRadius: '16px',
                        fontSize: '14px',
                        border: '1px solid #ffeaa7'
                      }}>
                        {topic}
                      </span>
                    ))}
                  </div>
                </div>

                <div style={{ marginBottom: '20px' }}>
                  <h4 style={{ color: '#6f42c1', borderBottom: '2px solid #6f42c1', paddingBottom: '5px' }}>
                    📊 难度级别
                  </h4>
                  <span style={{
                    backgroundColor: currentAnalysis.difficulty_level === 'beginner' ? '#d4edda' :
                                   currentAnalysis.difficulty_level === 'intermediate' ? '#fff3cd' : '#f8d7da',
                    color: currentAnalysis.difficulty_level === 'beginner' ? '#155724' :
                           currentAnalysis.difficulty_level === 'intermediate' ? '#856404' : '#721c24',
                    padding: '8px 16px',
                    borderRadius: '20px',
                    fontSize: '16px',
                    fontWeight: 'bold'
                  }}>
                    {currentAnalysis.difficulty_level === 'beginner' ? '🟢 初级' :
                     currentAnalysis.difficulty_level === 'intermediate' ? '🟡 中级' : '🔴 高级'}
                  </span>
                </div>

                {currentAnalysis.suggested_questions && currentAnalysis.suggested_questions.length > 0 && (
                  <div style={{ marginBottom: '20px' }}>
                    <h4 style={{ color: '#17a2b8', borderBottom: '2px solid #17a2b8', paddingBottom: '5px' }}>
                      ❓ 建议测试问题
                    </h4>
                    <ol style={{ lineHeight: '1.6' }}>
                      {currentAnalysis.suggested_questions.map((question, index) => (
                        <li key={index} style={{ margin: '8px 0', color: '#333' }}>{question}</li>
                      ))}
                    </ol>
                  </div>
                )}

                {currentAnalysis.learning_objectives && currentAnalysis.learning_objectives.length > 0 && (
                  <div style={{ marginBottom: '20px' }}>
                    <h4 style={{ color: '#fd7e14', borderBottom: '2px solid #fd7e14', paddingBottom: '5px' }}>
                      🎯 学习目标
                    </h4>
                    <ul style={{ lineHeight: '1.6' }}>
                      {currentAnalysis.learning_objectives.map((objective, index) => (
                        <li key={index} style={{ margin: '8px 0', color: '#333' }}>{objective}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ) : (
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <p style={{ color: '#dc3545', fontSize: '18px' }}>❌ 分析失败</p>
                <p style={{ color: '#666' }}>错误信息: {currentAnalysis.error}</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export default CourseManagement;


// Helper to map MIME type to backend MaterialType enum
const mapMimeToMaterialType = (mimeType) => {
    if (mimeType.startsWith('video/')) return 'video';
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.includes('powerpoint')) return 'ppt';
    if (mimeType.includes('pdf')) return 'pdf';
    if (mimeType.includes('word')) return 'word';
    if (mimeType.startsWith('audio/')) return 'audio';
    if (mimeType.startsWith('text/')) return 'text';
    return 'text'; // Default fallback
};