import React, { useState, useEffect } from 'react';
import { teacherApi, materialApi } from '../api';

const TestUpload = () => {
  const [courses, setCourses] = useState([]);
  const [selectedCourseId, setSelectedCourseId] = useState('');
  const [selectedFile, setSelectedFile] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [message, setMessage] = useState('');

  useEffect(() => {
    fetchCourses();
  }, []);

  const fetchCourses = async () => {
    try {
      const coursesData = await teacherApi.getCourses();
      setCourses(coursesData);
      console.log('✅ 获取课程成功:', coursesData);
    } catch (error) {
      console.error('❌ 获取课程失败:', error);
      setMessage('获取课程失败: ' + (error.detail || error.message));
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    setSelectedFile(file);
    setMessage('');
    
    if (file) {
      console.log('📁 选择文件:', file.name, file.type, file.size);
    }
  };

  const mapMimeToMaterialType = (mimeType) => {
    console.log('🔍 检测文件类型:', mimeType);
    
    if (mimeType.startsWith('video/')) return 'video';
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) return 'ppt';
    if (mimeType.includes('pdf')) return 'pdf';
    if (mimeType.includes('word') || mimeType.includes('msword')) return 'word';
    if (mimeType.startsWith('audio/')) return 'audio';
    if (mimeType.startsWith('text/')) return 'text';
    
    return 'text';
  };

  const handleUpload = async (e) => {
    e.preventDefault();
    
    if (!selectedFile || !selectedCourseId) {
      setMessage('请选择文件和课程');
      return;
    }

    console.log('🚀 开始上传:', selectedFile.name, '到课程:', selectedCourseId);
    
    setUploading(true);
    setUploadProgress(0);
    setMessage('');

    try {
      // Step 1: 获取上传凭证
      console.log('📋 Step 1: 获取上传凭证');
      setUploadProgress(10);
      
      const materialType = mapMimeToMaterialType(selectedFile.type);
      console.log('📄 文件类型映射:', selectedFile.type, '->', materialType);
      
      const { upload_url, object_name } = await materialApi.getUploadCredentials(
        selectedCourseId,
        selectedFile.name,
        materialType
      );
      
      console.log('✅ 获取上传凭证成功:', { upload_url, object_name });

      // Step 2: 上传文件
      console.log('📋 Step 2: 上传文件到存储');
      setUploadProgress(20);
      
      await materialApi.uploadFileToMinio(upload_url, selectedFile, selectedFile.type, (progress) => {
        const totalProgress = 20 + (progress * 0.6);
        setUploadProgress(totalProgress);
        console.log('📈 上传进度:', progress.toFixed(1) + '%');
      });
      
      console.log('✅ 文件上传成功');

      // Step 3: 创建材料记录
      console.log('📋 Step 3: 创建材料记录');
      setUploadProgress(80);
      
      const materialData = {
        title: selectedFile.name.split('.')[0],
        description: `上传的${materialType}文件`,
        file_type: materialType,
        file_path_minio: object_name,
        file_size_bytes: selectedFile.size,
      };
      
      console.log('📝 材料数据:', materialData);
      
      const result = await materialApi.createMaterialRecord(selectedCourseId, materialData);
      
      console.log('✅ 材料记录创建成功:', result);
      
      setUploadProgress(100);
      setMessage('✅ 文件上传成功！');
      
      // 重置表单
      setSelectedFile(null);
      setSelectedCourseId('');
      document.getElementById('file-input').value = '';
      
      setTimeout(() => {
        setUploadProgress(0);
        setMessage('');
      }, 3000);

    } catch (error) {
      console.error('❌ 上传失败:', error);
      setMessage('❌ 上传失败: ' + (error.detail || error.message || error.toString()));
      setUploadProgress(0);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div style={{ padding: '20px', maxWidth: '600px', margin: '0 auto' }}>
      <h2>🧪 文件上传测试页面</h2>
      
      <div style={{ 
        backgroundColor: '#f8f9fa', 
        padding: '15px', 
        borderRadius: '8px', 
        marginBottom: '20px',
        border: '1px solid #dee2e6'
      }}>
        <h3>📊 测试状态</h3>
        <p>课程数量: {courses.length}</p>
        <p>选择的文件: {selectedFile ? selectedFile.name : '未选择'}</p>
        <p>选择的课程: {selectedCourseId || '未选择'}</p>
        <p>上传状态: {uploading ? '上传中' : '待上传'}</p>
      </div>

      <form onSubmit={handleUpload} style={{ display: 'grid', gap: '15px' }}>
        <div>
          <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
            选择课程:
          </label>
          <select
            value={selectedCourseId}
            onChange={(e) => setSelectedCourseId(e.target.value)}
            required
            style={{ 
              width: '100%', 
              padding: '8px', 
              borderRadius: '4px', 
              border: '1px solid #ddd' 
            }}
          >
            <option value="">-- 请选择课程 --</option>
            {courses.map(course => (
              <option key={course.id} value={course.id}>
                {course.title}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
            选择文件:
          </label>
          <input
            id="file-input"
            type="file"
            onChange={handleFileChange}
            required
            accept="video/*,image/*,audio/*,.pdf,.doc,.docx,.ppt,.pptx,.txt"
            style={{ 
              width: '100%', 
              padding: '8px', 
              borderRadius: '4px', 
              border: '1px solid #ddd' 
            }}
          />
        </div>

        {/* 文件预览 */}
        {selectedFile && (
          <div style={{
            padding: '15px',
            backgroundColor: '#e3f2fd',
            borderRadius: '8px',
            border: '1px solid #2196f3'
          }}>
            <h4>📎 文件信息</h4>
            <p><strong>文件名:</strong> {selectedFile.name}</p>
            <p><strong>文件类型:</strong> {selectedFile.type}</p>
            <p><strong>文件大小:</strong> {(selectedFile.size / 1024 / 1024).toFixed(2)} MB</p>
            <p><strong>映射类型:</strong> {mapMimeToMaterialType(selectedFile.type)}</p>
            
            {selectedFile.type.startsWith('image/') && (
              <div style={{ marginTop: '10px' }}>
                <img
                  src={URL.createObjectURL(selectedFile)}
                  alt="预览"
                  style={{
                    maxWidth: '200px',
                    maxHeight: '150px',
                    borderRadius: '4px',
                    border: '1px solid #ddd'
                  }}
                  onLoad={(e) => URL.revokeObjectURL(e.target.src)}
                />
              </div>
            )}
          </div>
        )}

        {/* 上传进度 */}
        {uploading && (
          <div style={{ margin: '15px 0' }}>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center', 
              marginBottom: '5px' 
            }}>
              <span>上传进度</span>
              <span>{Math.round(uploadProgress)}%</span>
            </div>
            <div style={{
              width: '100%',
              height: '8px',
              backgroundColor: '#e9ecef',
              borderRadius: '4px',
              overflow: 'hidden'
            }}>
              <div style={{
                width: `${uploadProgress}%`,
                height: '100%',
                backgroundColor: uploadProgress === 100 ? '#28a745' : '#007bff',
                transition: 'width 0.3s ease',
                borderRadius: '4px'
              }} />
            </div>
          </div>
        )}

        {/* 消息显示 */}
        {message && (
          <div style={{
            padding: '10px',
            borderRadius: '4px',
            backgroundColor: message.includes('❌') ? '#f8d7da' : '#d4edda',
            color: message.includes('❌') ? '#721c24' : '#155724',
            border: `1px solid ${message.includes('❌') ? '#f5c6cb' : '#c3e6cb'}`
          }}>
            {message}
          </div>
        )}

        <button
          type="submit"
          disabled={!selectedFile || !selectedCourseId || uploading}
          style={{
            padding: '12px 24px',
            backgroundColor: uploading ? '#6c757d' : '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: uploading ? 'not-allowed' : 'pointer',
            fontSize: '16px',
            fontWeight: 'bold'
          }}
        >
          {uploading ? '📤 上传中...' : '📤 测试上传'}
        </button>
      </form>

      <div style={{ 
        marginTop: '30px', 
        padding: '15px', 
        backgroundColor: '#fff3cd', 
        borderRadius: '8px',
        border: '1px solid #ffeaa7'
      }}>
        <h4>🔍 调试信息</h4>
        <p>请打开浏览器开发者工具的Console查看详细日志</p>
        <p>如果上传失败，请检查网络连接和后端服务状态</p>
      </div>
    </div>
  );
};

export default TestUpload;
