import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link, Navigate } from 'react-router-dom';
import { useAuth } from './contexts/AuthContext';
import Auth from './components/Auth';
import LoginDebug from './components/LoginDebug';
import StudentDashboard from './components/StudentDashboard';
import TeacherDashboard from './components/TeacherDashboard';
import CourseDetail from './components/CourseDetail';
import MaterialViewer from './components/MaterialViewer';
import QuizAttempt from './components/QuizAttempt';
import QuizResult from './components/QuizResult';
import AdminPanel from './components/AdminPanel';
import Notifications from './components/Notifications';
import CourseManagement from './components/CourseManagement'; // 教师管理课程
import ClassManagement from './components/ClassManagement';   // 教师管理班级
import QuizManagement from './components/QuizManagement';     // 教师管理测验
import ReportViewer from './components/ReportViewer';         // 教师查看报告
import UserProfile from './components/UserProfile';           // 用户个人信息

// 私有路由组件，确保用户已登录且角色匹配
const PrivateRoute = ({ children, allowedRoles }) => {
  const { isAuthenticated, userRole } = useAuth();
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  if (allowedRoles && !allowedRoles.includes(userRole)) {
    return <Navigate to="/" replace />; // 未授权角色重定向到首页
  }
  return children;
};

function App() {
  const { isAuthenticated, userRole, logout } = useAuth();

  return (
    <Router>
      <nav>
        <span>Education Platform</span>
        <div>
          {isAuthenticated ? (
            <>
              {userRole === 'student' && <Link to="/student">学生仪表盘</Link>}
              {userRole === 'teacher' && <Link to="/teacher">教师管理</Link>}
              {userRole === 'admin' && <Link to="/admin">管理员面板</Link>}
              <Link to="/notifications">通知</Link>
              <Link to="/profile">我的资料</Link>
              <button onClick={logout}>登出</button>
            </>
          ) : (
            <>
              <Link to="/login">登录</Link>
              <Link to="/register">注册</Link>
            </>
          )}
        </div>
      </nav>

      <div className="container">
        <Routes>
          <Route path="/" element={isAuthenticated ? <Navigate to={userRole === 'student' ? "/student" : "/teacher"} replace /> : <Navigate to="/login" replace />} />
          <Route path="/login" element={<Auth type="login" />} />
          <Route path="/register" element={<Auth type="register" />} />
          <Route path="/debug" element={<LoginDebug />} />

          {/* 学生路由 */}
          <Route path="/student" element={<PrivateRoute allowedRoles={['student']}><StudentDashboard /></PrivateRoute>} />
          <Route path="/student/courses/:courseId" element={<PrivateRoute allowedRoles={['student']}><CourseDetail /></PrivateRoute>} />
          <Route path="/student/materials/:materialId" element={<PrivateRoute allowedRoles={['student']}><MaterialViewer /></PrivateRoute>} />
          <Route path="/student/quizzes/:quizId" element={<PrivateRoute allowedRoles={['student']}><QuizAttempt /></PrivateRoute>} />
          <Route path="/student/quizzes/results/:attemptId" element={<PrivateRoute allowedRoles={['student']}><QuizResult /></PrivateRoute>} />
          <Route path="/notifications" element={<PrivateRoute><Notifications /></PrivateRoute>} />
          <Route path="/profile" element={<PrivateRoute><UserProfile /></PrivateRoute>} />


          {/* 教师路由 */}
          <Route path="/teacher" element={<PrivateRoute allowedRoles={['teacher', 'admin']}><TeacherDashboard /></PrivateRoute>} />
          <Route path="/teacher/courses" element={<PrivateRoute allowedRoles={['teacher', 'admin']}><CourseManagement /></PrivateRoute>} />
          <Route path="/teacher/classes" element={<PrivateRoute allowedRoles={['teacher', 'admin']}><ClassManagement /></PrivateRoute>} />
          <Route path="/teacher/quizzes/:courseId?" element={<PrivateRoute allowedRoles={['teacher', 'admin']}><QuizManagement /></PrivateRoute>} /> {/* 测验管理页，可选课程ID */}
          <Route path="/teacher/reports" element={<PrivateRoute allowedRoles={['teacher', 'admin']}><ReportViewer /></PrivateRoute>} />

          {/* 管理员路由 */}
          <Route path="/admin" element={<PrivateRoute allowedRoles={['admin']}><AdminPanel /></PrivateRoute>} />

          {/* 默认跳转 */}
          <Route path="*" element={<h1>404 Not Found</h1>} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;