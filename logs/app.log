2025-05-26 19:45:40,314 - app.main - WARNING - Database initialization failed: 'utf-8' codec can't decode byte 0xd6 in position 61: invalid continuation byte. Continuing without database...
2025-05-26 19:45:40,315 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 19:45:40,316 - app.main - INFO - Minio bucket check completed.
2025-05-26 19:45:40,316 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 19:45:40,317 - app.main - INFO - AI model loaded successfully.
2025-05-26 19:45:40,317 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 19:45:40,318 - app.main - INFO - Milvus initialized successfully.
2025-05-26 19:45:40,319 - app.main - INFO - Application started successfully.
2025-05-26 19:54:04,783 - app.main - INFO - Database tables created successfully.
2025-05-26 19:54:04,783 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 19:54:04,784 - app.main - INFO - Minio bucket check completed.
2025-05-26 19:54:04,784 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 19:54:04,784 - app.main - INFO - AI model loaded successfully.
2025-05-26 19:54:04,784 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 19:54:04,784 - app.main - INFO - Milvus initialized successfully.
2025-05-26 19:54:04,784 - app.main - INFO - Application started successfully.
2025-05-26 19:55:42,141 - app.main - ERROR - Unhandled Internal Server Error: bcrypt: no backends available -- recommend you install one (e.g. 'pip install bcrypt')
Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 214, in run_endpoint_function
    return await run_in_threadpool(dependant.call, **values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\concurrency.py", line 37, in run_in_threadpool
    return await anyio.to_thread.run_sync(func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\anyio\to_thread.py", line 56, in run_sync
    return await get_async_backend().run_sync_in_worker_thread(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\anyio\_backends\_asyncio.py", line 2461, in run_sync_in_worker_thread
    return await future
           ^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\anyio\_backends\_asyncio.py", line 962, in run
    result = context.run(func, *args)
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\auth\router.py", line 34, in register_user
    new_user = create_user(db=db, user=user)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 25, in create_user
    hashed_password = get_password_hash(user.password)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\auth\security.py", line 18, in get_password_hash
    return pwd_context.hash(password)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\context.py", line 2258, in hash
    return record.hash(secret, **kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\utils\handlers.py", line 779, in hash
    self.checksum = self._calc_checksum(secret)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\handlers\bcrypt.py", line 591, in _calc_checksum
    self._stub_requires_backend()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\utils\handlers.py", line 2254, in _stub_requires_backend
    cls.set_backend()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\utils\handlers.py", line 2156, in set_backend
    return owner.set_backend(name, dryrun=dryrun)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\utils\handlers.py", line 2176, in set_backend
    raise default_error
passlib.exc.MissingBackendError: bcrypt: no backends available -- recommend you install one (e.g. 'pip install bcrypt')
2025-05-26 19:56:12,953 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-26 19:56:13,195 - app.auth.router - INFO - User 'testuser' (student) registered successfully with ID: 1.
2025-05-26 19:56:15,247 - app.main - WARNING - Validation Error: [{'loc': ('body',), 'msg': 'Input should be a valid dictionary or object to extract fields from', 'type': 'model_attributes_type'}]
2025-05-26 19:56:28,435 - app.main - INFO - Database tables created successfully.
2025-05-26 19:56:28,435 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 19:56:28,435 - app.main - INFO - Minio bucket check completed.
2025-05-26 19:56:28,435 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 19:56:28,437 - app.main - INFO - AI model loaded successfully.
2025-05-26 19:56:28,437 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 19:56:28,437 - app.main - INFO - Milvus initialized successfully.
2025-05-26 19:56:28,437 - app.main - INFO - Application started successfully.
2025-05-26 19:56:39,942 - app.auth.router - WARNING - Registration failed: Username 'testuser' already registered.
2025-05-26 19:56:39,942 - app.main - ERROR - Custom HTTP Exception occurred: Username already registered, Code: USERNAME_EXISTS, Status: 400, Data: None
2025-05-26 19:56:53,648 - app.main - INFO - Database tables created successfully.
2025-05-26 19:56:53,648 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 19:56:53,650 - app.main - INFO - Minio bucket check completed.
2025-05-26 19:56:53,650 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 19:56:53,651 - app.main - INFO - AI model loaded successfully.
2025-05-26 19:56:53,651 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 19:56:53,653 - app.main - INFO - Milvus initialized successfully.
2025-05-26 19:56:53,654 - app.main - INFO - Application started successfully.
2025-05-26 19:57:03,624 - app.auth.router - WARNING - Registration failed: Username 'testuser' already registered.
2025-05-26 19:57:03,625 - app.main - ERROR - Custom HTTP Exception occurred: Username already registered, Code: USERNAME_EXISTS, Status: 400, Data: None
2025-05-26 19:57:05,646 - app.main - WARNING - Validation Error: [{'loc': ('body',), 'msg': 'Input should be a valid dictionary or object to extract fields from', 'type': 'model_attributes_type'}]
2025-05-26 19:57:43,010 - app.main - INFO - Database tables created successfully.
2025-05-26 19:57:43,010 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 19:57:43,011 - app.main - INFO - Minio bucket check completed.
2025-05-26 19:57:43,011 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 19:57:43,012 - app.main - INFO - AI model loaded successfully.
2025-05-26 19:57:43,012 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 19:57:43,012 - app.main - INFO - Milvus initialized successfully.
2025-05-26 19:57:43,012 - app.main - INFO - Application started successfully.
2025-05-26 19:57:57,997 - app.auth.router - WARNING - Registration failed: Username 'testuser' already registered.
2025-05-26 19:57:57,999 - app.main - ERROR - Custom HTTP Exception occurred: Username already registered, Code: USERNAME_EXISTS, Status: 400, Data: None
2025-05-26 19:58:00,044 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-26 19:58:00,286 - app.auth.router - INFO - User 'testuser' logged in successfully.
2025-05-26 19:58:02,312 - app.main - INFO - Protected test endpoint accessed by testuser
2025-05-26 20:07:33,995 - app.main - INFO - Database tables created successfully.
2025-05-26 20:07:33,996 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:07:33,996 - app.main - INFO - Minio bucket check completed.
2025-05-26 20:07:33,996 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:07:33,997 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:07:33,997 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:07:33,998 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:07:33,998 - app.main - INFO - Application started successfully.
2025-05-26 20:10:46,170 - app.main - INFO - Database tables created successfully.
2025-05-26 20:10:46,170 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:10:46,170 - app.main - INFO - Minio bucket check completed.
2025-05-26 20:10:46,170 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:10:46,171 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:10:46,171 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:10:46,171 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:10:46,172 - app.main - INFO - Application started successfully.
2025-05-26 20:10:46,176 - app.main - INFO - Database tables created successfully.
2025-05-26 20:10:46,176 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:10:46,176 - app.main - INFO - Minio bucket check completed.
2025-05-26 20:10:46,177 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:10:46,177 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:10:46,177 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:10:46,177 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:10:46,177 - app.main - INFO - Application started successfully.
2025-05-26 20:11:03,788 - app.main - INFO - Database tables created successfully.
2025-05-26 20:11:03,788 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:11:03,789 - app.main - INFO - Minio bucket check completed.
2025-05-26 20:11:03,790 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:11:03,790 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:11:03,791 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:11:03,791 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:11:03,792 - app.main - INFO - Application started successfully.
2025-05-26 20:11:03,800 - app.main - INFO - Database tables created successfully.
2025-05-26 20:11:03,801 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:11:03,801 - app.main - INFO - Minio bucket check completed.
2025-05-26 20:11:03,801 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:11:03,802 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:11:03,802 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:11:03,802 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:11:03,803 - app.main - INFO - Application started successfully.
2025-05-26 20:11:39,707 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-26 20:11:40,080 - app.auth.router - INFO - User 'testuser' logged in successfully.
2025-05-26 20:11:42,116 - app.main - INFO - Protected test endpoint accessed by testuser
2025-05-26 20:12:47,540 - app.main - INFO - Database tables created successfully.
2025-05-26 20:12:47,541 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:12:47,541 - app.main - INFO - Minio bucket check completed.
2025-05-26 20:12:47,541 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:12:47,541 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:12:47,541 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:12:47,542 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:12:47,542 - app.main - INFO - Application started successfully.
2025-05-26 20:12:47,777 - app.main - INFO - Database tables created successfully.
2025-05-26 20:12:47,778 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:12:47,778 - app.main - INFO - Minio bucket check completed.
2025-05-26 20:12:47,779 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:12:47,779 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:12:47,779 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:12:47,780 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:12:47,780 - app.main - INFO - Application started successfully.
2025-05-26 20:12:59,236 - app.main - INFO - Database tables created successfully.
2025-05-26 20:12:59,237 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:12:59,237 - app.main - INFO - Minio bucket check completed.
2025-05-26 20:12:59,237 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:12:59,238 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:12:59,238 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:12:59,239 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:12:59,239 - app.main - INFO - Application started successfully.
2025-05-26 20:12:59,468 - app.main - INFO - Database tables created successfully.
2025-05-26 20:12:59,468 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:12:59,469 - app.main - INFO - Minio bucket check completed.
2025-05-26 20:12:59,469 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:12:59,469 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:12:59,469 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:12:59,470 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:12:59,470 - app.main - INFO - Application started successfully.
2025-05-26 20:13:46,906 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-26 20:13:47,132 - app.auth.router - INFO - User 'testuser' logged in successfully.
2025-05-26 20:13:49,161 - app.main - INFO - Protected test endpoint accessed by testuser
2025-05-26 20:15:16,629 - app.auth.router - INFO - User 'testuser' logged in successfully.
2025-05-26 20:15:16,638 - app.api.v1.routers.users - INFO - User 1 (testuser) accessed /users/me.
2025-05-26 20:15:16,692 - app.main - ERROR - Unhandled Internal Server Error: Don't know how to join to <Mapper at 0x20f88d08c90; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\api\v1\routers\students.py", line 81, in get_my_dashboard_summary
    recent_notifications_data = get_notifications_for_user(db, student_id)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 655, in get_notifications_for_user
    ClassStudent.student_id == student_id).distinct().all()
                                                      ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2704, in all
    return self._iter().all()  # type: ignore
           ^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1629, in _execute_clauseelement
    compiled_sql, extracted_params, cache_hit = elem._compile_w_cache(
                                                ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 711, in _compile_w_cache
    compiled_sql = self._compiler(
                   ^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 320, in _compiler
    return dialect.statement_compiler(dialect, self, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 1446, in __init__
    Compiled.__init__(self, dialect, statement, **kwargs)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 886, in __init__
    self.string = self.process(self.statement, **compile_kwargs)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 932, in process
    return obj._compiler_dispatch(self, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\visitors.py", line 141, in _compiler_dispatch
    return meth(self, **kw)  # type: ignore  # noqa: E501
           ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 4728, in visit_select
    compile_state = select_stmt._compile_state_factory(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\base.py", line 687, in create_for_statement
    return klass.create_for_statement(statement, compiler, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 447, in create_for_statement
    return cls._create_orm_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1246, in _create_orm_context
    self._setup_for_generate()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1281, in _setup_for_generate
    self._join(query._setup_joins, self._entities)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1922, in _join
    self._join_left_to_right(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1957, in _join_left_to_right
    ) = self._join_determine_implicit_left_side(
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 2121, in _join_determine_implicit_left_side
    raise sa_exc.InvalidRequestError(
sqlalchemy.exc.InvalidRequestError: Don't know how to join to <Mapper at 0x20f88d08c90; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
2025-05-26 20:15:16,728 - app.main - ERROR - Unhandled Internal Server Error: Don't know how to join to <Mapper at 0x20f88d08c90; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\api\v1\routers\students.py", line 81, in get_my_dashboard_summary
    recent_notifications_data = get_notifications_for_user(db, student_id)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 655, in get_notifications_for_user
    ClassStudent.student_id == student_id).distinct().all()
                                                      ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2704, in all
    return self._iter().all()  # type: ignore
           ^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1629, in _execute_clauseelement
    compiled_sql, extracted_params, cache_hit = elem._compile_w_cache(
                                                ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 711, in _compile_w_cache
    compiled_sql = self._compiler(
                   ^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 320, in _compiler
    return dialect.statement_compiler(dialect, self, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 1446, in __init__
    Compiled.__init__(self, dialect, statement, **kwargs)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 886, in __init__
    self.string = self.process(self.statement, **compile_kwargs)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 932, in process
    return obj._compiler_dispatch(self, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\visitors.py", line 141, in _compiler_dispatch
    return meth(self, **kw)  # type: ignore  # noqa: E501
           ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 4728, in visit_select
    compile_state = select_stmt._compile_state_factory(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\base.py", line 687, in create_for_statement
    return klass.create_for_statement(statement, compiler, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 447, in create_for_statement
    return cls._create_orm_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1246, in _create_orm_context
    self._setup_for_generate()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1281, in _setup_for_generate
    self._join(query._setup_joins, self._entities)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1922, in _join
    self._join_left_to_right(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1957, in _join_left_to_right
    ) = self._join_determine_implicit_left_side(
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 2121, in _join_determine_implicit_left_side
    raise sa_exc.InvalidRequestError(
sqlalchemy.exc.InvalidRequestError: Don't know how to join to <Mapper at 0x20f88d08c90; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
2025-05-26 20:15:17,000 - app.api.v1.routers.notifications - INFO - Attempting to connect WebSocket for user 1 (testuser).
2025-05-26 20:15:17,000 - app.utils.websocket_manager - INFO - User 1 connected via WebSocket
2025-05-26 20:15:17,001 - app.api.v1.routers.notifications - INFO - WebSocket connection established for user 1.
2025-05-26 20:15:17,048 - app.main - ERROR - Unhandled Internal Server Error: Don't know how to join to <Mapper at 0x20f88d08c90; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\api\v1\routers\students.py", line 81, in get_my_dashboard_summary
    recent_notifications_data = get_notifications_for_user(db, student_id)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 655, in get_notifications_for_user
    ClassStudent.student_id == student_id).distinct().all()
                                                      ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2704, in all
    return self._iter().all()  # type: ignore
           ^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1629, in _execute_clauseelement
    compiled_sql, extracted_params, cache_hit = elem._compile_w_cache(
                                                ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 711, in _compile_w_cache
    compiled_sql = self._compiler(
                   ^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 320, in _compiler
    return dialect.statement_compiler(dialect, self, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 1446, in __init__
    Compiled.__init__(self, dialect, statement, **kwargs)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 886, in __init__
    self.string = self.process(self.statement, **compile_kwargs)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 932, in process
    return obj._compiler_dispatch(self, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\visitors.py", line 141, in _compiler_dispatch
    return meth(self, **kw)  # type: ignore  # noqa: E501
           ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 4728, in visit_select
    compile_state = select_stmt._compile_state_factory(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\base.py", line 687, in create_for_statement
    return klass.create_for_statement(statement, compiler, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 447, in create_for_statement
    return cls._create_orm_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1246, in _create_orm_context
    self._setup_for_generate()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1281, in _setup_for_generate
    self._join(query._setup_joins, self._entities)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1922, in _join
    self._join_left_to_right(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1957, in _join_left_to_right
    ) = self._join_determine_implicit_left_side(
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 2121, in _join_determine_implicit_left_side
    raise sa_exc.InvalidRequestError(
sqlalchemy.exc.InvalidRequestError: Don't know how to join to <Mapper at 0x20f88d08c90; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
2025-05-26 20:15:28,891 - app.utils.websocket_manager - INFO - User 1 disconnected from WebSocket
2025-05-26 20:15:28,891 - app.api.v1.routers.notifications - INFO - WebSocket disconnected for user 1.
2025-05-26 20:15:32,561 - app.main - ERROR - Unhandled Internal Server Error: Don't know how to join to <Mapper at 0x20f88d08c90; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\api\v1\routers\students.py", line 81, in get_my_dashboard_summary
    recent_notifications_data = get_notifications_for_user(db, student_id)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 655, in get_notifications_for_user
    ClassStudent.student_id == student_id).distinct().all()
                                                      ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2704, in all
    return self._iter().all()  # type: ignore
           ^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1629, in _execute_clauseelement
    compiled_sql, extracted_params, cache_hit = elem._compile_w_cache(
                                                ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 711, in _compile_w_cache
    compiled_sql = self._compiler(
                   ^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 320, in _compiler
    return dialect.statement_compiler(dialect, self, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 1446, in __init__
    Compiled.__init__(self, dialect, statement, **kwargs)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 886, in __init__
    self.string = self.process(self.statement, **compile_kwargs)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 932, in process
    return obj._compiler_dispatch(self, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\visitors.py", line 141, in _compiler_dispatch
    return meth(self, **kw)  # type: ignore  # noqa: E501
           ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 4728, in visit_select
    compile_state = select_stmt._compile_state_factory(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\base.py", line 687, in create_for_statement
    return klass.create_for_statement(statement, compiler, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 447, in create_for_statement
    return cls._create_orm_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1246, in _create_orm_context
    self._setup_for_generate()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1281, in _setup_for_generate
    self._join(query._setup_joins, self._entities)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1922, in _join
    self._join_left_to_right(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1957, in _join_left_to_right
    ) = self._join_determine_implicit_left_side(
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 2121, in _join_determine_implicit_left_side
    raise sa_exc.InvalidRequestError(
sqlalchemy.exc.InvalidRequestError: Don't know how to join to <Mapper at 0x20f88d08c90; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
2025-05-26 20:15:32,865 - app.api.v1.routers.notifications - INFO - Attempting to connect WebSocket for user 1 (testuser).
2025-05-26 20:15:32,866 - app.utils.websocket_manager - INFO - User 1 connected via WebSocket
2025-05-26 20:15:32,866 - app.api.v1.routers.notifications - INFO - WebSocket connection established for user 1.
2025-05-26 20:15:32,882 - app.main - ERROR - Unhandled Internal Server Error: Don't know how to join to <Mapper at 0x20f88d08c90; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\api\v1\routers\students.py", line 81, in get_my_dashboard_summary
    recent_notifications_data = get_notifications_for_user(db, student_id)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 655, in get_notifications_for_user
    ClassStudent.student_id == student_id).distinct().all()
                                                      ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2704, in all
    return self._iter().all()  # type: ignore
           ^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1629, in _execute_clauseelement
    compiled_sql, extracted_params, cache_hit = elem._compile_w_cache(
                                                ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 711, in _compile_w_cache
    compiled_sql = self._compiler(
                   ^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 320, in _compiler
    return dialect.statement_compiler(dialect, self, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 1446, in __init__
    Compiled.__init__(self, dialect, statement, **kwargs)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 886, in __init__
    self.string = self.process(self.statement, **compile_kwargs)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 932, in process
    return obj._compiler_dispatch(self, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\visitors.py", line 141, in _compiler_dispatch
    return meth(self, **kw)  # type: ignore  # noqa: E501
           ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 4728, in visit_select
    compile_state = select_stmt._compile_state_factory(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\base.py", line 687, in create_for_statement
    return klass.create_for_statement(statement, compiler, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 447, in create_for_statement
    return cls._create_orm_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1246, in _create_orm_context
    self._setup_for_generate()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1281, in _setup_for_generate
    self._join(query._setup_joins, self._entities)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1922, in _join
    self._join_left_to_right(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1957, in _join_left_to_right
    ) = self._join_determine_implicit_left_side(
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 2121, in _join_determine_implicit_left_side
    raise sa_exc.InvalidRequestError(
sqlalchemy.exc.InvalidRequestError: Don't know how to join to <Mapper at 0x20f88d08c90; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
2025-05-26 20:15:32,897 - app.main - ERROR - Unhandled Internal Server Error: Don't know how to join to <Mapper at 0x20f88d08c90; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\api\v1\routers\students.py", line 81, in get_my_dashboard_summary
    recent_notifications_data = get_notifications_for_user(db, student_id)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 655, in get_notifications_for_user
    ClassStudent.student_id == student_id).distinct().all()
                                                      ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2704, in all
    return self._iter().all()  # type: ignore
           ^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1629, in _execute_clauseelement
    compiled_sql, extracted_params, cache_hit = elem._compile_w_cache(
                                                ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 711, in _compile_w_cache
    compiled_sql = self._compiler(
                   ^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 320, in _compiler
    return dialect.statement_compiler(dialect, self, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 1446, in __init__
    Compiled.__init__(self, dialect, statement, **kwargs)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 886, in __init__
    self.string = self.process(self.statement, **compile_kwargs)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 932, in process
    return obj._compiler_dispatch(self, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\visitors.py", line 141, in _compiler_dispatch
    return meth(self, **kw)  # type: ignore  # noqa: E501
           ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 4728, in visit_select
    compile_state = select_stmt._compile_state_factory(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\base.py", line 687, in create_for_statement
    return klass.create_for_statement(statement, compiler, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 447, in create_for_statement
    return cls._create_orm_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1246, in _create_orm_context
    self._setup_for_generate()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1281, in _setup_for_generate
    self._join(query._setup_joins, self._entities)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1922, in _join
    self._join_left_to_right(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1957, in _join_left_to_right
    ) = self._join_determine_implicit_left_side(
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 2121, in _join_determine_implicit_left_side
    raise sa_exc.InvalidRequestError(
sqlalchemy.exc.InvalidRequestError: Don't know how to join to <Mapper at 0x20f88d08c90; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
2025-05-26 20:17:50,681 - app.auth.router - INFO - User 'teacher001' (student) registered successfully with ID: 2.
2025-05-26 20:17:53,052 - app.auth.router - INFO - User 'teacher001' logged in successfully.
2025-05-26 20:17:55,074 - app.main - INFO - Protected test endpoint accessed by teacher001
2025-05-26 20:17:57,100 - app.auth.dependencies - WARNING - User teacher001 (role: student) tried to access teacher-only endpoint.
2025-05-26 20:17:59,141 - app.auth.dependencies - WARNING - User teacher001 (role: student) tried to access teacher-only endpoint.
2025-05-26 20:18:01,193 - app.auth.dependencies - WARNING - User teacher001 (role: student) tried to access teacher-only endpoint.
2025-05-26 20:18:03,231 - app.auth.dependencies - WARNING - User teacher001 (role: student) tried to access teacher-only endpoint.
2025-05-26 20:18:29,107 - app.utils.websocket_manager - INFO - User 1 disconnected from WebSocket
2025-05-26 20:18:29,109 - app.api.v1.routers.notifications - INFO - WebSocket disconnected for user 1.
2025-05-26 20:18:30,622 - app.main - INFO - Database tables created successfully.
2025-05-26 20:18:30,622 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:18:30,623 - app.main - INFO - Minio bucket check completed.
2025-05-26 20:18:30,623 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:18:30,623 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:18:30,624 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:18:30,624 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:18:30,624 - app.main - INFO - Application started successfully.
2025-05-26 20:18:30,688 - app.main - INFO - Database tables created successfully.
2025-05-26 20:18:30,688 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:18:30,689 - app.main - INFO - Minio bucket check completed.
2025-05-26 20:18:30,689 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:18:30,689 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:18:30,691 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:18:30,692 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:18:30,692 - app.main - INFO - Application started successfully.
2025-05-26 20:19:17,761 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-26 20:19:18,173 - app.auth.router - INFO - User 'teacher_zhang' (teacher) registered successfully with ID: 3.
2025-05-26 20:19:20,566 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 20:19:22,602 - app.main - INFO - Protected test endpoint accessed by teacher_zhang
2025-05-26 20:19:28,732 - app.api.v1.routers.courses - INFO - Course 'Python��̻���' (ID: 1) created by teacher 3.
2025-05-26 20:19:30,783 - app.api.v1.routers.classes - INFO - Class 'Python��̰༶A' (ID: 1) created by teacher 3.
2025-05-26 20:19:32,855 - app.api.v1.routers.quizzes - INFO - Quiz 'Python��������' (ID: 1) created by teacher 3 for course 1.
2025-05-26 20:23:26,568 - app.api.v1.routers.users - INFO - User 1 (testuser) accessed /users/me.
2025-05-26 20:23:26,576 - app.api.v1.routers.users - INFO - User 1 (testuser) accessed /users/me.
2025-05-26 20:23:26,597 - app.main - ERROR - Unhandled Internal Server Error: Don't know how to join to <Mapper at 0x208e57c0fd0; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\api\v1\routers\students.py", line 81, in get_my_dashboard_summary
    recent_notifications_data = get_notifications_for_user(db, student_id)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 655, in get_notifications_for_user
    ClassStudent.student_id == student_id).distinct().all()
                                                      ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2704, in all
    return self._iter().all()  # type: ignore
           ^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1629, in _execute_clauseelement
    compiled_sql, extracted_params, cache_hit = elem._compile_w_cache(
                                                ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 711, in _compile_w_cache
    compiled_sql = self._compiler(
                   ^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 320, in _compiler
    return dialect.statement_compiler(dialect, self, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 1446, in __init__
    Compiled.__init__(self, dialect, statement, **kwargs)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 886, in __init__
    self.string = self.process(self.statement, **compile_kwargs)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 932, in process
    return obj._compiler_dispatch(self, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\visitors.py", line 141, in _compiler_dispatch
    return meth(self, **kw)  # type: ignore  # noqa: E501
           ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 4728, in visit_select
    compile_state = select_stmt._compile_state_factory(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\base.py", line 687, in create_for_statement
    return klass.create_for_statement(statement, compiler, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 447, in create_for_statement
    return cls._create_orm_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1246, in _create_orm_context
    self._setup_for_generate()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1281, in _setup_for_generate
    self._join(query._setup_joins, self._entities)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1922, in _join
    self._join_left_to_right(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1957, in _join_left_to_right
    ) = self._join_determine_implicit_left_side(
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 2121, in _join_determine_implicit_left_side
    raise sa_exc.InvalidRequestError(
sqlalchemy.exc.InvalidRequestError: Don't know how to join to <Mapper at 0x208e57c0fd0; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
2025-05-26 20:23:26,837 - app.main - ERROR - Unhandled Internal Server Error: Don't know how to join to <Mapper at 0x208e57c0fd0; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\api\v1\routers\students.py", line 81, in get_my_dashboard_summary
    recent_notifications_data = get_notifications_for_user(db, student_id)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 655, in get_notifications_for_user
    ClassStudent.student_id == student_id).distinct().all()
                                                      ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2704, in all
    return self._iter().all()  # type: ignore
           ^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1629, in _execute_clauseelement
    compiled_sql, extracted_params, cache_hit = elem._compile_w_cache(
                                                ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 711, in _compile_w_cache
    compiled_sql = self._compiler(
                   ^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 320, in _compiler
    return dialect.statement_compiler(dialect, self, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 1446, in __init__
    Compiled.__init__(self, dialect, statement, **kwargs)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 886, in __init__
    self.string = self.process(self.statement, **compile_kwargs)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 932, in process
    return obj._compiler_dispatch(self, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\visitors.py", line 141, in _compiler_dispatch
    return meth(self, **kw)  # type: ignore  # noqa: E501
           ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 4728, in visit_select
    compile_state = select_stmt._compile_state_factory(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\base.py", line 687, in create_for_statement
    return klass.create_for_statement(statement, compiler, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 447, in create_for_statement
    return cls._create_orm_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1246, in _create_orm_context
    self._setup_for_generate()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1281, in _setup_for_generate
    self._join(query._setup_joins, self._entities)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1922, in _join
    self._join_left_to_right(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1957, in _join_left_to_right
    ) = self._join_determine_implicit_left_side(
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 2121, in _join_determine_implicit_left_side
    raise sa_exc.InvalidRequestError(
sqlalchemy.exc.InvalidRequestError: Don't know how to join to <Mapper at 0x208e57c0fd0; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
2025-05-26 20:23:26,882 - app.main - ERROR - Unhandled Internal Server Error: Don't know how to join to <Mapper at 0x208e57c0fd0; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\api\v1\routers\students.py", line 81, in get_my_dashboard_summary
    recent_notifications_data = get_notifications_for_user(db, student_id)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 655, in get_notifications_for_user
    ClassStudent.student_id == student_id).distinct().all()
                                                      ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2704, in all
    return self._iter().all()  # type: ignore
           ^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1629, in _execute_clauseelement
    compiled_sql, extracted_params, cache_hit = elem._compile_w_cache(
                                                ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 711, in _compile_w_cache
    compiled_sql = self._compiler(
                   ^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 320, in _compiler
    return dialect.statement_compiler(dialect, self, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 1446, in __init__
    Compiled.__init__(self, dialect, statement, **kwargs)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 886, in __init__
    self.string = self.process(self.statement, **compile_kwargs)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 932, in process
    return obj._compiler_dispatch(self, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\visitors.py", line 141, in _compiler_dispatch
    return meth(self, **kw)  # type: ignore  # noqa: E501
           ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 4728, in visit_select
    compile_state = select_stmt._compile_state_factory(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\base.py", line 687, in create_for_statement
    return klass.create_for_statement(statement, compiler, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 447, in create_for_statement
    return cls._create_orm_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1246, in _create_orm_context
    self._setup_for_generate()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1281, in _setup_for_generate
    self._join(query._setup_joins, self._entities)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1922, in _join
    self._join_left_to_right(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1957, in _join_left_to_right
    ) = self._join_determine_implicit_left_side(
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 2121, in _join_determine_implicit_left_side
    raise sa_exc.InvalidRequestError(
sqlalchemy.exc.InvalidRequestError: Don't know how to join to <Mapper at 0x208e57c0fd0; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
2025-05-26 20:23:26,897 - app.api.v1.routers.notifications - INFO - Attempting to connect WebSocket for user 1 (testuser).
2025-05-26 20:23:26,897 - app.utils.websocket_manager - INFO - User 1 connected via WebSocket
2025-05-26 20:23:26,898 - app.api.v1.routers.notifications - INFO - WebSocket connection established for user 1.
2025-05-26 20:23:28,520 - app.utils.websocket_manager - INFO - User 1 disconnected from WebSocket
2025-05-26 20:23:28,522 - app.api.v1.routers.notifications - INFO - WebSocket disconnected for user 1.
2025-05-26 20:23:49,988 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 20:23:49,993 - app.api.v1.routers.users - INFO - User 3 (teacher_zhang) accessed /users/me.
2025-05-26 20:25:35,593 - app.main - WARNING - Validation Error: [{'loc': ('query', 'course_id'), 'msg': 'Field required', 'type': 'missing'}, {'loc': ('query', 'original_filename'), 'msg': 'Field required', 'type': 'missing'}, {'loc': ('query', 'file_type'), 'msg': 'Field required', 'type': 'missing'}]
2025-05-26 20:25:46,924 - app.main - WARNING - Validation Error: [{'loc': ('query', 'course_id'), 'msg': 'Field required', 'type': 'missing'}, {'loc': ('query', 'original_filename'), 'msg': 'Field required', 'type': 'missing'}, {'loc': ('query', 'file_type'), 'msg': 'Field required', 'type': 'missing'}]
2025-05-26 20:32:00,793 - app.main - INFO - Database tables created successfully.
2025-05-26 20:32:00,793 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:32:00,795 - app.main - INFO - Minio bucket check completed.
2025-05-26 20:32:00,795 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:32:00,795 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:32:00,795 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:32:00,795 - app.main - INFO - Database tables created successfully.
2025-05-26 20:32:00,795 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:32:00,795 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:32:00,795 - app.main - INFO - Minio bucket check completed.
y.
2025-05-26 20:32:00,797 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:32:00,797 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:32:00,797 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:32:00,797 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:32:00,797 - app.main - INFO - Application started successfully.
2025-05-26 20:35:55,007 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-26 20:35:55,329 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 20:35:57,378 - app.api.v1.routers.courses - INFO - Course 'DeepSeek AI���Կγ�' (ID: 2) created by teacher 3.
2025-05-26 20:35:59,413 - app.main - WARNING - Validation Error: [{'loc': ('query', 'course_id'), 'msg': 'Field required', 'type': 'missing'}, {'loc': ('query', 'original_filename'), 'msg': 'Field required', 'type': 'missing'}, {'loc': ('query', 'file_type'), 'msg': 'Field required', 'type': 'missing'}]
2025-05-26 20:37:01,587 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 20:37:03,621 - app.api.v1.routers.courses - INFO - Course 'DeepSeek AI���Կγ�' (ID: 3) created by teacher 3.
2025-05-26 20:37:05,656 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/3/text/7b1a2866-21a5-47c0-b82c-3b7df9352fb6.txt
2025-05-26 20:37:05,657 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 3, object courses/3/text/7b1a2866-21a5-47c0-b82c-3b7df9352fb6.txt.
2025-05-26 20:37:07,706 - app.utils.minio_client - INFO - Mock Minio: File courses/3/text/7b1a2866-21a5-47c0-b82c-3b7df9352fb6.txt exists check
2025-05-26 20:37:07,712 - app.api.v1.routers.materials - INFO - Material 'Python��̳̽�' (ID: 1) created successfully for course 3.
2025-05-26 20:37:07,714 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/3/text/7b1a2866-21a5-47c0-b82c-3b7df9352fb6.txt
2025-05-26 20:37:12,745 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/3/text/7b1a2866-21a5-47c0-b82c-3b7df9352fb6.txt
2025-05-26 20:37:19,711 - app.api.v1.routers.materials - ERROR - Error vectorizing material 1: insert_vectors_into_milvus() takes 1 positional argument but 3 were given
2025-05-26 20:37:19,712 - app.api.v1.routers.materials - INFO - Material 1 processed successfully with DeepSeek analysis.
2025-05-26 20:37:24,881 - app.api.v1.routers.materials - INFO - Material 1 analyzed with DeepSeek by user 3.
2025-05-26 20:40:19,480 - app.api.v1.routers.users - INFO - User 3 (teacher_zhang) accessed /users/me.
2025-05-26 20:40:19,490 - app.api.v1.routers.users - INFO - User 3 (teacher_zhang) accessed /users/me.
2025-05-26 20:40:33,146 - app.main - ERROR - Unhandled Internal Server Error: (sqlite3.IntegrityError) NOT NULL constraint failed: materials.course_id
[SQL: UPDATE materials SET course_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: (None, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlite3.IntegrityError: NOT NULL constraint failed: materials.course_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\api\v1\routers\courses.py", line 129, in delete_course_by_id
    deleted_course = delete_course(db, course_id)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 171, in delete_course
    db.commit()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2032, in commit
    trans.commit(_to_root=True)
  File "<string>", line 2, in commit
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1313, in commit
    self._prepare_impl()
  File "<string>", line 2, in _prepare_impl
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1288, in _prepare_impl
    self.session.flush()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4345, in flush
    self._flush(objects)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4480, in _flush
    with util.safe_reraise():
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4441, in _flush
    flush_context.execute()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\unitofwork.py", line 466, in execute
    rec.execute(self)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\unitofwork.py", line 642, in execute
    util.preloaded.orm_persistence.save_obj(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\persistence.py", line 85, in save_obj
    _emit_update_statements(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\persistence.py", line 912, in _emit_update_statements
    c = connection.execute(
        ^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.IntegrityError: (sqlite3.IntegrityError) NOT NULL constraint failed: materials.course_id
[SQL: UPDATE materials SET course_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: (None, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-26 20:40:38,200 - app.main - WARNING - Validation Error: [{'loc': ('query', 'course_id'), 'msg': 'Field required', 'type': 'missing'}, {'loc': ('query', 'original_filename'), 'msg': 'Field required', 'type': 'missing'}, {'loc': ('query', 'file_type'), 'msg': 'Field required', 'type': 'missing'}]
2025-05-26 20:41:34,028 - app.main - WARNING - Validation Error: [{'loc': ('query', 'course_id'), 'msg': 'Field required', 'type': 'missing'}, {'loc': ('query', 'original_filename'), 'msg': 'Field required', 'type': 'missing'}, {'loc': ('query', 'file_type'), 'msg': 'Field required', 'type': 'missing'}]
2025-05-26 20:44:48,406 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 20:44:50,817 - app.auth.router - INFO - User 'testuser' logged in successfully.
2025-05-26 20:44:52,886 - app.api.v1.routers.courses - INFO - Course '�ļ��ϴ����Կγ�' (ID: 4) created by teacher 3.
2025-05-26 20:44:54,931 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/4/text/a860a791-bf03-4df0-b975-c70b7948e820.txt
2025-05-26 20:44:54,932 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 4, object courses/4/text/a860a791-bf03-4df0-b975-c70b7948e820.txt.
2025-05-26 20:44:57,005 - app.utils.minio_client - INFO - Mock Minio: File courses/4/text/a860a791-bf03-4df0-b975-c70b7948e820.txt exists check
2025-05-26 20:44:57,019 - app.api.v1.routers.materials - INFO - Material '�����ĵ�' (ID: 2) created successfully for course 4.
2025-05-26 20:44:57,020 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/4/text/a860a791-bf03-4df0-b975-c70b7948e820.txt
2025-05-26 20:45:01,078 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/4/text/a860a791-bf03-4df0-b975-c70b7948e820.txt
2025-05-26 20:45:08,628 - app.api.v1.routers.materials - ERROR - Error vectorizing material 2: insert_vectors_into_milvus() takes 1 positional argument but 3 were given
2025-05-26 20:45:08,630 - app.api.v1.routers.materials - INFO - Material 2 processed successfully with DeepSeek analysis.
2025-05-26 20:45:12,419 - app.api.v1.routers.materials - INFO - Material 2 analyzed with DeepSeek by user 3.
2025-05-26 20:45:14,507 - app.api.v1.routers.courses - INFO - Course 'Python�������' (ID: 5) created by teacher 3.
2025-05-26 20:45:16,560 - app.api.v1.routers.courses - INFO - Course 'Web����ʵս' (ID: 6) created by teacher 3.
2025-05-26 20:45:18,606 - app.api.v1.routers.courses - INFO - Course '���ݿ�ѧ����' (ID: 7) created by teacher 3.
2025-05-26 20:45:20,658 - app.api.v1.routers.classes - INFO - Class 'Python������' (ID: 2) created by teacher 3.
2025-05-26 20:45:22,712 - app.api.v1.routers.classes - INFO - Class 'Web�������װ�' (ID: 3) created by teacher 3.
2025-05-26 20:47:42,116 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 20:47:44,354 - app.auth.router - INFO - User 'testuser' logged in successfully.
2025-05-26 20:47:50,535 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/1/text/1cd7ef94-fa7a-46e0-b10d-a542bbdbace9.txt
2025-05-26 20:47:50,535 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 1, object courses/1/text/1cd7ef94-fa7a-46e0-b10d-a542bbdbace9.txt.
2025-05-26 20:52:56,421 - app.main - INFO - Database tables created successfully.
2025-05-26 20:52:56,421 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:52:56,421 - app.main - INFO - Minio bucket check completed.
2025-05-26 20:52:56,422 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:52:56,422 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:52:56,422 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:52:56,422 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:52:56,423 - app.main - INFO - Application started successfully.
2025-05-26 20:53:47,389 - app.api.v1.routers.users - INFO - User 3 (teacher_zhang) accessed /users/me.
2025-05-26 20:53:47,393 - app.api.v1.routers.users - INFO - User 3 (teacher_zhang) accessed /users/me.
2025-05-26 20:53:56,091 - app.auth.security - WARNING - JWT decoding failed: Signature has expired.
2025-05-26 20:53:56,092 - app.auth.dependencies - WARNING - JWT decode failed or payload is empty for HTTP request.
2025-05-26 20:53:59,144 - app.auth.router - WARNING - Login failed: Incorrect credentials for username '<EMAIL>'.
2025-05-26 20:53:59,146 - app.main - ERROR - Custom HTTP Exception occurred: Incorrect username or password, Code: INVALID_CREDENTIALS, Status: 401, Data: None
2025-05-26 20:54:42,165 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 20:54:42,171 - app.api.v1.routers.users - INFO - User 3 (teacher_zhang) accessed /users/me.
2025-05-26 20:54:58,642 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/6/pdf/23743fee-32ff-4b11-bb5b-98dd25d58be9.pdf
2025-05-26 20:54:58,643 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 6, object courses/6/pdf/23743fee-32ff-4b11-bb5b-98dd25d58be9.pdf.
2025-05-26 20:56:47,935 - app.main - INFO - Database tables created successfully.
2025-05-26 20:56:47,935 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:56:47,936 - app.main - INFO - Minio bucket check completed.
2025-05-26 20:56:47,937 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:56:47,937 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:56:47,937 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:56:47,938 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:56:47,938 - app.main - INFO - Application started successfully.
2025-05-26 20:58:16,924 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 20:58:20,976 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/1/text/14ceef3e-26b0-4baf-ae68-19be6ccf469c.txt
2025-05-26 20:58:20,976 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 1, object courses/1/text/14ceef3e-26b0-4baf-ae68-19be6ccf469c.txt.
2025-05-26 20:58:23,005 - app.utils.minio_client - INFO - Mock Minio: File courses/1/text/14ceef3e-26b0-4baf-ae68-19be6ccf469c.txt exists check
2025-05-26 20:58:23,011 - app.api.v1.routers.materials - INFO - Material '�����ĵ�' (ID: 3) created successfully for course 1.
2025-05-26 20:58:23,013 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/1/text/14ceef3e-26b0-4baf-ae68-19be6ccf469c.txt
2025-05-26 20:58:25,259 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 20:58:27,288 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/1/text/cc1954d5-74da-4429-ae54-39a9b57e3d2d.txt
2025-05-26 20:58:27,288 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 1, object courses/1/text/cc1954d5-74da-4429-ae54-39a9b57e3d2d.txt.
2025-05-26 20:58:29,315 - app.main - WARNING - Validation Error: [{'loc': ('query', 'course_id'), 'msg': 'Field required', 'type': 'missing'}, {'loc': ('query', 'original_filename'), 'msg': 'Field required', 'type': 'missing'}, {'loc': ('query', 'file_type'), 'msg': 'Field required', 'type': 'missing'}]
2025-05-26 20:58:33,486 - app.api.v1.routers.materials - ERROR - Error vectorizing material 3: insert_vectors_into_milvus() takes 1 positional argument but 3 were given
2025-05-26 20:58:33,486 - app.api.v1.routers.materials - INFO - Material 3 processed successfully with DeepSeek analysis.
2025-05-26 21:02:32,348 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 21:02:36,404 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/1/text/c6bf37a2-ccb2-458c-9c26-1174c5e182ac.txt
2025-05-26 21:02:36,405 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 1, object courses/1/text/c6bf37a2-ccb2-458c-9c26-1174c5e182ac.txt.
2025-05-26 21:02:38,445 - app.utils.minio_client - INFO - Mock Minio: File courses/1/text/c6bf37a2-ccb2-458c-9c26-1174c5e182ac.txt exists check
2025-05-26 21:02:38,449 - app.api.v1.routers.materials - INFO - Material '�����ĵ�' (ID: 4) created successfully for course 1.
2025-05-26 21:02:38,451 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/1/text/c6bf37a2-ccb2-458c-9c26-1174c5e182ac.txt
2025-05-26 21:02:42,477 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/1/text/c6bf37a2-ccb2-458c-9c26-1174c5e182ac.txt
2025-05-26 21:02:49,439 - app.api.v1.routers.materials - ERROR - Error vectorizing material 4: insert_vectors_into_milvus() takes 1 positional argument but 3 were given
2025-05-26 21:02:49,440 - app.api.v1.routers.materials - INFO - Material 4 processed successfully with DeepSeek analysis.
2025-05-26 21:02:56,025 - app.api.v1.routers.materials - INFO - Material 4 analyzed with DeepSeek by user 3.
2025-05-26 21:03:02,364 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 21:03:08,437 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/1/text/47e42505-bbe9-45b7-82b0-f9962501752d.txt
2025-05-26 21:03:08,437 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 1, object courses/1/text/47e42505-bbe9-45b7-82b0-f9962501752d.txt.
2025-05-26 21:08:14,817 - app.main - INFO - Database tables created successfully.
2025-05-26 21:08:14,818 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 21:08:14,818 - app.main - INFO - Minio bucket check completed.
2025-05-26 21:08:14,818 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 21:08:14,818 - app.main - INFO - AI model loaded successfully.
2025-05-26 21:08:14,818 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 21:08:14,820 - app.main - INFO - Milvus initialized successfully.
2025-05-26 21:08:14,820 - app.main - INFO - Application started successfully.
2025-05-26 21:08:14,825 - app.main - INFO - Database tables created successfully.
2025-05-26 21:08:14,826 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 21:08:14,826 - app.main - INFO - Minio bucket check completed.
2025-05-26 21:08:14,826 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 21:08:14,826 - app.main - INFO - AI model loaded successfully.
2025-05-26 21:08:14,827 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 21:08:14,827 - app.main - INFO - Milvus initialized successfully.
2025-05-26 21:08:14,827 - app.main - INFO - Application started successfully.
2025-05-26 21:09:13,945 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 21:09:16,207 - app.auth.router - INFO - User 'testuser' logged in successfully.
2025-05-26 21:09:18,245 - app.auth.router - WARNING - Login failed: Incorrect credentials for username 'admin'.
2025-05-26 21:09:18,247 - app.main - ERROR - Custom HTTP Exception occurred: Incorrect username or password, Code: INVALID_CREDENTIALS, Status: 401, Data: None
2025-05-26 21:09:20,513 - app.auth.router - INFO - User 'teacher_zhang' (teacher) registered successfully with ID: 3.
2025-05-26 21:09:22,762 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 21:09:25,023 - app.auth.router - INFO - User 'testuser' logged in successfully.
2025-05-26 21:09:27,086 - app.auth.router - WARNING - Login failed: Incorrect credentials for username 'admin'.
2025-05-26 21:09:27,087 - app.main - ERROR - Custom HTTP Exception occurred: Incorrect username or password, Code: INVALID_CREDENTIALS, Status: 401, Data: None
2025-05-26 21:10:22,901 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 21:10:27,167 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 21:10:31,213 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/1/text/e25d17dd-4ac5-4bda-ae58-ef135894202c.txt
2025-05-26 21:10:31,213 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 1, object courses/1/text/e25d17dd-4ac5-4bda-ae58-ef135894202c.txt.
2025-05-26 21:10:33,273 - app.utils.minio_client - INFO - Mock Minio: File courses/1/text/e25d17dd-4ac5-4bda-ae58-ef135894202c.txt exists check
2025-05-26 21:10:33,280 - app.api.v1.routers.materials - INFO - Material '�����ĵ�' (ID: 5) created successfully for course 1.
2025-05-26 21:10:33,282 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/1/text/e25d17dd-4ac5-4bda-ae58-ef135894202c.txt
2025-05-26 21:10:46,423 - app.api.v1.routers.materials - ERROR - Error vectorizing material 5: insert_vectors_into_milvus() takes 1 positional argument but 3 were given
2025-05-26 21:10:46,424 - app.api.v1.routers.materials - INFO - Material 5 processed successfully with DeepSeek analysis.
2025-05-26 21:44:14,895 - app.main - INFO - Database tables created successfully.
2025-05-26 21:44:14,896 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 21:44:14,896 - app.main - INFO - Minio bucket check completed.
2025-05-26 21:44:14,896 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 21:44:14,896 - app.main - INFO - AI model loaded successfully.
2025-05-26 21:44:14,896 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 21:44:14,896 - app.main - INFO - Milvus initialized successfully.
2025-05-26 21:44:14,896 - app.main - INFO - Application started successfully.
2025-05-26 21:44:15,104 - app.main - INFO - Database tables created successfully.
2025-05-26 21:44:15,104 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 21:44:15,105 - app.main - INFO - Minio bucket check completed.
2025-05-26 21:44:15,105 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 21:44:15,105 - app.main - INFO - AI model loaded successfully.
2025-05-26 21:44:15,106 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 21:44:15,106 - app.main - INFO - Milvus initialized successfully.
2025-05-26 21:44:15,106 - app.main - INFO - Application started successfully.
2025-05-26 21:44:34,910 - app.main - INFO - Database tables created successfully.
2025-05-26 21:44:34,910 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 21:44:34,911 - app.main - INFO - Minio bucket check completed.
2025-05-26 21:44:34,911 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 21:44:34,911 - app.main - INFO - AI model loaded successfully.
2025-05-26 21:44:34,911 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 21:44:34,912 - app.main - INFO - Milvus initialized successfully.
2025-05-26 21:44:34,912 - app.main - INFO - Application started successfully.
2025-05-26 21:45:45,513 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 21:45:51,620 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/1/text/184002f4-18c8-444e-ae9a-c6b8ffc9a39f.txt
2025-05-26 21:45:51,621 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 1, object courses/1/text/184002f4-18c8-444e-ae9a-c6b8ffc9a39f.txt.
2025-05-26 21:47:07,960 - app.auth.router - WARNING - Login failed: Incorrect credentials for username 'test'.
2025-05-26 21:47:07,961 - app.main - ERROR - Custom HTTP Exception occurred: Incorrect username or password, Code: INVALID_CREDENTIALS, Status: 401, Data: None
2025-05-26 21:47:42,757 - app.main - INFO - Database tables created successfully.
2025-05-26 21:47:42,758 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 21:47:42,758 - app.main - INFO - Minio bucket check completed.
2025-05-26 21:47:42,758 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 21:47:42,759 - app.main - INFO - AI model loaded successfully.
2025-05-26 21:47:42,759 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 21:47:42,759 - app.main - INFO - Milvus initialized successfully.
2025-05-26 21:47:42,759 - app.main - INFO - Application started successfully.
2025-05-26 21:48:15,379 - app.auth.router - WARNING - Login failed: Incorrect credentials for username 'test'.
2025-05-26 21:48:15,380 - app.main - ERROR - Custom HTTP Exception occurred: Incorrect username or password, Code: INVALID_CREDENTIALS, Status: 401, Data: None
2025-05-26 21:48:49,958 - app.main - INFO - Database tables created successfully.
2025-05-26 21:48:49,959 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 21:48:49,959 - app.main - INFO - Minio bucket check completed.
2025-05-26 21:48:49,960 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 21:48:49,960 - app.main - INFO - AI model loaded successfully.
2025-05-26 21:48:49,960 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 21:48:49,961 - app.main - INFO - Milvus initialized successfully.
2025-05-26 21:48:49,961 - app.main - INFO - Application started successfully.
2025-05-26 21:50:28,301 - app.main - INFO - Database tables created successfully.
2025-05-26 21:50:28,301 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 21:50:28,302 - app.main - INFO - Minio bucket check completed.
2025-05-26 21:50:28,302 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 21:50:28,303 - app.main - INFO - AI model loaded successfully.
2025-05-26 21:50:28,303 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 21:50:28,304 - app.main - INFO - Milvus initialized successfully.
2025-05-26 21:50:28,304 - app.main - INFO - Application started successfully.
2025-05-26 21:50:56,253 - app.auth.router - WARNING - Login failed: Incorrect credentials for username 'test'.
2025-05-26 21:50:56,254 - app.main - ERROR - Custom HTTP Exception occurred: Incorrect username or password, Code: INVALID_CREDENTIALS, Status: 401, Data: None
2025-05-26 21:51:49,611 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 21:52:31,304 - app.main - INFO - Database tables created successfully.
2025-05-26 21:52:31,304 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 21:52:31,305 - app.main - INFO - Minio bucket check completed.
2025-05-26 21:52:31,305 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 21:52:31,305 - app.main - INFO - AI model loaded successfully.
2025-05-26 21:52:31,306 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 21:52:31,306 - app.main - INFO - Milvus initialized successfully.
2025-05-26 21:52:31,306 - app.main - INFO - Application started successfully.
2025-05-26 21:52:45,279 - app.main - INFO - Database tables created successfully.
2025-05-26 21:52:45,279 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 21:52:45,279 - app.main - INFO - Minio bucket check completed.
2025-05-26 21:52:45,281 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 21:52:45,281 - app.main - INFO - AI model loaded successfully.
2025-05-26 21:52:45,281 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 21:52:45,281 - app.main - INFO - Milvus initialized successfully.
2025-05-26 21:52:45,281 - app.main - INFO - Application started successfully.
2025-05-26 21:53:03,626 - app.main - INFO - Database tables created successfully.
2025-05-26 21:53:03,627 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 21:53:03,627 - app.main - INFO - Minio bucket check completed.
2025-05-26 21:53:03,627 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 21:53:03,628 - app.main - INFO - AI model loaded successfully.
2025-05-26 21:53:03,628 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 21:53:03,629 - app.main - INFO - Milvus initialized successfully.
2025-05-26 21:53:03,629 - app.main - INFO - Application started successfully.
2025-05-26 21:53:25,783 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 21:55:10,448 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 21:56:06,175 - app.main - INFO - Database tables created successfully.
2025-05-26 21:56:06,175 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 21:56:06,175 - app.main - INFO - Minio bucket check completed.
2025-05-26 21:56:06,175 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 21:56:06,177 - app.main - INFO - AI model loaded successfully.
2025-05-26 21:56:06,177 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 21:56:06,177 - app.main - INFO - Milvus initialized successfully.
2025-05-26 21:56:06,177 - app.main - INFO - Application started successfully.
2025-05-26 21:56:19,838 - app.main - INFO - Database tables created successfully.
2025-05-26 21:56:19,839 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 21:56:19,839 - app.main - INFO - Minio bucket check completed.
2025-05-26 21:56:19,839 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 21:56:19,839 - app.main - INFO - AI model loaded successfully.
2025-05-26 21:56:19,839 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 21:56:19,840 - app.main - INFO - Milvus initialized successfully.
2025-05-26 21:56:19,840 - app.main - INFO - Application started successfully.
2025-05-26 21:56:33,655 - app.main - INFO - Database tables created successfully.
2025-05-26 21:56:33,656 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 21:56:33,656 - app.main - INFO - Minio bucket check completed.
2025-05-26 21:56:33,656 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 21:56:33,656 - app.main - INFO - AI model loaded successfully.
2025-05-26 21:56:33,657 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 21:56:33,657 - app.main - INFO - Milvus initialized successfully.
2025-05-26 21:56:33,657 - app.main - INFO - Application started successfully.
2025-05-26 21:56:57,575 - app.main - INFO - Database tables created successfully.
2025-05-26 21:56:57,576 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 21:56:57,577 - app.main - INFO - Minio bucket check completed.
2025-05-26 21:56:57,577 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 21:56:57,578 - app.main - INFO - AI model loaded successfully.
2025-05-26 21:56:57,578 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 21:56:57,578 - app.main - INFO - Milvus initialized successfully.
2025-05-26 21:56:57,578 - app.main - INFO - Application started successfully.
2025-05-26 21:57:21,720 - app.main - INFO - Database tables created successfully.
2025-05-26 21:57:21,721 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 21:57:21,721 - app.main - INFO - Minio bucket check completed.
2025-05-26 21:57:21,721 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 21:57:21,721 - app.main - INFO - AI model loaded successfully.
2025-05-26 21:57:21,722 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 21:57:21,722 - app.main - INFO - Milvus initialized successfully.
2025-05-26 21:57:21,722 - app.main - INFO - Application started successfully.
2025-05-26 21:57:45,325 - app.main - INFO - Database tables created successfully.
2025-05-26 21:57:45,325 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 21:57:45,326 - app.main - INFO - Minio bucket check completed.
2025-05-26 21:57:45,326 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 21:57:45,326 - app.main - INFO - AI model loaded successfully.
2025-05-26 21:57:45,326 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 21:57:45,326 - app.main - INFO - Milvus initialized successfully.
2025-05-26 21:57:45,326 - app.main - INFO - Application started successfully.
2025-05-26 21:58:05,979 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 23:22:03,182 - app.main - INFO - Database tables created successfully.
2025-05-26 23:22:03,184 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 23:22:03,184 - app.main - INFO - Minio bucket check completed.
2025-05-26 23:22:03,184 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 23:22:03,184 - app.main - INFO - AI model loaded successfully.
2025-05-26 23:22:03,184 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 23:22:03,184 - app.main - INFO - Milvus initialized successfully.
2025-05-26 23:22:03,185 - app.main - INFO - Application started successfully.
2025-05-26 23:25:45,252 - app.main - INFO - Database tables created successfully.
2025-05-26 23:25:45,253 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 23:25:45,253 - app.main - INFO - Minio bucket check completed.
2025-05-26 23:25:45,253 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 23:25:45,253 - app.main - INFO - AI model loaded successfully.
2025-05-26 23:25:45,254 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 23:25:45,254 - app.main - INFO - Milvus initialized successfully.
2025-05-26 23:25:45,254 - app.main - INFO - Application started successfully.
2025-05-26 23:27:58,628 - app.auth.router - WARNING - Registration failed: Username 'teacher_zhang' already registered.
2025-05-26 23:27:58,631 - app.main - ERROR - Custom HTTP Exception occurred: Username already registered, Code: USERNAME_EXISTS, Status: 400, Data: None
2025-05-26 23:28:00,704 - app.auth.router - WARNING - Registration failed: Username 'testuser' already registered.
2025-05-26 23:28:00,706 - app.main - ERROR - Custom HTTP Exception occurred: Username already registered, Code: USERNAME_EXISTS, Status: 400, Data: None
2025-05-26 23:28:04,775 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-26 23:28:05,178 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 23:28:07,229 - app.api.v1.routers.users - INFO - User 3 (teacher_zhang) accessed /users/me.
2025-05-26 23:28:09,656 - app.auth.router - INFO - User 'testuser' logged in successfully.
2025-05-26 23:28:15,851 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/1/text/c53f6395-8ead-4c03-904d-c352fc7b1308.txt
2025-05-26 23:28:15,853 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 1, object courses/1/text/c53f6395-8ead-4c03-904d-c352fc7b1308.txt.
2025-05-26 23:28:17,920 - app.utils.minio_client - INFO - Mock Minio: File courses/1/text/c53f6395-8ead-4c03-904d-c352fc7b1308.txt exists check
2025-05-26 23:28:17,942 - app.api.v1.routers.materials - INFO - Material '�����ĵ�' (ID: 6) created successfully for course 1.
2025-05-26 23:28:17,944 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/1/text/c53f6395-8ead-4c03-904d-c352fc7b1308.txt
2025-05-26 23:28:31,064 - app.api.v1.routers.materials - ERROR - Error vectorizing material 6: insert_vectors_into_milvus() takes 1 positional argument but 3 were given
2025-05-26 23:28:31,065 - app.api.v1.routers.materials - INFO - Material 6 processed successfully with DeepSeek analysis.
2025-05-26 23:29:49,995 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 23:29:54,091 - app.auth.router - WARNING - Registration failed: Username 'teacher_zhang' already registered.
2025-05-26 23:37:23,750 - app.main - INFO - Database tables created successfully.
2025-05-26 23:37:23,751 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 23:37:23,751 - app.main - INFO - Minio bucket check completed.
2025-05-26 23:37:23,751 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 23:37:23,752 - app.main - INFO - AI model loaded successfully.
2025-05-26 23:37:23,752 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 23:37:23,752 - app.main - INFO - Milvus initialized successfully.
2025-05-26 23:37:23,752 - app.main - INFO - Application started successfully.
2025-05-26 23:40:49,232 - app.main - INFO - Database tables created successfully.
2025-05-26 23:40:49,233 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 23:40:49,233 - app.main - INFO - Minio bucket check completed.
2025-05-26 23:40:49,234 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 23:40:49,234 - app.main - INFO - AI model loaded successfully.
2025-05-26 23:40:49,234 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 23:40:49,235 - app.main - INFO - Milvus initialized successfully.
2025-05-26 23:40:49,235 - app.main - INFO - Application started successfully.
2025-05-26 23:29:54,093 - app.main - ERROR - Custom HTTP Exception occurred: Username already registered, Code: USERNAME_EXISTS, Status: 400, Data: None
2025-05-27 00:11:37,902 - app.auth.router - WARNING - Login failed: Incorrect credentials for username 'invalid'.
2025-05-27 00:11:37,903 - app.auth.router - WARNING - Registration failed: Username 'teacher_zhang' already registered.
2025-05-27 00:11:37,905 - app.main - ERROR - Custom HTTP Exception occurred: Incorrect username or password, Code: INVALID_CREDENTIALS, Status: 401, Data: None
2025-05-27 00:11:37,906 - app.main - ERROR - Custom HTTP Exception occurred: Username already registered, Code: USERNAME_EXISTS, Status: 400, Data: None
2025-05-27 00:11:38,411 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-27 00:11:38,437 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-27 00:11:38,437 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-27 00:11:38,455 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-27 00:11:38,479 - app.auth.router - INFO - User 'testuser' logged in successfully.
2025-05-27 00:11:38,868 - app.auth.security - WARNING - JWT decoding failed: Signature has expired.
2025-05-27 00:11:38,868 - app.auth.dependencies - WARNING - JWT decode failed or payload is empty for HTTP request.
2025-05-27 00:11:38,873 - app.auth.security - WARNING - JWT decoding failed: Signature has expired.
2025-05-27 00:11:38,873 - app.auth.dependencies - WARNING - JWT decode failed or payload is empty for HTTP request.
2025-05-27 00:12:13,714 - app.main - INFO - Database tables created successfully.
2025-05-27 00:12:13,714 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-27 00:12:13,715 - app.main - INFO - Minio bucket check completed.
2025-05-27 00:12:13,715 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-27 00:12:13,715 - app.main - INFO - AI model loaded successfully.
2025-05-27 00:12:13,716 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-27 00:12:13,716 - app.main - INFO - Milvus initialized successfully.
2025-05-27 00:12:13,716 - app.main - INFO - Application started successfully.
2025-05-27 00:12:37,641 - app.auth.router - WARNING - Login failed: Incorrect credentials for username 'tearch_zhang'.
2025-05-27 00:12:37,643 - app.main - ERROR - Custom HTTP Exception occurred: Incorrect username or password, Code: INVALID_CREDENTIALS, Status: 401, Data: None
2025-05-27 00:13:07,456 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-27 00:13:07,466 - app.api.v1.routers.users - INFO - User 3 (teacher_zhang) accessed /users/me.
2025-05-27 00:13:18,117 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/2/pdf/fd516fa8-7f37-42fc-b0fd-697d09b9a97c.pdf
2025-05-27 00:13:18,118 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 2, object courses/2/pdf/fd516fa8-7f37-42fc-b0fd-697d09b9a97c.pdf.
2025-05-27 00:14:03,395 - app.api.v1.routers.users - INFO - User 3 (teacher_zhang) accessed /users/me.
2025-05-27 00:14:03,410 - app.api.v1.routers.users - INFO - User 3 (teacher_zhang) accessed /users/me.
2025-05-27 00:14:11,067 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/4/pdf/7dd8901f-89bc-4a35-88db-b03fa6d37050.pdf
2025-05-27 00:14:11,067 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 4, object courses/4/pdf/7dd8901f-89bc-4a35-88db-b03fa6d37050.pdf.
2025-05-27 00:15:08,262 - app.api.v1.routers.users - INFO - User 3 (teacher_zhang) accessed /users/me.
2025-05-27 00:15:08,267 - app.api.v1.routers.users - INFO - User 3 (teacher_zhang) accessed /users/me.
2025-05-27 00:15:17,197 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/4/pdf/f169ce64-4310-48cf-8082-34bcf602ef58.pdf
2025-05-27 00:15:17,198 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 4, object courses/4/pdf/f169ce64-4310-48cf-8082-34bcf602ef58.pdf.
2025-05-27 00:15:29,018 - app.api.v1.routers.courses - INFO - Course 7 deleted by teacher 3.
2025-05-27 00:15:47,281 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/1/text/14ceef3e-26b0-4baf-ae68-19be6ccf469c.txt
2025-05-27 00:15:57,527 - app.api.v1.routers.materials - INFO - Material 3 analyzed with DeepSeek by user 3.
2025-05-27 00:16:15,669 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/4/pdf/fb4b68c8-6a1c-4c35-9b1f-e1d367e6c752.pdf
2025-05-27 00:16:15,670 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 4, object courses/4/pdf/fb4b68c8-6a1c-4c35-9b1f-e1d367e6c752.pdf.
2025-05-27 00:17:22,605 - app.main - INFO - Database tables created successfully.
2025-05-27 00:17:22,606 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-27 00:17:22,606 - app.main - INFO - Minio bucket check completed.
2025-05-27 00:17:22,606 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-27 00:17:22,606 - app.main - INFO - AI model loaded successfully.
2025-05-27 00:17:22,606 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-27 00:17:22,607 - app.main - INFO - Milvus initialized successfully.
2025-05-27 00:17:22,607 - app.main - INFO - Application started successfully.
2025-05-27 00:20:46,210 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-27 00:20:50,255 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/1/text/9e5370fd-6ce2-437f-8ebd-a28d42031318.txt
2025-05-27 00:20:50,258 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 1, object courses/1/text/9e5370fd-6ce2-437f-8ebd-a28d42031318.txt.
2025-05-27 00:20:56,762 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-27 00:21:00,861 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/1/text/84c4c882-e3b8-4138-a084-a83350c400a7.txt
2025-05-27 00:21:00,862 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 1, object courses/1/text/84c4c882-e3b8-4138-a084-a83350c400a7.txt.
2025-05-27 00:21:47,793 - app.main - INFO - Database tables created successfully.
2025-05-27 00:21:47,793 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-27 00:21:47,794 - app.main - INFO - Minio bucket check completed.
2025-05-27 00:21:47,794 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-27 00:21:47,795 - app.main - INFO - AI model loaded successfully.
2025-05-27 00:21:47,795 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-27 00:21:47,795 - app.main - INFO - Milvus initialized successfully.
2025-05-27 00:21:47,796 - app.main - INFO - Application started successfully.
2025-05-27 00:21:48,421 - app.main - INFO - Database tables created successfully.
2025-05-27 00:21:48,421 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-27 00:21:48,422 - app.main - INFO - Minio bucket check completed.
2025-05-27 00:21:48,422 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-27 00:21:48,422 - app.main - INFO - AI model loaded successfully.
2025-05-27 00:21:48,422 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-27 00:21:48,423 - app.main - INFO - Milvus initialized successfully.
2025-05-27 00:21:48,423 - app.main - INFO - Application started successfully.
2025-05-27 00:22:11,265 - app.main - INFO - Database tables created successfully.
2025-05-27 00:22:11,265 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-27 00:22:11,266 - app.main - INFO - Minio bucket check completed.
2025-05-27 00:22:11,266 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-27 00:22:11,266 - app.main - INFO - AI model loaded successfully.
2025-05-27 00:22:11,266 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-27 00:22:11,266 - app.main - INFO - Milvus initialized successfully.
2025-05-27 00:22:11,266 - app.main - INFO - Application started successfully.
2025-05-27 00:22:11,788 - app.main - INFO - Database tables created successfully.
2025-05-27 00:22:11,788 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-27 00:22:11,789 - app.main - INFO - Minio bucket check completed.
2025-05-27 00:22:11,790 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-27 00:22:11,791 - app.main - INFO - AI model loaded successfully.
2025-05-27 00:22:11,791 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-27 00:22:11,791 - app.main - INFO - Milvus initialized successfully.
2025-05-27 00:22:11,792 - app.main - INFO - Application started successfully.
2025-05-27 00:22:25,650 - app.main - INFO - Database tables created successfully.
2025-05-27 00:22:25,651 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-27 00:22:25,651 - app.main - INFO - Minio bucket check completed.
2025-05-27 00:22:25,651 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-27 00:22:25,651 - app.main - INFO - AI model loaded successfully.
2025-05-27 00:22:25,652 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-27 00:22:25,652 - app.main - INFO - Milvus initialized successfully.
2025-05-27 00:22:25,653 - app.main - INFO - Application started successfully.
2025-05-27 00:22:26,277 - app.main - INFO - Database tables created successfully.
2025-05-27 00:22:26,279 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-27 00:22:26,279 - app.main - INFO - Minio bucket check completed.
2025-05-27 00:22:26,279 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-27 00:22:26,280 - app.main - INFO - AI model loaded successfully.
2025-05-27 00:22:26,280 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-27 00:22:26,280 - app.main - INFO - Milvus initialized successfully.
2025-05-27 00:22:26,280 - app.main - INFO - Application started successfully.
2025-05-27 00:22:47,337 - app.main - INFO - Database tables created successfully.
2025-05-27 00:22:47,337 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-27 00:22:47,337 - app.main - INFO - Minio bucket check completed.
2025-05-27 00:22:47,338 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-27 00:22:47,338 - app.main - INFO - AI model loaded successfully.
2025-05-27 00:22:47,339 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-27 00:22:47,339 - app.main - INFO - Milvus initialized successfully.
2025-05-27 00:22:47,340 - app.main - INFO - Application started successfully.
2025-05-27 00:22:47,357 - app.main - INFO - Database tables created successfully.
2025-05-27 00:22:47,357 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-27 00:22:47,358 - app.main - INFO - Minio bucket check completed.
2025-05-27 00:22:47,358 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-27 00:22:47,358 - app.main - INFO - AI model loaded successfully.
2025-05-27 00:22:47,358 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-27 00:22:47,359 - app.main - INFO - Milvus initialized successfully.
2025-05-27 00:22:47,359 - app.main - INFO - Application started successfully.
2025-05-27 00:23:13,784 - app.main - INFO - Database tables created successfully.
2025-05-27 00:23:13,785 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-27 00:23:13,785 - app.main - INFO - Minio bucket check completed.
2025-05-27 00:23:13,785 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-27 00:23:13,785 - app.main - INFO - AI model loaded successfully.
2025-05-27 00:23:13,785 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-27 00:23:13,785 - app.main - INFO - Milvus initialized successfully.
2025-05-27 00:23:13,786 - app.main - INFO - Application started successfully.
2025-05-27 00:23:49,030 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-27 00:23:53,124 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/1/text/b4f5cca4-fd34-4d41-a4d5-49037d0ff5f4.txt
2025-05-27 00:23:53,126 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 1, object courses/1/text/b4f5cca4-fd34-4d41-a4d5-49037d0ff5f4.txt.
2025-05-27 00:23:59,623 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-27 00:24:03,697 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/1/text/4f3d600e-1465-446c-a369-fe820a02ff5d.txt
2025-05-27 00:24:03,698 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 1, object courses/1/text/4f3d600e-1465-446c-a369-fe820a02ff5d.txt.
2025-05-27 00:43:08,790 - app.main - INFO - Database tables created successfully.
2025-05-27 00:43:08,790 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-27 00:43:08,790 - app.main - INFO - Minio bucket check completed.
2025-05-27 00:43:08,791 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-27 00:43:08,791 - app.main - INFO - AI model loaded successfully.
2025-05-27 00:43:08,791 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-27 00:43:08,791 - app.main - INFO - Milvus initialized successfully.
2025-05-27 00:43:08,791 - app.main - INFO - Application started successfully.
2025-05-27 00:43:25,495 - app.main - INFO - Database tables created successfully.
2025-05-27 00:43:25,495 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-27 00:43:25,495 - app.main - INFO - Minio bucket check completed.
2025-05-27 00:43:25,496 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-27 00:43:25,496 - app.main - INFO - AI model loaded successfully.
2025-05-27 00:43:25,496 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-27 00:43:25,496 - app.main - INFO - Milvus initialized successfully.
2025-05-27 00:43:25,496 - app.main - INFO - Application started successfully.
2025-05-27 00:44:04,366 - app.auth.security - WARNING - JWT decoding failed: Signature has expired.
2025-05-27 00:44:04,367 - app.auth.dependencies - WARNING - JWT decode failed or payload is empty for HTTP request.
2025-05-27 00:44:04,378 - app.auth.security - WARNING - JWT decoding failed: Signature has expired.
2025-05-27 00:44:04,378 - app.auth.dependencies - WARNING - JWT decode failed or payload is empty for HTTP request.
2025-05-27 00:44:31,921 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-27 00:44:32,349 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-27 00:44:32,377 - app.api.v1.routers.users - INFO - User 3 (teacher_zhang) accessed /users/me.
2025-05-27 00:44:42,180 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/2/pdf/92dc843a-6fbd-4aa4-b66e-904bd59c2809.pdf
2025-05-27 00:44:42,181 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 2, object courses/2/pdf/92dc843a-6fbd-4aa4-b66e-904bd59c2809.pdf.
2025-05-27 00:44:42,218 - app.api.v1.routers.mock_storage - INFO - Mock upload: Receiving file courses/2/pdf/92dc843a-6fbd-4aa4-b66e-904bd59c2809.pdf (1028434 bytes)
2025-05-27 00:44:42,219 - app.utils.minio_client - INFO - Mock Minio: Storing content for courses/2/pdf/92dc843a-6fbd-4aa4-b66e-904bd59c2809.pdf (1028434 bytes)
2025-05-27 00:44:42,220 - app.api.v1.routers.mock_storage - INFO - Mock upload: Successfully stored courses/2/pdf/92dc843a-6fbd-4aa4-b66e-904bd59c2809.pdf
2025-05-27 00:44:42,247 - app.utils.minio_client - INFO - Mock Minio: Checking if file courses/2/pdf/92dc843a-6fbd-4aa4-b66e-904bd59c2809.pdf exists
2025-05-27 00:44:42,256 - app.api.v1.routers.materials - INFO - Material '������̨�ݽ�������' (ID: 7) created successfully for course 2.
2025-05-27 00:44:42,264 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/2/pdf/92dc843a-6fbd-4aa4-b66e-904bd59c2809.pdf
2025-05-27 00:44:59,121 - app.api.v1.routers.materials - ERROR - Error vectorizing material 7: insert_vectors_into_milvus() takes 1 positional argument but 3 were given
2025-05-27 00:44:59,121 - app.api.v1.routers.materials - INFO - Material 7 processed successfully with DeepSeek analysis.
2025-05-27 00:45:12,385 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-27 00:45:14,919 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/2/pdf/92dc843a-6fbd-4aa4-b66e-904bd59c2809.pdf
2025-05-27 00:45:32,059 - app.api.v1.routers.materials - INFO - Material 7 analyzed with DeepSeek by user 3.
2025-05-27 00:45:32,079 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/1/text/d4e8adb3-fd86-4f09-b42d-53e7a81cf99d.txt
2025-05-27 00:45:32,079 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 1, object courses/1/text/d4e8adb3-fd86-4f09-b42d-53e7a81cf99d.txt.
2025-05-27 00:46:19,896 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-27 00:46:21,961 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/1/text/44e5a7d8-9610-4542-b3d2-886b6a861066.txt
2025-05-27 00:46:21,961 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 1, object courses/1/text/44e5a7d8-9610-4542-b3d2-886b6a861066.txt.
2025-05-27 13:44:42,805 - app.main - INFO - Database tables created successfully.
2025-05-27 13:44:42,806 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-27 13:44:42,806 - app.main - INFO - Minio bucket check completed.
2025-05-27 13:44:42,807 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-27 13:44:42,807 - app.main - INFO - AI model loaded successfully.
2025-05-27 13:44:42,807 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-27 13:44:42,808 - app.main - INFO - Milvus initialized successfully.
2025-05-27 13:44:42,808 - app.main - INFO - Application started successfully.
2025-05-27 13:45:03,132 - app.auth.security - WARNING - JWT decoding failed: Signature has expired.
2025-05-27 13:45:03,132 - app.auth.dependencies - WARNING - JWT decode failed or payload is empty for HTTP request.
2025-05-27 13:45:03,141 - app.auth.security - WARNING - JWT decoding failed: Signature has expired.
2025-05-27 13:45:03,142 - app.auth.dependencies - WARNING - JWT decode failed or payload is empty for HTTP request.
2025-05-27 13:46:32,915 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-27 13:46:33,293 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-27 13:46:33,312 - app.api.v1.routers.users - INFO - User 3 (teacher_zhang) accessed /users/me.
2025-05-27 13:47:36,618 - app.main - ERROR - Unhandled Internal Server Error: (sqlite3.IntegrityError) NOT NULL constraint failed: materials.course_id
[SQL: UPDATE materials SET course_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: [(None, 3), (None, 4), (None, 5), (None, 6)]]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1932, in _exec_single_context
    self.dialect.do_executemany(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 940, in do_executemany
    cursor.executemany(statement, parameters)
sqlite3.IntegrityError: NOT NULL constraint failed: materials.course_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 177, in __call__
    with recv_stream, send_stream, collapse_excgroups():
  File "D:\PycharmProjects\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 179, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\main.py", line 282, in log_requests
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 154, in call_next
    raise app_exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\api\v1\routers\courses.py", line 129, in delete_course_by_id
    deleted_course = delete_course(db, course_id)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 171, in delete_course
    db.commit()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2032, in commit
    trans.commit(_to_root=True)
  File "<string>", line 2, in commit
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1313, in commit
    self._prepare_impl()
  File "<string>", line 2, in _prepare_impl
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1288, in _prepare_impl
    self.session.flush()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4345, in flush
    self._flush(objects)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4480, in _flush
    with util.safe_reraise():
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4441, in _flush
    flush_context.execute()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\unitofwork.py", line 466, in execute
    rec.execute(self)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\unitofwork.py", line 642, in execute
    util.preloaded.orm_persistence.save_obj(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\persistence.py", line 85, in save_obj
    _emit_update_statements(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\persistence.py", line 912, in _emit_update_statements
    c = connection.execute(
        ^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1932, in _exec_single_context
    self.dialect.do_executemany(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 940, in do_executemany
    cursor.executemany(statement, parameters)
sqlalchemy.exc.IntegrityError: (sqlite3.IntegrityError) NOT NULL constraint failed: materials.course_id
[SQL: UPDATE materials SET course_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: [(None, 3), (None, 4), (None, 5), (None, 6)]]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-27 13:47:40,309 - app.main - ERROR - Unhandled Internal Server Error: (sqlite3.IntegrityError) NOT NULL constraint failed: materials.course_id
[SQL: UPDATE materials SET course_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: [(None, 3), (None, 4), (None, 5), (None, 6)]]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1932, in _exec_single_context
    self.dialect.do_executemany(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 940, in do_executemany
    cursor.executemany(statement, parameters)
sqlite3.IntegrityError: NOT NULL constraint failed: materials.course_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 177, in __call__
    with recv_stream, send_stream, collapse_excgroups():
  File "D:\PycharmProjects\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 179, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\main.py", line 282, in log_requests
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 154, in call_next
    raise app_exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\api\v1\routers\courses.py", line 129, in delete_course_by_id
    deleted_course = delete_course(db, course_id)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 171, in delete_course
    db.commit()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2032, in commit
    trans.commit(_to_root=True)
  File "<string>", line 2, in commit
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1313, in commit
    self._prepare_impl()
  File "<string>", line 2, in _prepare_impl
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1288, in _prepare_impl
    self.session.flush()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4345, in flush
    self._flush(objects)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4480, in _flush
    with util.safe_reraise():
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4441, in _flush
    flush_context.execute()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\unitofwork.py", line 466, in execute
    rec.execute(self)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\unitofwork.py", line 642, in execute
    util.preloaded.orm_persistence.save_obj(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\persistence.py", line 85, in save_obj
    _emit_update_statements(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\persistence.py", line 912, in _emit_update_statements
    c = connection.execute(
        ^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1932, in _exec_single_context
    self.dialect.do_executemany(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 940, in do_executemany
    cursor.executemany(statement, parameters)
sqlalchemy.exc.IntegrityError: (sqlite3.IntegrityError) NOT NULL constraint failed: materials.course_id
[SQL: UPDATE materials SET course_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: [(None, 3), (None, 4), (None, 5), (None, 6)]]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-27 13:47:44,719 - app.api.v1.routers.courses - INFO - Course 6 deleted by teacher 3.
2025-05-27 13:47:48,458 - app.api.v1.routers.courses - INFO - Course 5 deleted by teacher 3.
2025-05-27 13:47:52,305 - app.main - ERROR - Unhandled Internal Server Error: (sqlite3.IntegrityError) NOT NULL constraint failed: materials.course_id
[SQL: UPDATE materials SET course_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: (None, 2)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlite3.IntegrityError: NOT NULL constraint failed: materials.course_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 177, in __call__
    with recv_stream, send_stream, collapse_excgroups():
  File "D:\PycharmProjects\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 179, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\main.py", line 282, in log_requests
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 154, in call_next
    raise app_exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\api\v1\routers\courses.py", line 129, in delete_course_by_id
    deleted_course = delete_course(db, course_id)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 171, in delete_course
    db.commit()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2032, in commit
    trans.commit(_to_root=True)
  File "<string>", line 2, in commit
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1313, in commit
    self._prepare_impl()
  File "<string>", line 2, in _prepare_impl
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1288, in _prepare_impl
    self.session.flush()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4345, in flush
    self._flush(objects)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4480, in _flush
    with util.safe_reraise():
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4441, in _flush
    flush_context.execute()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\unitofwork.py", line 466, in execute
    rec.execute(self)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\unitofwork.py", line 642, in execute
    util.preloaded.orm_persistence.save_obj(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\persistence.py", line 85, in save_obj
    _emit_update_statements(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\persistence.py", line 912, in _emit_update_statements
    c = connection.execute(
        ^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.IntegrityError: (sqlite3.IntegrityError) NOT NULL constraint failed: materials.course_id
[SQL: UPDATE materials SET course_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: (None, 2)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-27 13:47:56,928 - app.main - ERROR - Unhandled Internal Server Error: (sqlite3.IntegrityError) NOT NULL constraint failed: materials.course_id
[SQL: UPDATE materials SET course_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: (None, 2)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlite3.IntegrityError: NOT NULL constraint failed: materials.course_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 177, in __call__
    with recv_stream, send_stream, collapse_excgroups():
  File "D:\PycharmProjects\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 179, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\main.py", line 282, in log_requests
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 154, in call_next
    raise app_exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\api\v1\routers\courses.py", line 129, in delete_course_by_id
    deleted_course = delete_course(db, course_id)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 171, in delete_course
    db.commit()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2032, in commit
    trans.commit(_to_root=True)
  File "<string>", line 2, in commit
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1313, in commit
    self._prepare_impl()
  File "<string>", line 2, in _prepare_impl
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1288, in _prepare_impl
    self.session.flush()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4345, in flush
    self._flush(objects)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4480, in _flush
    with util.safe_reraise():
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4441, in _flush
    flush_context.execute()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\unitofwork.py", line 466, in execute
    rec.execute(self)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\unitofwork.py", line 642, in execute
    util.preloaded.orm_persistence.save_obj(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\persistence.py", line 85, in save_obj
    _emit_update_statements(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\persistence.py", line 912, in _emit_update_statements
    c = connection.execute(
        ^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.IntegrityError: (sqlite3.IntegrityError) NOT NULL constraint failed: materials.course_id
[SQL: UPDATE materials SET course_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: (None, 2)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-27 13:48:16,484 - app.main - ERROR - Unhandled Internal Server Error: (sqlite3.IntegrityError) NOT NULL constraint failed: materials.course_id
[SQL: UPDATE materials SET course_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: (None, 2)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlite3.IntegrityError: NOT NULL constraint failed: materials.course_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 177, in __call__
    with recv_stream, send_stream, collapse_excgroups():
  File "D:\PycharmProjects\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 179, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\main.py", line 282, in log_requests
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 154, in call_next
    raise app_exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\api\v1\routers\courses.py", line 129, in delete_course_by_id
    deleted_course = delete_course(db, course_id)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 171, in delete_course
    db.commit()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2032, in commit
    trans.commit(_to_root=True)
  File "<string>", line 2, in commit
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1313, in commit
    self._prepare_impl()
  File "<string>", line 2, in _prepare_impl
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1288, in _prepare_impl
    self.session.flush()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4345, in flush
    self._flush(objects)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4480, in _flush
    with util.safe_reraise():
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4441, in _flush
    flush_context.execute()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\unitofwork.py", line 466, in execute
    rec.execute(self)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\unitofwork.py", line 642, in execute
    util.preloaded.orm_persistence.save_obj(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\persistence.py", line 85, in save_obj
    _emit_update_statements(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\persistence.py", line 912, in _emit_update_statements
    c = connection.execute(
        ^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.IntegrityError: (sqlite3.IntegrityError) NOT NULL constraint failed: materials.course_id
[SQL: UPDATE materials SET course_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: (None, 2)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-27 13:54:18,547 - app.main - INFO - Database tables created successfully.
2025-05-27 13:54:18,547 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-27 13:54:18,548 - app.main - INFO - Minio bucket check completed.
2025-05-27 13:54:18,549 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-27 13:54:18,549 - app.main - INFO - AI model loaded successfully.
2025-05-27 13:54:18,550 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-27 13:54:18,550 - app.main - INFO - Milvus initialized successfully.
2025-05-27 13:54:18,551 - app.main - INFO - Application started successfully.
2025-05-27 13:58:10,881 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-27 13:58:11,306 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-27 13:58:11,358 - app.api.v1.routers.users - INFO - User 3 (teacher_zhang) accessed /users/me.
2025-05-27 13:58:24,968 - app.main - ERROR - Unhandled Internal Server Error: (sqlite3.IntegrityError) NOT NULL constraint failed: materials.course_id
[SQL: UPDATE materials SET course_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: (None, 7)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlite3.IntegrityError: NOT NULL constraint failed: materials.course_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 177, in __call__
    with recv_stream, send_stream, collapse_excgroups():
  File "D:\PycharmProjects\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 179, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\main.py", line 282, in log_requests
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 154, in call_next
    raise app_exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\api\v1\routers\courses.py", line 129, in delete_course_by_id
    deleted_course = delete_course(db, course_id)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 171, in delete_course
    db.commit()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2032, in commit
    trans.commit(_to_root=True)
  File "<string>", line 2, in commit
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1313, in commit
    self._prepare_impl()
  File "<string>", line 2, in _prepare_impl
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1288, in _prepare_impl
    self.session.flush()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4345, in flush
    self._flush(objects)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4480, in _flush
    with util.safe_reraise():
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4441, in _flush
    flush_context.execute()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\unitofwork.py", line 466, in execute
    rec.execute(self)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\unitofwork.py", line 642, in execute
    util.preloaded.orm_persistence.save_obj(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\persistence.py", line 85, in save_obj
    _emit_update_statements(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\persistence.py", line 912, in _emit_update_statements
    c = connection.execute(
        ^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.IntegrityError: (sqlite3.IntegrityError) NOT NULL constraint failed: materials.course_id
[SQL: UPDATE materials SET course_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: (None, 7)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-27 14:00:02,443 - app.main - INFO - Database tables created successfully.
2025-05-27 14:00:02,443 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-27 14:00:02,443 - app.main - INFO - Minio bucket check completed.
2025-05-27 14:00:02,445 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-27 14:00:02,445 - app.main - INFO - AI model loaded successfully.
2025-05-27 14:00:02,445 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-27 14:00:02,445 - app.main - INFO - Milvus initialized successfully.
2025-05-27 14:00:02,446 - app.main - INFO - Application started successfully.
2025-05-27 14:00:32,711 - app.main - INFO - Database tables created successfully.
2025-05-27 14:00:32,711 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-27 14:00:32,713 - app.main - INFO - Minio bucket check completed.
2025-05-27 14:00:32,713 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-27 14:00:32,714 - app.main - INFO - AI model loaded successfully.
2025-05-27 14:00:32,714 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-27 14:00:32,715 - app.main - INFO - Milvus initialized successfully.
2025-05-27 14:00:32,715 - app.main - INFO - Application started successfully.
2025-05-27 14:01:08,250 - app.main - INFO - Database tables created successfully.
2025-05-27 14:01:08,250 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-27 14:01:08,251 - app.main - INFO - Minio bucket check completed.
2025-05-27 14:01:08,251 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-27 14:01:08,252 - app.main - INFO - AI model loaded successfully.
2025-05-27 14:01:08,252 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-27 14:01:08,252 - app.main - INFO - Milvus initialized successfully.
2025-05-27 14:01:08,253 - app.main - INFO - Application started successfully.
2025-05-27 14:02:26,581 - app.main - INFO - Database tables created successfully.
2025-05-27 14:02:26,582 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-27 14:02:26,582 - app.main - INFO - Minio bucket check completed.
2025-05-27 14:02:26,583 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-27 14:02:26,583 - app.main - INFO - AI model loaded successfully.
2025-05-27 14:02:26,584 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-27 14:02:26,584 - app.main - INFO - Milvus initialized successfully.
2025-05-27 14:02:26,584 - app.main - INFO - Application started successfully.
2025-05-27 14:02:50,166 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-27 14:02:50,395 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-27 14:02:54,532 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/1/text/db33d01c-e119-4605-9af2-559c95e6cdbf.txt
2025-05-27 14:02:54,532 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 1, object courses/1/text/db33d01c-e119-4605-9af2-559c95e6cdbf.txt.
2025-05-27 14:02:56,611 - app.api.v1.routers.mock_storage - INFO - Mock upload: Receiving file courses/1/text/db33d01c-e119-4605-9af2-559c95e6cdbf.txt (31 bytes)
2025-05-27 14:02:56,611 - app.utils.minio_client - INFO - Mock Minio: Storing content for courses/1/text/db33d01c-e119-4605-9af2-559c95e6cdbf.txt (31 bytes)
2025-05-27 14:02:56,612 - app.api.v1.routers.mock_storage - INFO - Mock upload: Successfully stored courses/1/text/db33d01c-e119-4605-9af2-559c95e6cdbf.txt
2025-05-27 14:02:58,685 - app.utils.minio_client - INFO - Mock Minio: Checking if file courses/1/text/db33d01c-e119-4605-9af2-559c95e6cdbf.txt exists
2025-05-27 14:02:58,693 - app.api.v1.routers.materials - INFO - Material '���ݿ������Բ��Բ���' (ID: 8) created successfully for course 1.
2025-05-27 14:02:58,701 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/1/text/db33d01c-e119-4605-9af2-559c95e6cdbf.txt
2025-05-27 14:03:00,778 - app.api.v1.routers.materials - INFO - Material 8 updated by user 3.
2025-05-27 14:03:03,039 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-27 14:03:07,205 - app.api.v1.routers.quizzes - INFO - Quiz '���ݿ������Բ��Բ���' (ID: 2) created by teacher 3 for course 1.
2025-05-27 14:03:09,288 - app.api.v1.routers.quizzes - INFO - Quiz '���º�Ĳ������' (ID: 2) updated by teacher 3.
2025-05-27 14:03:13,579 - app.api.v1.routers.materials - ERROR - Error vectorizing material 8: insert_vectors_into_milvus() takes 1 positional argument but 3 were given
2025-05-27 14:03:13,580 - app.api.v1.routers.materials - INFO - Material 8 processed successfully with DeepSeek analysis.
2025-05-27 14:08:21,727 - app.main - INFO - Database tables created successfully.
2025-05-27 14:08:21,727 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-27 14:08:21,728 - app.main - INFO - Minio bucket check completed.
2025-05-27 14:08:21,728 - app.main - INFO - Database tables created successfully.
2025-05-27 14:08:21,728 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-27 14:08:21,728 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-27 14:08:21,729 - app.main - INFO - AI model loaded successfully.
2025-05-27 14:08:21,729 - app.main - INFO - Minio bucket check completed.
2025-05-27 14:08:21,729 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-27 14:08:21,729 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-27 14:08:21,729 - app.main - INFO - Milvus initialized successfully.
2025-05-27 14:08:21,730 - app.main - INFO - Application started successfully.
2025-05-27 14:08:21,730 - app.main - INFO - AI model loaded successfully.
2025-05-27 14:08:21,730 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-27 14:08:21,730 - app.main - INFO - Milvus initialized successfully.
2025-05-27 14:08:21,731 - app.main - INFO - Application started successfully.
2025-05-27 14:15:04,996 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-27 14:15:05,233 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-27 14:15:09,377 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/1/text/baac398b-ff5b-491b-a4e6-ba6127d36aff.txt
2025-05-27 14:15:09,378 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 1, object courses/1/text/baac398b-ff5b-491b-a4e6-ba6127d36aff.txt.
2025-05-27 14:15:11,411 - app.api.v1.routers.mock_storage - INFO - Mock upload: Receiving file courses/1/text/baac398b-ff5b-491b-a4e6-ba6127d36aff.txt (821 bytes)
2025-05-27 14:15:11,411 - app.utils.minio_client - INFO - Mock Minio: Storing content for courses/1/text/baac398b-ff5b-491b-a4e6-ba6127d36aff.txt (821 bytes)
2025-05-27 14:15:11,412 - app.api.v1.routers.mock_storage - INFO - Mock upload: Successfully stored courses/1/text/baac398b-ff5b-491b-a4e6-ba6127d36aff.txt
2025-05-27 14:15:13,502 - app.utils.minio_client - INFO - Mock Minio: Checking if file courses/1/text/baac398b-ff5b-491b-a4e6-ba6127d36aff.txt exists
2025-05-27 14:15:13,513 - app.api.v1.routers.materials - INFO - Material 'Milvus�����������ĵ�' (ID: 9) created successfully for course 1.
2025-05-27 14:15:13,518 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/1/text/baac398b-ff5b-491b-a4e6-ba6127d36aff.txt
2025-05-27 14:15:25,794 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-27 14:15:27,891 - app.main - WARNING - Validation Error: [{'loc': ('path', 'course_id'), 'msg': 'Input should be a valid integer, unable to parse string as an integer', 'type': 'int_parsing'}, {'loc': ('body', 'title'), 'msg': 'Field required', 'type': 'missing'}, {'loc': ('body', 'file_type'), 'msg': 'Field required', 'type': 'missing'}, {'loc': ('body', 'file_path_minio'), 'msg': 'Field required', 'type': 'missing'}]
2025-05-27 14:15:28,498 - app.utils.milvus_client_utils - INFO - Mock Milvus: Inserted 1 vectors
2025-05-27 14:15:28,503 - app.api.v1.routers.materials - INFO - Material 9 vectorized and stored in Milvus with ID 0.
2025-05-27 14:15:28,504 - app.api.v1.routers.materials - INFO - Material 9 processed successfully with DeepSeek analysis.
2025-05-27 14:35:59,181 - app.auth.security - WARNING - JWT decoding failed: Signature has expired.
2025-05-27 14:35:59,182 - app.auth.dependencies - WARNING - JWT decode failed or payload is empty for HTTP request.
2025-05-27 14:36:15,324 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-27 14:36:15,659 - app.api.v1.routers.users - INFO - User 3 (teacher_zhang) accessed /users/me.
2025-05-27 14:36:24,940 - app.main - ERROR - Unhandled Internal Server Error: (sqlite3.IntegrityError) NOT NULL constraint failed: materials.course_id
[SQL: UPDATE materials SET course_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: (None, 7)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlite3.IntegrityError: NOT NULL constraint failed: materials.course_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 177, in __call__
    with recv_stream, send_stream, collapse_excgroups():
  File "D:\PycharmProjects\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 179, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\main.py", line 282, in log_requests
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 154, in call_next
    raise app_exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\api\v1\routers\courses.py", line 129, in delete_course_by_id
    deleted_course = delete_course(db, course_id)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 171, in delete_course
    db.commit()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2032, in commit
    trans.commit(_to_root=True)
  File "<string>", line 2, in commit
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1313, in commit
    self._prepare_impl()
  File "<string>", line 2, in _prepare_impl
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1288, in _prepare_impl
    self.session.flush()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4345, in flush
    self._flush(objects)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4480, in _flush
    with util.safe_reraise():
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4441, in _flush
    flush_context.execute()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\unitofwork.py", line 466, in execute
    rec.execute(self)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\unitofwork.py", line 642, in execute
    util.preloaded.orm_persistence.save_obj(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\persistence.py", line 85, in save_obj
    _emit_update_statements(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\persistence.py", line 912, in _emit_update_statements
    c = connection.execute(
        ^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.IntegrityError: (sqlite3.IntegrityError) NOT NULL constraint failed: materials.course_id
[SQL: UPDATE materials SET course_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: (None, 7)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-27 14:37:22,285 - app.main - ERROR - Unhandled Internal Server Error: (sqlite3.IntegrityError) NOT NULL constraint failed: materials.course_id
[SQL: UPDATE materials SET course_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: (None, 2)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlite3.IntegrityError: NOT NULL constraint failed: materials.course_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 177, in __call__
    with recv_stream, send_stream, collapse_excgroups():
  File "D:\PycharmProjects\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 179, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\main.py", line 282, in log_requests
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 154, in call_next
    raise app_exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\api\v1\routers\courses.py", line 129, in delete_course_by_id
    deleted_course = delete_course(db, course_id)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 171, in delete_course
    db.commit()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2032, in commit
    trans.commit(_to_root=True)
  File "<string>", line 2, in commit
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1313, in commit
    self._prepare_impl()
  File "<string>", line 2, in _prepare_impl
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1288, in _prepare_impl
    self.session.flush()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4345, in flush
    self._flush(objects)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4480, in _flush
    with util.safe_reraise():
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4441, in _flush
    flush_context.execute()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\unitofwork.py", line 466, in execute
    rec.execute(self)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\unitofwork.py", line 642, in execute
    util.preloaded.orm_persistence.save_obj(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\persistence.py", line 85, in save_obj
    _emit_update_statements(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\persistence.py", line 912, in _emit_update_statements
    c = connection.execute(
        ^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.IntegrityError: (sqlite3.IntegrityError) NOT NULL constraint failed: materials.course_id
[SQL: UPDATE materials SET course_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: (None, 2)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-27 14:38:57,551 - app.main - INFO - Database tables created successfully.
2025-05-27 14:38:57,551 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-27 14:38:57,552 - app.main - INFO - Minio bucket check completed.
2025-05-27 14:38:57,552 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-27 14:38:57,552 - app.main - INFO - AI model loaded successfully.
2025-05-27 14:38:57,552 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-27 14:38:57,552 - app.main - INFO - Milvus initialized successfully.
2025-05-27 14:38:57,553 - app.main - INFO - Application started successfully.
2025-05-27 14:40:20,637 - app.main - INFO - Database tables created successfully.
2025-05-27 14:40:20,638 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-27 14:40:20,638 - app.main - INFO - Minio bucket check completed.
2025-05-27 14:40:20,638 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-27 14:40:20,638 - app.main - INFO - AI model loaded successfully.
2025-05-27 14:40:20,639 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-27 14:40:20,639 - app.main - INFO - Milvus initialized successfully.
2025-05-27 14:40:20,639 - app.main - INFO - Application started successfully.
2025-05-27 14:40:45,208 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-27 14:40:45,592 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-27 14:40:47,663 - app.api.v1.routers.courses - INFO - Course 'ɾ�����Կγ�' (ID: 5) created by teacher 3.
2025-05-27 14:40:49,709 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/5/text/8a27e32c-5597-4494-abbc-9de233bb861c.txt
2025-05-27 14:40:49,710 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 5, object courses/5/text/8a27e32c-5597-4494-abbc-9de233bb861c.txt.
2025-05-27 14:40:51,765 - app.api.v1.routers.mock_storage - INFO - Mock upload: Receiving file courses/5/text/8a27e32c-5597-4494-abbc-9de233bb861c.txt (60 bytes)
2025-05-27 14:40:51,765 - app.utils.minio_client - INFO - Mock Minio: Storing content for courses/5/text/8a27e32c-5597-4494-abbc-9de233bb861c.txt (60 bytes)
2025-05-27 14:40:51,768 - app.api.v1.routers.mock_storage - INFO - Mock upload: Successfully stored courses/5/text/8a27e32c-5597-4494-abbc-9de233bb861c.txt
2025-05-27 14:40:53,822 - app.utils.minio_client - INFO - Mock Minio: Checking if file courses/5/text/8a27e32c-5597-4494-abbc-9de233bb861c.txt exists
2025-05-27 14:40:53,834 - app.api.v1.routers.materials - INFO - Material 'ɾ�����Բ���' (ID: 10) created successfully for course 5.
2025-05-27 14:40:53,845 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/5/text/8a27e32c-5597-4494-abbc-9de233bb861c.txt
2025-05-27 14:40:55,891 - app.api.v1.routers.quizzes - INFO - Quiz 'ɾ�����Բ���' (ID: 3) created by teacher 3 for course 5.
2025-05-27 14:40:57,973 - app.main - WARNING - Validation Error: [{'loc': ('body', 'options'), 'msg': 'Input should be a valid dictionary', 'type': 'dict_type'}]
2025-05-27 14:41:12,731 - app.utils.milvus_client_utils - INFO - Mock Milvus: Inserted 1 vectors
2025-05-27 14:41:12,734 - app.api.v1.routers.materials - ERROR - Error vectorizing material 10: (sqlite3.IntegrityError) UNIQUE constraint failed: materials.milvus_vector_id
[SQL: UPDATE materials SET milvus_vector_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: ('0', 10)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-27 14:41:12,735 - app.api.v1.routers.materials - INFO - Material 10 processed successfully with DeepSeek analysis.
2025-05-27 14:49:48,939 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-27 14:49:50,989 - app.api.v1.routers.courses - INFO - Course '��ɾ�����Կγ�' (ID: 6) created by teacher 3.
2025-05-27 14:49:53,034 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/6/text/fd1fc671-f72f-4aef-b999-d8f472499b9c.txt
2025-05-27 14:49:53,034 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 6, object courses/6/text/fd1fc671-f72f-4aef-b999-d8f472499b9c.txt.
2025-05-27 14:49:55,109 - app.api.v1.routers.mock_storage - INFO - Mock upload: Receiving file courses/6/text/fd1fc671-f72f-4aef-b999-d8f472499b9c.txt (66 bytes)
2025-05-27 14:49:55,109 - app.utils.minio_client - INFO - Mock Minio: Storing content for courses/6/text/fd1fc671-f72f-4aef-b999-d8f472499b9c.txt (66 bytes)
2025-05-27 14:49:55,112 - app.api.v1.routers.mock_storage - INFO - Mock upload: Successfully stored courses/6/text/fd1fc671-f72f-4aef-b999-d8f472499b9c.txt
2025-05-27 14:49:57,173 - app.utils.minio_client - INFO - Mock Minio: Checking if file courses/6/text/fd1fc671-f72f-4aef-b999-d8f472499b9c.txt exists
2025-05-27 14:49:57,184 - app.api.v1.routers.materials - INFO - Material '��ɾ�����Բ���' (ID: 11) created successfully for course 6.
2025-05-27 14:49:57,194 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/6/text/fd1fc671-f72f-4aef-b999-d8f472499b9c.txt
2025-05-27 14:50:01,303 - app.crud - INFO - Course 6 and all associated data deleted successfully.
2025-05-27 14:50:01,304 - app.api.v1.routers.courses - INFO - Course 6 deleted by teacher 3.
2025-05-27 14:50:14,649 - app.utils.milvus_client_utils - INFO - Mock Milvus: Inserted 1 vectors
2025-05-27 14:50:14,650 - app.api.v1.routers.materials - INFO - Material 11 vectorized and stored in Milvus with ID 0.
2025-05-27 14:50:14,651 - app.api.v1.routers.materials - INFO - Material 11 processed successfully with DeepSeek analysis.
2025-05-27 15:05:06,226 - app.crud - INFO - Course 4 and all associated data deleted successfully.
2025-05-27 15:05:06,227 - app.api.v1.routers.courses - INFO - Course 4 deleted by teacher 3.
2025-05-27 15:05:15,966 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/2/pdf/f4ea669a-39f7-49f9-99ac-64cd2ce9bd40.pdf
2025-05-27 15:05:15,966 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 2, object courses/2/pdf/f4ea669a-39f7-49f9-99ac-64cd2ce9bd40.pdf.
2025-05-27 15:05:15,998 - app.api.v1.routers.mock_storage - INFO - Mock upload: Receiving file courses/2/pdf/f4ea669a-39f7-49f9-99ac-64cd2ce9bd40.pdf (1028434 bytes)
2025-05-27 15:05:15,998 - app.utils.minio_client - INFO - Mock Minio: Storing content for courses/2/pdf/f4ea669a-39f7-49f9-99ac-64cd2ce9bd40.pdf (1028434 bytes)
2025-05-27 15:05:16,000 - app.api.v1.routers.mock_storage - INFO - Mock upload: Successfully stored courses/2/pdf/f4ea669a-39f7-49f9-99ac-64cd2ce9bd40.pdf
2025-05-27 15:05:16,024 - app.utils.minio_client - INFO - Mock Minio: Checking if file courses/2/pdf/f4ea669a-39f7-49f9-99ac-64cd2ce9bd40.pdf exists
2025-05-27 15:05:16,029 - app.api.v1.routers.materials - INFO - Material '������̨�ݽ�������' (ID: 11) created successfully for course 2.
2025-05-27 15:05:16,035 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/2/pdf/f4ea669a-39f7-49f9-99ac-64cd2ce9bd40.pdf
2025-05-27 15:05:47,184 - app.utils.milvus_client_utils - INFO - Mock Milvus: Inserted 1 vectors
2025-05-27 15:05:47,186 - app.api.v1.routers.materials - ERROR - Error vectorizing material 11: (sqlite3.IntegrityError) UNIQUE constraint failed: materials.milvus_vector_id
[SQL: UPDATE materials SET milvus_vector_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: ('0', 11)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-27 15:05:47,187 - app.api.v1.routers.materials - INFO - Material 11 processed successfully with DeepSeek analysis.
2025-05-28 10:19:05,222 - app.auth.security - WARNING - JWT decoding failed: Signature has expired.
2025-05-28 10:19:05,224 - app.auth.dependencies - WARNING - JWT decode failed or payload is empty for HTTP request.
2025-05-28 10:19:05,241 - app.auth.security - WARNING - JWT decoding failed: Signature has expired.
2025-05-28 10:19:05,242 - app.auth.dependencies - WARNING - JWT decode failed or payload is empty for HTTP request.
2025-05-28 10:19:06,674 - app.auth.security - WARNING - JWT decoding failed: Signature has expired.
2025-05-28 10:19:06,674 - app.auth.dependencies - WARNING - JWT decode failed or payload is empty for HTTP request.
2025-05-28 10:19:06,968 - app.auth.security - WARNING - JWT decoding failed: Signature has expired.
2025-05-28 10:19:06,983 - app.auth.dependencies - WARNING - JWT decode failed or payload is empty for HTTP request.
2025-05-28 10:22:26,263 - app.main - INFO - Database tables created successfully.
2025-05-28 10:22:26,264 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-28 10:22:26,264 - app.main - INFO - Minio bucket check completed.
2025-05-28 10:22:26,265 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-28 10:22:26,265 - app.main - INFO - AI model loaded successfully.
2025-05-28 10:22:26,265 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-28 10:22:26,265 - app.main - INFO - Milvus initialized successfully.
2025-05-28 10:22:26,266 - app.main - INFO - Application started successfully.
2025-05-28 10:25:08,977 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-28 10:25:11,064 - app.api.v1.routers.courses - INFO - Course 'Python��̻���' (ID: 6) created by teacher 3.
2025-05-28 10:25:13,154 - app.api.v1.routers.courses - INFO - Course '���ݽṹ���㷨' (ID: 7) created by teacher 3.
2025-05-28 10:25:15,240 - app.api.v1.routers.courses - INFO - Course 'Web����ʵս' (ID: 8) created by teacher 3.
2025-05-28 10:25:17,324 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/6/text/6050d7c4-9677-421b-b208-b2c823f075e4.txt
2025-05-28 10:25:17,325 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 6, object courses/6/text/6050d7c4-9677-421b-b208-b2c823f075e4.txt.
2025-05-28 10:25:19,391 - app.api.v1.routers.mock_storage - INFO - Mock upload: Receiving file courses/6/text/6050d7c4-9677-421b-b208-b2c823f075e4.txt (430 bytes)
2025-05-28 10:25:19,391 - app.utils.minio_client - INFO - Mock Minio: Storing content for courses/6/text/6050d7c4-9677-421b-b208-b2c823f075e4.txt (430 bytes)
2025-05-28 10:25:19,392 - app.api.v1.routers.mock_storage - INFO - Mock upload: Successfully stored courses/6/text/6050d7c4-9677-421b-b208-b2c823f075e4.txt
2025-05-28 10:25:21,461 - app.utils.minio_client - INFO - Mock Minio: Checking if file courses/6/text/6050d7c4-9677-421b-b208-b2c823f075e4.txt exists
2025-05-28 10:25:21,473 - app.api.v1.routers.materials - INFO - Material 'Python�����﷨' (ID: 12) created successfully for course 6.
2025-05-28 10:25:21,477 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/6/text/6050d7c4-9677-421b-b208-b2c823f075e4.txt
2025-05-28 10:25:23,525 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/7/text/37ee32b1-7c1a-47ce-9b6c-bade3618064b.txt
2025-05-28 10:25:23,525 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 7, object courses/7/text/37ee32b1-7c1a-47ce-9b6c-bade3618064b.txt.
2025-05-28 10:25:25,550 - app.api.v1.routers.mock_storage - INFO - Mock upload: Receiving file courses/7/text/37ee32b1-7c1a-47ce-9b6c-bade3618064b.txt (527 bytes)
2025-05-28 10:25:25,550 - app.utils.minio_client - INFO - Mock Minio: Storing content for courses/7/text/37ee32b1-7c1a-47ce-9b6c-bade3618064b.txt (527 bytes)
2025-05-28 10:25:25,553 - app.api.v1.routers.mock_storage - INFO - Mock upload: Successfully stored courses/7/text/37ee32b1-7c1a-47ce-9b6c-bade3618064b.txt
2025-05-28 10:25:27,581 - app.utils.minio_client - INFO - Mock Minio: Checking if file courses/7/text/37ee32b1-7c1a-47ce-9b6c-bade3618064b.txt exists
2025-05-28 10:25:27,591 - app.api.v1.routers.materials - INFO - Material '���ݽṹ����' (ID: 13) created successfully for course 7.
2025-05-28 10:25:27,595 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/7/text/37ee32b1-7c1a-47ce-9b6c-bade3618064b.txt
2025-05-28 10:25:29,635 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/8/text/6ed114cc-271f-4ea6-93a2-298dfd143396.txt
2025-05-28 10:25:29,636 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 8, object courses/8/text/6ed114cc-271f-4ea6-93a2-298dfd143396.txt.
2025-05-28 10:25:31,677 - app.api.v1.routers.mock_storage - INFO - Mock upload: Receiving file courses/8/text/6ed114cc-271f-4ea6-93a2-298dfd143396.txt (557 bytes)
2025-05-28 10:25:31,677 - app.utils.minio_client - INFO - Mock Minio: Storing content for courses/8/text/6ed114cc-271f-4ea6-93a2-298dfd143396.txt (557 bytes)
2025-05-28 10:25:31,678 - app.api.v1.routers.mock_storage - INFO - Mock upload: Successfully stored courses/8/text/6ed114cc-271f-4ea6-93a2-298dfd143396.txt
2025-05-28 10:25:33,709 - app.utils.minio_client - INFO - Mock Minio: Checking if file courses/8/text/6ed114cc-271f-4ea6-93a2-298dfd143396.txt exists
2025-05-28 10:25:33,722 - app.api.v1.routers.materials - INFO - Material 'Web��������ջ' (ID: 14) created successfully for course 8.
2025-05-28 10:25:33,727 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/8/text/6ed114cc-271f-4ea6-93a2-298dfd143396.txt
2025-05-28 10:25:36,384 - app.utils.milvus_client_utils - INFO - Mock Milvus: Inserted 1 vectors
2025-05-28 10:25:36,386 - app.api.v1.routers.materials - ERROR - Error vectorizing material 12: (sqlite3.IntegrityError) UNIQUE constraint failed: materials.milvus_vector_id
[SQL: UPDATE materials SET milvus_vector_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: ('0', 12)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-28 10:25:36,386 - app.api.v1.routers.materials - INFO - Material 12 processed successfully with DeepSeek analysis.
2025-05-28 10:25:41,981 - app.utils.minio_client - INFO - Mock Minio: Deleting courses/6/text/6050d7c4-9677-421b-b208-b2c823f075e4.txt
2025-05-28 10:25:41,994 - app.api.v1.routers.materials - INFO - Material 12 deleted by user 3.
2025-05-28 10:25:43,712 - app.utils.milvus_client_utils - INFO - Mock Milvus: Inserted 1 vectors
2025-05-28 10:25:43,717 - app.api.v1.routers.materials - ERROR - Error vectorizing material 13: (sqlite3.IntegrityError) UNIQUE constraint failed: materials.milvus_vector_id
[SQL: UPDATE materials SET milvus_vector_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: ('0', 13)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-28 10:25:43,719 - app.api.v1.routers.materials - INFO - Material 13 processed successfully with DeepSeek analysis.
2025-05-28 10:25:57,197 - app.utils.milvus_client_utils - INFO - Mock Milvus: Inserted 1 vectors
2025-05-28 10:25:57,242 - app.api.v1.routers.materials - ERROR - Error vectorizing material 14: (sqlite3.IntegrityError) UNIQUE constraint failed: materials.milvus_vector_id
[SQL: UPDATE materials SET milvus_vector_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: ('0', 14)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-28 10:25:57,242 - app.api.v1.routers.materials - INFO - Material 14 processed successfully with DeepSeek analysis.
2025-05-28 10:27:17,681 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-28 10:27:21,971 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/1/text/699d49ca-3292-45c0-a9d1-1534da3e7dab.txt
2025-05-28 10:27:21,972 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 1, object courses/1/text/699d49ca-3292-45c0-a9d1-1534da3e7dab.txt.
2025-05-28 10:27:24,051 - app.api.v1.routers.mock_storage - INFO - Mock upload: Receiving file courses/1/text/699d49ca-3292-45c0-a9d1-1534da3e7dab.txt (45 bytes)
2025-05-28 10:27:24,052 - app.utils.minio_client - INFO - Mock Minio: Storing content for courses/1/text/699d49ca-3292-45c0-a9d1-1534da3e7dab.txt (45 bytes)
2025-05-28 10:27:24,055 - app.api.v1.routers.mock_storage - INFO - Mock upload: Successfully stored courses/1/text/699d49ca-3292-45c0-a9d1-1534da3e7dab.txt
2025-05-28 10:27:26,174 - app.utils.minio_client - INFO - Mock Minio: Checking if file courses/1/text/699d49ca-3292-45c0-a9d1-1534da3e7dab.txt exists
2025-05-28 10:27:26,192 - app.api.v1.routers.materials - INFO - Material 'ɾ�����Բ���' (ID: 15) created successfully for course 1.
2025-05-28 10:27:26,209 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/1/text/699d49ca-3292-45c0-a9d1-1534da3e7dab.txt
2025-05-28 10:27:39,481 - app.utils.milvus_client_utils - INFO - Mock Milvus: Inserted 1 vectors
2025-05-28 10:27:39,485 - app.api.v1.routers.materials - ERROR - Error vectorizing material 15: (sqlite3.IntegrityError) UNIQUE constraint failed: materials.milvus_vector_id
[SQL: UPDATE materials SET milvus_vector_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: ('0', 15)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-28 10:27:39,486 - app.api.v1.routers.materials - INFO - Material 15 processed successfully with DeepSeek analysis.
2025-05-28 10:28:26,811 - app.main - INFO - Database tables created successfully.
2025-05-28 10:28:26,812 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-28 10:28:26,812 - app.main - INFO - Minio bucket check completed.
2025-05-28 10:28:26,812 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-28 10:28:26,814 - app.main - INFO - AI model loaded successfully.
2025-05-28 10:28:26,814 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-28 10:28:26,815 - app.main - INFO - Milvus initialized successfully.
2025-05-28 10:28:26,815 - app.main - INFO - Application started successfully.
2025-05-28 10:28:27,030 - app.main - INFO - Database tables created successfully.
2025-05-28 10:28:27,032 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-28 10:28:27,032 - app.main - INFO - Minio bucket check completed.
2025-05-28 10:28:27,034 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-28 10:28:27,034 - app.main - INFO - AI model loaded successfully.
2025-05-28 10:28:27,036 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-28 10:28:27,037 - app.main - INFO - Milvus initialized successfully.
2025-05-28 10:28:27,038 - app.main - INFO - Application started successfully.
2025-05-28 10:28:41,134 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-28 10:28:41,384 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-28 10:28:45,449 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/1/text/d9bfbd8b-e0a6-4feb-a5ba-4a1d510c6144.txt
2025-05-28 10:28:45,450 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 1, object courses/1/text/d9bfbd8b-e0a6-4feb-a5ba-4a1d510c6144.txt.
2025-05-28 10:28:47,500 - app.api.v1.routers.mock_storage - INFO - Mock upload: Receiving file courses/1/text/d9bfbd8b-e0a6-4feb-a5ba-4a1d510c6144.txt (45 bytes)
2025-05-28 10:28:47,501 - app.utils.minio_client - INFO - Mock Minio: Storing content for courses/1/text/d9bfbd8b-e0a6-4feb-a5ba-4a1d510c6144.txt (45 bytes)
2025-05-28 10:28:47,501 - app.api.v1.routers.mock_storage - INFO - Mock upload: Successfully stored courses/1/text/d9bfbd8b-e0a6-4feb-a5ba-4a1d510c6144.txt
2025-05-28 10:28:49,543 - app.utils.minio_client - INFO - Mock Minio: Checking if file courses/1/text/d9bfbd8b-e0a6-4feb-a5ba-4a1d510c6144.txt exists
2025-05-28 10:28:49,558 - app.api.v1.routers.materials - INFO - Material 'ɾ�����Բ���' (ID: 16) created successfully for course 1.
2025-05-28 10:28:49,563 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/1/text/d9bfbd8b-e0a6-4feb-a5ba-4a1d510c6144.txt
2025-05-28 10:28:53,670 - app.utils.minio_client - INFO - Mock Minio: Deleting courses/1/text/d9bfbd8b-e0a6-4feb-a5ba-4a1d510c6144.txt
2025-05-28 10:28:53,685 - app.api.v1.routers.materials - INFO - Material 16 deleted by user 3.
2025-05-28 10:28:57,761 - app.api.v1.routers.materials - WARNING - Get material request: Material 16 not found.
2025-05-28 10:28:57,762 - app.main - ERROR - Custom HTTP Exception occurred: Material not found., Code: NOT_FOUND, Status: 404, Data: None
2025-05-28 10:29:05,325 - app.utils.milvus_client_utils - INFO - Mock Milvus: Inserted 1 vectors
2025-05-28 10:29:05,327 - app.api.v1.routers.materials - INFO - Material 16 vectorized and stored in Milvus with ID 0.
2025-05-28 10:29:05,328 - app.api.v1.routers.materials - INFO - Material 16 processed successfully with DeepSeek analysis.
2025-05-28 10:29:11,504 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-28 10:29:13,580 - app.api.v1.routers.courses - INFO - Course 'Python��̻���' (ID: 9) created by teacher 3.
2025-05-28 10:29:15,648 - app.api.v1.routers.courses - INFO - Course '���ݽṹ���㷨' (ID: 10) created by teacher 3.
2025-05-28 10:29:17,741 - app.api.v1.routers.courses - INFO - Course 'Web����ʵս' (ID: 11) created by teacher 3.
2025-05-28 10:29:19,798 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/9/text/ffd4b252-4ce2-4444-bdd9-4692667e2cf1.txt
2025-05-28 10:29:19,798 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 9, object courses/9/text/ffd4b252-4ce2-4444-bdd9-4692667e2cf1.txt.
2025-05-28 10:29:21,865 - app.api.v1.routers.mock_storage - INFO - Mock upload: Receiving file courses/9/text/ffd4b252-4ce2-4444-bdd9-4692667e2cf1.txt (430 bytes)
2025-05-28 10:29:21,866 - app.utils.minio_client - INFO - Mock Minio: Storing content for courses/9/text/ffd4b252-4ce2-4444-bdd9-4692667e2cf1.txt (430 bytes)
2025-05-28 10:29:21,868 - app.api.v1.routers.mock_storage - INFO - Mock upload: Successfully stored courses/9/text/ffd4b252-4ce2-4444-bdd9-4692667e2cf1.txt
2025-05-28 10:29:23,958 - app.utils.minio_client - INFO - Mock Minio: Checking if file courses/9/text/ffd4b252-4ce2-4444-bdd9-4692667e2cf1.txt exists
2025-05-28 10:29:23,975 - app.api.v1.routers.materials - INFO - Material 'Python�����﷨' (ID: 16) created successfully for course 9.
2025-05-28 10:29:23,991 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/9/text/ffd4b252-4ce2-4444-bdd9-4692667e2cf1.txt
2025-05-28 10:29:26,055 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/10/text/60efbc5c-8a64-43ce-b058-717edb251908.txt
2025-05-28 10:29:26,056 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 10, object courses/10/text/60efbc5c-8a64-43ce-b058-717edb251908.txt.
2025-05-28 10:29:28,107 - app.api.v1.routers.mock_storage - INFO - Mock upload: Receiving file courses/10/text/60efbc5c-8a64-43ce-b058-717edb251908.txt (527 bytes)
2025-05-28 10:29:28,108 - app.utils.minio_client - INFO - Mock Minio: Storing content for courses/10/text/60efbc5c-8a64-43ce-b058-717edb251908.txt (527 bytes)
2025-05-28 10:29:28,109 - app.api.v1.routers.mock_storage - INFO - Mock upload: Successfully stored courses/10/text/60efbc5c-8a64-43ce-b058-717edb251908.txt
2025-05-28 10:29:30,173 - app.utils.minio_client - INFO - Mock Minio: Checking if file courses/10/text/60efbc5c-8a64-43ce-b058-717edb251908.txt exists
2025-05-28 10:29:30,189 - app.api.v1.routers.materials - INFO - Material '���ݽṹ����' (ID: 17) created successfully for course 10.
2025-05-28 10:29:30,197 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/10/text/60efbc5c-8a64-43ce-b058-717edb251908.txt
2025-05-28 10:29:32,286 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/11/text/9b68888f-2f03-4aca-924f-9ad5912f2d6e.txt
2025-05-28 10:29:32,287 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 11, object courses/11/text/9b68888f-2f03-4aca-924f-9ad5912f2d6e.txt.
2025-05-28 10:29:34,367 - app.api.v1.routers.mock_storage - INFO - Mock upload: Receiving file courses/11/text/9b68888f-2f03-4aca-924f-9ad5912f2d6e.txt (557 bytes)
2025-05-28 10:29:34,367 - app.utils.minio_client - INFO - Mock Minio: Storing content for courses/11/text/9b68888f-2f03-4aca-924f-9ad5912f2d6e.txt (557 bytes)
2025-05-28 10:29:34,369 - app.api.v1.routers.mock_storage - INFO - Mock upload: Successfully stored courses/11/text/9b68888f-2f03-4aca-924f-9ad5912f2d6e.txt
2025-05-28 10:29:36,411 - app.utils.minio_client - INFO - Mock Minio: Checking if file courses/11/text/9b68888f-2f03-4aca-924f-9ad5912f2d6e.txt exists
2025-05-28 10:29:36,428 - app.api.v1.routers.materials - INFO - Material 'Web��������ջ' (ID: 18) created successfully for course 11.
2025-05-28 10:29:36,440 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/11/text/9b68888f-2f03-4aca-924f-9ad5912f2d6e.txt
2025-05-28 10:29:41,675 - app.utils.milvus_client_utils - INFO - Mock Milvus: Inserted 1 vectors
2025-05-28 10:29:41,677 - app.api.v1.routers.materials - ERROR - Error vectorizing material 16: (sqlite3.IntegrityError) UNIQUE constraint failed: materials.milvus_vector_id
[SQL: UPDATE materials SET milvus_vector_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: ('0', 16)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-28 10:29:41,678 - app.api.v1.routers.materials - INFO - Material 16 processed successfully with DeepSeek analysis.
2025-05-28 10:29:44,645 - app.utils.minio_client - INFO - Mock Minio: Deleting courses/9/text/ffd4b252-4ce2-4444-bdd9-4692667e2cf1.txt
2025-05-28 10:29:44,661 - app.api.v1.routers.materials - INFO - Material 16 deleted by user 3.
2025-05-28 10:29:45,912 - app.utils.milvus_client_utils - INFO - Mock Milvus: Inserted 1 vectors
2025-05-28 10:29:45,914 - app.api.v1.routers.materials - ERROR - Error vectorizing material 17: (sqlite3.IntegrityError) UNIQUE constraint failed: materials.milvus_vector_id
[SQL: UPDATE materials SET milvus_vector_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: ('0', 17)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-28 10:29:45,917 - app.api.v1.routers.materials - INFO - Material 17 processed successfully with DeepSeek analysis.
2025-05-28 10:29:46,737 - app.api.v1.routers.materials - WARNING - Get material request: Material 16 not found.
2025-05-28 10:29:46,738 - app.main - ERROR - Custom HTTP Exception occurred: Material not found., Code: NOT_FOUND, Status: 404, Data: None
2025-05-28 10:29:48,792 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/10/text/60efbc5c-8a64-43ce-b058-717edb251908.txt
2025-05-28 10:30:00,411 - app.utils.milvus_client_utils - INFO - Mock Milvus: Inserted 1 vectors
2025-05-28 10:30:00,413 - app.api.v1.routers.materials - ERROR - Error vectorizing material 18: (sqlite3.IntegrityError) UNIQUE constraint failed: materials.milvus_vector_id
[SQL: UPDATE materials SET milvus_vector_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: ('0', 18)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-28 10:30:00,414 - app.api.v1.routers.materials - INFO - Material 18 processed successfully with DeepSeek analysis.
2025-05-28 10:30:08,491 - app.api.v1.routers.materials - INFO - Material 17 analyzed with DeepSeek by user 3.
2025-05-28 10:34:18,676 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-28 10:34:22,847 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/12/text/27773238-281f-47c8-a23c-44967b13da6c.txt
2025-05-28 10:34:22,847 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 12, object courses/12/text/27773238-281f-47c8-a23c-44967b13da6c.txt.
2025-05-28 10:34:24,916 - app.api.v1.routers.mock_storage - INFO - Mock upload: Receiving file courses/12/text/27773238-281f-47c8-a23c-44967b13da6c.txt (280 bytes)
2025-05-28 10:34:24,917 - app.utils.minio_client - INFO - Mock Minio: Storing content for courses/12/text/27773238-281f-47c8-a23c-44967b13da6c.txt (280 bytes)
2025-05-28 10:34:24,917 - app.api.v1.routers.mock_storage - INFO - Mock upload: Successfully stored courses/12/text/27773238-281f-47c8-a23c-44967b13da6c.txt
2025-05-28 10:34:27,120 - app.utils.minio_client - INFO - Mock Minio: Checking if file courses/12/text/27773238-281f-47c8-a23c-44967b13da6c.txt exists
2025-05-28 10:34:27,146 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/12/text/27773238-281f-47c8-a23c-44967b13da6c.txt
2025-05-28 10:34:31,283 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/12/text/27773238-281f-47c8-a23c-44967b13da6c.txt
2025-05-28 10:34:44,254 - app.api.v1.routers.materials - INFO - Material 19 analyzed with DeepSeek by user 3.
2025-05-28 10:34:44,781 - app.utils.milvus_client_utils - INFO - Mock Milvus: Inserted 1 vectors
2025-05-28 10:34:44,784 - app.api.v1.routers.materials - ERROR - Error vectorizing material 19: (sqlite3.IntegrityError) UNIQUE constraint failed: materials.milvus_vector_id
[SQL: UPDATE materials SET milvus_vector_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: ('0', 19)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-28 10:34:44,784 - app.api.v1.routers.materials - INFO - Material 19 processed successfully with DeepSeek analysis.
2025-05-28 10:34:46,349 - app.utils.minio_client - INFO - Mock Minio: Deleting courses/12/text/27773238-281f-47c8-a23c-44967b13da6c.txt
2025-05-28 10:34:46,361 - app.api.v1.routers.materials - INFO - Material 19 deleted by user 3.
2025-05-28 10:34:49,591 - app.api.v1.routers.materials - WARNING - Get material request: Material 19 not found.
2025-05-28 10:34:49,596 - app.main - ERROR - Custom HTTP Exception occurred: Material not found., Code: NOT_FOUND, Status: 404, Data: None
2025-05-28 10:37:22,630 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-28 10:37:23,833 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-28 10:37:27,742 - app.api.v1.routers.users - INFO - User 3 (teacher_zhang) accessed /users/me.
2025-05-28 10:37:27,769 - app.api.v1.routers.users - INFO - User 3 (teacher_zhang) accessed /users/me.
2025-05-28 10:39:15,123 - app.main - INFO - Database tables created successfully.
2025-05-28 10:39:15,123 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-28 10:39:15,123 - app.main - INFO - Minio bucket check completed.
2025-05-28 10:39:15,124 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-28 10:39:15,124 - app.main - INFO - AI model loaded successfully.
2025-05-28 10:39:15,125 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-28 10:39:15,125 - app.main - INFO - Milvus initialized successfully.
2025-05-28 10:39:15,125 - app.main - INFO - Application started successfully.
2025-05-28 10:40:25,711 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-28 10:40:26,371 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-28 10:40:26,474 - app.api.v1.routers.users - INFO - User 3 (teacher_zhang) accessed /users/me.
2025-05-28 10:40:43,709 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/1/pdf/8bebb555-209a-4911-9897-68f523fbc805.pdf
2025-05-28 10:40:43,711 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 1, object courses/1/pdf/8bebb555-209a-4911-9897-68f523fbc805.pdf.
2025-05-28 10:40:43,760 - app.api.v1.routers.mock_storage - INFO - Mock upload: Receiving file courses/1/pdf/8bebb555-209a-4911-9897-68f523fbc805.pdf (1028434 bytes)
2025-05-28 10:40:43,761 - app.utils.minio_client - INFO - Mock Minio: Storing content for courses/1/pdf/8bebb555-209a-4911-9897-68f523fbc805.pdf (1028434 bytes)
2025-05-28 10:40:43,763 - app.api.v1.routers.mock_storage - INFO - Mock upload: Successfully stored courses/1/pdf/8bebb555-209a-4911-9897-68f523fbc805.pdf
2025-05-28 10:40:43,871 - app.utils.minio_client - INFO - Mock Minio: Checking if file courses/1/pdf/8bebb555-209a-4911-9897-68f523fbc805.pdf exists
2025-05-28 10:40:43,894 - app.api.v1.routers.materials - INFO - Material '������̨�ݽ�������' (ID: 19) created successfully for course 1.
2025-05-28 10:40:43,992 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/1/pdf/8bebb555-209a-4911-9897-68f523fbc805.pdf
2025-05-28 10:41:04,212 - app.utils.minio_client - INFO - Mock Minio: Deleting courses/1/text/db33d01c-e119-4605-9af2-559c95e6cdbf.txt
2025-05-28 10:41:04,237 - app.api.v1.routers.materials - INFO - Material 8 deleted by user 3.
2025-05-28 10:41:08,916 - app.utils.minio_client - INFO - Mock Minio: Deleting courses/1/text/c53f6395-8ead-4c03-904d-c352fc7b1308.txt
2025-05-28 10:41:08,934 - app.api.v1.routers.materials - INFO - Material 6 deleted by user 3.
2025-05-28 10:41:08,990 - app.utils.milvus_client_utils - INFO - Mock Milvus: Inserted 1 vectors
2025-05-28 10:41:09,004 - app.api.v1.routers.materials - ERROR - Error vectorizing material 19: (sqlite3.IntegrityError) UNIQUE constraint failed: materials.milvus_vector_id
[SQL: UPDATE materials SET milvus_vector_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: ('0', 19)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-28 10:41:09,027 - app.api.v1.routers.materials - INFO - Material 19 processed successfully with DeepSeek analysis.
2025-05-28 10:41:12,388 - app.utils.minio_client - INFO - Mock Minio: Deleting courses/1/text/e25d17dd-4ac5-4bda-ae58-ef135894202c.txt
2025-05-28 10:41:12,399 - app.api.v1.routers.materials - INFO - Material 5 deleted by user 3.
2025-05-28 10:41:15,480 - app.utils.minio_client - INFO - Mock Minio: Deleting courses/1/text/c6bf37a2-ccb2-458c-9c26-1174c5e182ac.txt
2025-05-28 10:41:15,497 - app.api.v1.routers.materials - INFO - Material 4 deleted by user 3.
2025-05-28 10:41:17,532 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/1/pdf/8bebb555-209a-4911-9897-68f523fbc805.pdf
2025-05-28 10:41:41,906 - app.api.v1.routers.materials - INFO - Material 19 analyzed with DeepSeek by user 3.
