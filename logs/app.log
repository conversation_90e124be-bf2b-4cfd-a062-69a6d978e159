2025-05-26 19:45:40,314 - app.main - WARNING - Database initialization failed: 'utf-8' codec can't decode byte 0xd6 in position 61: invalid continuation byte. Continuing without database...
2025-05-26 19:45:40,315 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 19:45:40,316 - app.main - INFO - Minio bucket check completed.
2025-05-26 19:45:40,316 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 19:45:40,317 - app.main - INFO - AI model loaded successfully.
2025-05-26 19:45:40,317 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 19:45:40,318 - app.main - INFO - Milvus initialized successfully.
2025-05-26 19:45:40,319 - app.main - INFO - Application started successfully.
2025-05-26 19:54:04,783 - app.main - INFO - Database tables created successfully.
2025-05-26 19:54:04,783 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 19:54:04,784 - app.main - INFO - Minio bucket check completed.
2025-05-26 19:54:04,784 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 19:54:04,784 - app.main - INFO - AI model loaded successfully.
2025-05-26 19:54:04,784 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 19:54:04,784 - app.main - INFO - Milvus initialized successfully.
2025-05-26 19:54:04,784 - app.main - INFO - Application started successfully.
2025-05-26 19:55:42,141 - app.main - ERROR - Unhandled Internal Server Error: bcrypt: no backends available -- recommend you install one (e.g. 'pip install bcrypt')
Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 214, in run_endpoint_function
    return await run_in_threadpool(dependant.call, **values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\concurrency.py", line 37, in run_in_threadpool
    return await anyio.to_thread.run_sync(func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\anyio\to_thread.py", line 56, in run_sync
    return await get_async_backend().run_sync_in_worker_thread(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\anyio\_backends\_asyncio.py", line 2461, in run_sync_in_worker_thread
    return await future
           ^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\anyio\_backends\_asyncio.py", line 962, in run
    result = context.run(func, *args)
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\auth\router.py", line 34, in register_user
    new_user = create_user(db=db, user=user)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 25, in create_user
    hashed_password = get_password_hash(user.password)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\auth\security.py", line 18, in get_password_hash
    return pwd_context.hash(password)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\context.py", line 2258, in hash
    return record.hash(secret, **kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\utils\handlers.py", line 779, in hash
    self.checksum = self._calc_checksum(secret)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\handlers\bcrypt.py", line 591, in _calc_checksum
    self._stub_requires_backend()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\utils\handlers.py", line 2254, in _stub_requires_backend
    cls.set_backend()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\utils\handlers.py", line 2156, in set_backend
    return owner.set_backend(name, dryrun=dryrun)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\utils\handlers.py", line 2176, in set_backend
    raise default_error
passlib.exc.MissingBackendError: bcrypt: no backends available -- recommend you install one (e.g. 'pip install bcrypt')
2025-05-26 19:56:12,953 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-26 19:56:13,195 - app.auth.router - INFO - User 'testuser' (student) registered successfully with ID: 1.
2025-05-26 19:56:15,247 - app.main - WARNING - Validation Error: [{'loc': ('body',), 'msg': 'Input should be a valid dictionary or object to extract fields from', 'type': 'model_attributes_type'}]
2025-05-26 19:56:28,435 - app.main - INFO - Database tables created successfully.
2025-05-26 19:56:28,435 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 19:56:28,435 - app.main - INFO - Minio bucket check completed.
2025-05-26 19:56:28,435 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 19:56:28,437 - app.main - INFO - AI model loaded successfully.
2025-05-26 19:56:28,437 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 19:56:28,437 - app.main - INFO - Milvus initialized successfully.
2025-05-26 19:56:28,437 - app.main - INFO - Application started successfully.
2025-05-26 19:56:39,942 - app.auth.router - WARNING - Registration failed: Username 'testuser' already registered.
2025-05-26 19:56:39,942 - app.main - ERROR - Custom HTTP Exception occurred: Username already registered, Code: USERNAME_EXISTS, Status: 400, Data: None
2025-05-26 19:56:53,648 - app.main - INFO - Database tables created successfully.
2025-05-26 19:56:53,648 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 19:56:53,650 - app.main - INFO - Minio bucket check completed.
2025-05-26 19:56:53,650 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 19:56:53,651 - app.main - INFO - AI model loaded successfully.
2025-05-26 19:56:53,651 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 19:56:53,653 - app.main - INFO - Milvus initialized successfully.
2025-05-26 19:56:53,654 - app.main - INFO - Application started successfully.
2025-05-26 19:57:03,624 - app.auth.router - WARNING - Registration failed: Username 'testuser' already registered.
2025-05-26 19:57:03,625 - app.main - ERROR - Custom HTTP Exception occurred: Username already registered, Code: USERNAME_EXISTS, Status: 400, Data: None
2025-05-26 19:57:05,646 - app.main - WARNING - Validation Error: [{'loc': ('body',), 'msg': 'Input should be a valid dictionary or object to extract fields from', 'type': 'model_attributes_type'}]
2025-05-26 19:57:43,010 - app.main - INFO - Database tables created successfully.
2025-05-26 19:57:43,010 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 19:57:43,011 - app.main - INFO - Minio bucket check completed.
2025-05-26 19:57:43,011 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 19:57:43,012 - app.main - INFO - AI model loaded successfully.
2025-05-26 19:57:43,012 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 19:57:43,012 - app.main - INFO - Milvus initialized successfully.
2025-05-26 19:57:43,012 - app.main - INFO - Application started successfully.
2025-05-26 19:57:57,997 - app.auth.router - WARNING - Registration failed: Username 'testuser' already registered.
2025-05-26 19:57:57,999 - app.main - ERROR - Custom HTTP Exception occurred: Username already registered, Code: USERNAME_EXISTS, Status: 400, Data: None
2025-05-26 19:58:00,044 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-26 19:58:00,286 - app.auth.router - INFO - User 'testuser' logged in successfully.
2025-05-26 19:58:02,312 - app.main - INFO - Protected test endpoint accessed by testuser
2025-05-26 20:07:33,995 - app.main - INFO - Database tables created successfully.
2025-05-26 20:07:33,996 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:07:33,996 - app.main - INFO - Minio bucket check completed.
2025-05-26 20:07:33,996 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:07:33,997 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:07:33,997 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:07:33,998 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:07:33,998 - app.main - INFO - Application started successfully.
2025-05-26 20:10:46,170 - app.main - INFO - Database tables created successfully.
2025-05-26 20:10:46,170 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:10:46,170 - app.main - INFO - Minio bucket check completed.
2025-05-26 20:10:46,170 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:10:46,171 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:10:46,171 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:10:46,171 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:10:46,172 - app.main - INFO - Application started successfully.
2025-05-26 20:10:46,176 - app.main - INFO - Database tables created successfully.
2025-05-26 20:10:46,176 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:10:46,176 - app.main - INFO - Minio bucket check completed.
2025-05-26 20:10:46,177 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:10:46,177 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:10:46,177 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:10:46,177 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:10:46,177 - app.main - INFO - Application started successfully.
2025-05-26 20:11:03,788 - app.main - INFO - Database tables created successfully.
2025-05-26 20:11:03,788 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:11:03,789 - app.main - INFO - Minio bucket check completed.
2025-05-26 20:11:03,790 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:11:03,790 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:11:03,791 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:11:03,791 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:11:03,792 - app.main - INFO - Application started successfully.
2025-05-26 20:11:03,800 - app.main - INFO - Database tables created successfully.
2025-05-26 20:11:03,801 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:11:03,801 - app.main - INFO - Minio bucket check completed.
2025-05-26 20:11:03,801 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:11:03,802 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:11:03,802 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:11:03,802 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:11:03,803 - app.main - INFO - Application started successfully.
2025-05-26 20:11:39,707 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-26 20:11:40,080 - app.auth.router - INFO - User 'testuser' logged in successfully.
2025-05-26 20:11:42,116 - app.main - INFO - Protected test endpoint accessed by testuser
2025-05-26 20:12:47,540 - app.main - INFO - Database tables created successfully.
2025-05-26 20:12:47,541 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:12:47,541 - app.main - INFO - Minio bucket check completed.
2025-05-26 20:12:47,541 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:12:47,541 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:12:47,541 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:12:47,542 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:12:47,542 - app.main - INFO - Application started successfully.
2025-05-26 20:12:47,777 - app.main - INFO - Database tables created successfully.
2025-05-26 20:12:47,778 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:12:47,778 - app.main - INFO - Minio bucket check completed.
2025-05-26 20:12:47,779 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:12:47,779 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:12:47,779 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:12:47,780 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:12:47,780 - app.main - INFO - Application started successfully.
2025-05-26 20:12:59,236 - app.main - INFO - Database tables created successfully.
2025-05-26 20:12:59,237 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:12:59,237 - app.main - INFO - Minio bucket check completed.
2025-05-26 20:12:59,237 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:12:59,238 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:12:59,238 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:12:59,239 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:12:59,239 - app.main - INFO - Application started successfully.
2025-05-26 20:12:59,468 - app.main - INFO - Database tables created successfully.
2025-05-26 20:12:59,468 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:12:59,469 - app.main - INFO - Minio bucket check completed.
2025-05-26 20:12:59,469 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:12:59,469 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:12:59,469 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:12:59,470 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:12:59,470 - app.main - INFO - Application started successfully.
2025-05-26 20:13:46,906 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-26 20:13:47,132 - app.auth.router - INFO - User 'testuser' logged in successfully.
2025-05-26 20:13:49,161 - app.main - INFO - Protected test endpoint accessed by testuser
2025-05-26 20:15:16,629 - app.auth.router - INFO - User 'testuser' logged in successfully.
2025-05-26 20:15:16,638 - app.api.v1.routers.users - INFO - User 1 (testuser) accessed /users/me.
2025-05-26 20:15:16,692 - app.main - ERROR - Unhandled Internal Server Error: Don't know how to join to <Mapper at 0x20f88d08c90; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\api\v1\routers\students.py", line 81, in get_my_dashboard_summary
    recent_notifications_data = get_notifications_for_user(db, student_id)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 655, in get_notifications_for_user
    ClassStudent.student_id == student_id).distinct().all()
                                                      ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2704, in all
    return self._iter().all()  # type: ignore
           ^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1629, in _execute_clauseelement
    compiled_sql, extracted_params, cache_hit = elem._compile_w_cache(
                                                ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 711, in _compile_w_cache
    compiled_sql = self._compiler(
                   ^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 320, in _compiler
    return dialect.statement_compiler(dialect, self, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 1446, in __init__
    Compiled.__init__(self, dialect, statement, **kwargs)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 886, in __init__
    self.string = self.process(self.statement, **compile_kwargs)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 932, in process
    return obj._compiler_dispatch(self, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\visitors.py", line 141, in _compiler_dispatch
    return meth(self, **kw)  # type: ignore  # noqa: E501
           ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 4728, in visit_select
    compile_state = select_stmt._compile_state_factory(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\base.py", line 687, in create_for_statement
    return klass.create_for_statement(statement, compiler, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 447, in create_for_statement
    return cls._create_orm_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1246, in _create_orm_context
    self._setup_for_generate()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1281, in _setup_for_generate
    self._join(query._setup_joins, self._entities)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1922, in _join
    self._join_left_to_right(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1957, in _join_left_to_right
    ) = self._join_determine_implicit_left_side(
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 2121, in _join_determine_implicit_left_side
    raise sa_exc.InvalidRequestError(
sqlalchemy.exc.InvalidRequestError: Don't know how to join to <Mapper at 0x20f88d08c90; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
2025-05-26 20:15:16,728 - app.main - ERROR - Unhandled Internal Server Error: Don't know how to join to <Mapper at 0x20f88d08c90; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\api\v1\routers\students.py", line 81, in get_my_dashboard_summary
    recent_notifications_data = get_notifications_for_user(db, student_id)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 655, in get_notifications_for_user
    ClassStudent.student_id == student_id).distinct().all()
                                                      ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2704, in all
    return self._iter().all()  # type: ignore
           ^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1629, in _execute_clauseelement
    compiled_sql, extracted_params, cache_hit = elem._compile_w_cache(
                                                ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 711, in _compile_w_cache
    compiled_sql = self._compiler(
                   ^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 320, in _compiler
    return dialect.statement_compiler(dialect, self, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 1446, in __init__
    Compiled.__init__(self, dialect, statement, **kwargs)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 886, in __init__
    self.string = self.process(self.statement, **compile_kwargs)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 932, in process
    return obj._compiler_dispatch(self, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\visitors.py", line 141, in _compiler_dispatch
    return meth(self, **kw)  # type: ignore  # noqa: E501
           ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 4728, in visit_select
    compile_state = select_stmt._compile_state_factory(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\base.py", line 687, in create_for_statement
    return klass.create_for_statement(statement, compiler, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 447, in create_for_statement
    return cls._create_orm_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1246, in _create_orm_context
    self._setup_for_generate()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1281, in _setup_for_generate
    self._join(query._setup_joins, self._entities)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1922, in _join
    self._join_left_to_right(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1957, in _join_left_to_right
    ) = self._join_determine_implicit_left_side(
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 2121, in _join_determine_implicit_left_side
    raise sa_exc.InvalidRequestError(
sqlalchemy.exc.InvalidRequestError: Don't know how to join to <Mapper at 0x20f88d08c90; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
2025-05-26 20:15:17,000 - app.api.v1.routers.notifications - INFO - Attempting to connect WebSocket for user 1 (testuser).
2025-05-26 20:15:17,000 - app.utils.websocket_manager - INFO - User 1 connected via WebSocket
2025-05-26 20:15:17,001 - app.api.v1.routers.notifications - INFO - WebSocket connection established for user 1.
2025-05-26 20:15:17,048 - app.main - ERROR - Unhandled Internal Server Error: Don't know how to join to <Mapper at 0x20f88d08c90; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\api\v1\routers\students.py", line 81, in get_my_dashboard_summary
    recent_notifications_data = get_notifications_for_user(db, student_id)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 655, in get_notifications_for_user
    ClassStudent.student_id == student_id).distinct().all()
                                                      ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2704, in all
    return self._iter().all()  # type: ignore
           ^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1629, in _execute_clauseelement
    compiled_sql, extracted_params, cache_hit = elem._compile_w_cache(
                                                ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 711, in _compile_w_cache
    compiled_sql = self._compiler(
                   ^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 320, in _compiler
    return dialect.statement_compiler(dialect, self, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 1446, in __init__
    Compiled.__init__(self, dialect, statement, **kwargs)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 886, in __init__
    self.string = self.process(self.statement, **compile_kwargs)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 932, in process
    return obj._compiler_dispatch(self, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\visitors.py", line 141, in _compiler_dispatch
    return meth(self, **kw)  # type: ignore  # noqa: E501
           ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 4728, in visit_select
    compile_state = select_stmt._compile_state_factory(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\base.py", line 687, in create_for_statement
    return klass.create_for_statement(statement, compiler, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 447, in create_for_statement
    return cls._create_orm_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1246, in _create_orm_context
    self._setup_for_generate()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1281, in _setup_for_generate
    self._join(query._setup_joins, self._entities)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1922, in _join
    self._join_left_to_right(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1957, in _join_left_to_right
    ) = self._join_determine_implicit_left_side(
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 2121, in _join_determine_implicit_left_side
    raise sa_exc.InvalidRequestError(
sqlalchemy.exc.InvalidRequestError: Don't know how to join to <Mapper at 0x20f88d08c90; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
2025-05-26 20:15:28,891 - app.utils.websocket_manager - INFO - User 1 disconnected from WebSocket
2025-05-26 20:15:28,891 - app.api.v1.routers.notifications - INFO - WebSocket disconnected for user 1.
2025-05-26 20:15:32,561 - app.main - ERROR - Unhandled Internal Server Error: Don't know how to join to <Mapper at 0x20f88d08c90; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\api\v1\routers\students.py", line 81, in get_my_dashboard_summary
    recent_notifications_data = get_notifications_for_user(db, student_id)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 655, in get_notifications_for_user
    ClassStudent.student_id == student_id).distinct().all()
                                                      ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2704, in all
    return self._iter().all()  # type: ignore
           ^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1629, in _execute_clauseelement
    compiled_sql, extracted_params, cache_hit = elem._compile_w_cache(
                                                ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 711, in _compile_w_cache
    compiled_sql = self._compiler(
                   ^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 320, in _compiler
    return dialect.statement_compiler(dialect, self, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 1446, in __init__
    Compiled.__init__(self, dialect, statement, **kwargs)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 886, in __init__
    self.string = self.process(self.statement, **compile_kwargs)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 932, in process
    return obj._compiler_dispatch(self, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\visitors.py", line 141, in _compiler_dispatch
    return meth(self, **kw)  # type: ignore  # noqa: E501
           ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 4728, in visit_select
    compile_state = select_stmt._compile_state_factory(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\base.py", line 687, in create_for_statement
    return klass.create_for_statement(statement, compiler, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 447, in create_for_statement
    return cls._create_orm_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1246, in _create_orm_context
    self._setup_for_generate()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1281, in _setup_for_generate
    self._join(query._setup_joins, self._entities)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1922, in _join
    self._join_left_to_right(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1957, in _join_left_to_right
    ) = self._join_determine_implicit_left_side(
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 2121, in _join_determine_implicit_left_side
    raise sa_exc.InvalidRequestError(
sqlalchemy.exc.InvalidRequestError: Don't know how to join to <Mapper at 0x20f88d08c90; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
2025-05-26 20:15:32,865 - app.api.v1.routers.notifications - INFO - Attempting to connect WebSocket for user 1 (testuser).
2025-05-26 20:15:32,866 - app.utils.websocket_manager - INFO - User 1 connected via WebSocket
2025-05-26 20:15:32,866 - app.api.v1.routers.notifications - INFO - WebSocket connection established for user 1.
2025-05-26 20:15:32,882 - app.main - ERROR - Unhandled Internal Server Error: Don't know how to join to <Mapper at 0x20f88d08c90; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\api\v1\routers\students.py", line 81, in get_my_dashboard_summary
    recent_notifications_data = get_notifications_for_user(db, student_id)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 655, in get_notifications_for_user
    ClassStudent.student_id == student_id).distinct().all()
                                                      ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2704, in all
    return self._iter().all()  # type: ignore
           ^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1629, in _execute_clauseelement
    compiled_sql, extracted_params, cache_hit = elem._compile_w_cache(
                                                ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 711, in _compile_w_cache
    compiled_sql = self._compiler(
                   ^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 320, in _compiler
    return dialect.statement_compiler(dialect, self, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 1446, in __init__
    Compiled.__init__(self, dialect, statement, **kwargs)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 886, in __init__
    self.string = self.process(self.statement, **compile_kwargs)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 932, in process
    return obj._compiler_dispatch(self, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\visitors.py", line 141, in _compiler_dispatch
    return meth(self, **kw)  # type: ignore  # noqa: E501
           ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 4728, in visit_select
    compile_state = select_stmt._compile_state_factory(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\base.py", line 687, in create_for_statement
    return klass.create_for_statement(statement, compiler, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 447, in create_for_statement
    return cls._create_orm_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1246, in _create_orm_context
    self._setup_for_generate()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1281, in _setup_for_generate
    self._join(query._setup_joins, self._entities)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1922, in _join
    self._join_left_to_right(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1957, in _join_left_to_right
    ) = self._join_determine_implicit_left_side(
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 2121, in _join_determine_implicit_left_side
    raise sa_exc.InvalidRequestError(
sqlalchemy.exc.InvalidRequestError: Don't know how to join to <Mapper at 0x20f88d08c90; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
2025-05-26 20:15:32,897 - app.main - ERROR - Unhandled Internal Server Error: Don't know how to join to <Mapper at 0x20f88d08c90; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\api\v1\routers\students.py", line 81, in get_my_dashboard_summary
    recent_notifications_data = get_notifications_for_user(db, student_id)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 655, in get_notifications_for_user
    ClassStudent.student_id == student_id).distinct().all()
                                                      ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2704, in all
    return self._iter().all()  # type: ignore
           ^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1629, in _execute_clauseelement
    compiled_sql, extracted_params, cache_hit = elem._compile_w_cache(
                                                ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 711, in _compile_w_cache
    compiled_sql = self._compiler(
                   ^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 320, in _compiler
    return dialect.statement_compiler(dialect, self, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 1446, in __init__
    Compiled.__init__(self, dialect, statement, **kwargs)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 886, in __init__
    self.string = self.process(self.statement, **compile_kwargs)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 932, in process
    return obj._compiler_dispatch(self, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\visitors.py", line 141, in _compiler_dispatch
    return meth(self, **kw)  # type: ignore  # noqa: E501
           ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 4728, in visit_select
    compile_state = select_stmt._compile_state_factory(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\base.py", line 687, in create_for_statement
    return klass.create_for_statement(statement, compiler, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 447, in create_for_statement
    return cls._create_orm_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1246, in _create_orm_context
    self._setup_for_generate()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1281, in _setup_for_generate
    self._join(query._setup_joins, self._entities)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1922, in _join
    self._join_left_to_right(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1957, in _join_left_to_right
    ) = self._join_determine_implicit_left_side(
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 2121, in _join_determine_implicit_left_side
    raise sa_exc.InvalidRequestError(
sqlalchemy.exc.InvalidRequestError: Don't know how to join to <Mapper at 0x20f88d08c90; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
2025-05-26 20:17:50,681 - app.auth.router - INFO - User 'teacher001' (student) registered successfully with ID: 2.
2025-05-26 20:17:53,052 - app.auth.router - INFO - User 'teacher001' logged in successfully.
2025-05-26 20:17:55,074 - app.main - INFO - Protected test endpoint accessed by teacher001
2025-05-26 20:17:57,100 - app.auth.dependencies - WARNING - User teacher001 (role: student) tried to access teacher-only endpoint.
2025-05-26 20:17:59,141 - app.auth.dependencies - WARNING - User teacher001 (role: student) tried to access teacher-only endpoint.
2025-05-26 20:18:01,193 - app.auth.dependencies - WARNING - User teacher001 (role: student) tried to access teacher-only endpoint.
2025-05-26 20:18:03,231 - app.auth.dependencies - WARNING - User teacher001 (role: student) tried to access teacher-only endpoint.
2025-05-26 20:18:29,107 - app.utils.websocket_manager - INFO - User 1 disconnected from WebSocket
2025-05-26 20:18:29,109 - app.api.v1.routers.notifications - INFO - WebSocket disconnected for user 1.
2025-05-26 20:18:30,622 - app.main - INFO - Database tables created successfully.
2025-05-26 20:18:30,622 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:18:30,623 - app.main - INFO - Minio bucket check completed.
2025-05-26 20:18:30,623 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:18:30,623 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:18:30,624 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:18:30,624 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:18:30,624 - app.main - INFO - Application started successfully.
2025-05-26 20:18:30,688 - app.main - INFO - Database tables created successfully.
2025-05-26 20:18:30,688 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:18:30,689 - app.main - INFO - Minio bucket check completed.
2025-05-26 20:18:30,689 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:18:30,689 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:18:30,691 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:18:30,692 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:18:30,692 - app.main - INFO - Application started successfully.
2025-05-26 20:19:17,761 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-26 20:19:18,173 - app.auth.router - INFO - User 'teacher_zhang' (teacher) registered successfully with ID: 3.
2025-05-26 20:19:20,566 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 20:19:22,602 - app.main - INFO - Protected test endpoint accessed by teacher_zhang
2025-05-26 20:19:28,732 - app.api.v1.routers.courses - INFO - Course 'Python��̻���' (ID: 1) created by teacher 3.
2025-05-26 20:19:30,783 - app.api.v1.routers.classes - INFO - Class 'Python��̰༶A' (ID: 1) created by teacher 3.
2025-05-26 20:19:32,855 - app.api.v1.routers.quizzes - INFO - Quiz 'Python��������' (ID: 1) created by teacher 3 for course 1.
2025-05-26 20:23:26,568 - app.api.v1.routers.users - INFO - User 1 (testuser) accessed /users/me.
2025-05-26 20:23:26,576 - app.api.v1.routers.users - INFO - User 1 (testuser) accessed /users/me.
2025-05-26 20:23:26,597 - app.main - ERROR - Unhandled Internal Server Error: Don't know how to join to <Mapper at 0x208e57c0fd0; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\api\v1\routers\students.py", line 81, in get_my_dashboard_summary
    recent_notifications_data = get_notifications_for_user(db, student_id)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 655, in get_notifications_for_user
    ClassStudent.student_id == student_id).distinct().all()
                                                      ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2704, in all
    return self._iter().all()  # type: ignore
           ^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1629, in _execute_clauseelement
    compiled_sql, extracted_params, cache_hit = elem._compile_w_cache(
                                                ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 711, in _compile_w_cache
    compiled_sql = self._compiler(
                   ^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 320, in _compiler
    return dialect.statement_compiler(dialect, self, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 1446, in __init__
    Compiled.__init__(self, dialect, statement, **kwargs)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 886, in __init__
    self.string = self.process(self.statement, **compile_kwargs)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 932, in process
    return obj._compiler_dispatch(self, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\visitors.py", line 141, in _compiler_dispatch
    return meth(self, **kw)  # type: ignore  # noqa: E501
           ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 4728, in visit_select
    compile_state = select_stmt._compile_state_factory(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\base.py", line 687, in create_for_statement
    return klass.create_for_statement(statement, compiler, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 447, in create_for_statement
    return cls._create_orm_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1246, in _create_orm_context
    self._setup_for_generate()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1281, in _setup_for_generate
    self._join(query._setup_joins, self._entities)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1922, in _join
    self._join_left_to_right(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1957, in _join_left_to_right
    ) = self._join_determine_implicit_left_side(
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 2121, in _join_determine_implicit_left_side
    raise sa_exc.InvalidRequestError(
sqlalchemy.exc.InvalidRequestError: Don't know how to join to <Mapper at 0x208e57c0fd0; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
2025-05-26 20:23:26,837 - app.main - ERROR - Unhandled Internal Server Error: Don't know how to join to <Mapper at 0x208e57c0fd0; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\api\v1\routers\students.py", line 81, in get_my_dashboard_summary
    recent_notifications_data = get_notifications_for_user(db, student_id)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 655, in get_notifications_for_user
    ClassStudent.student_id == student_id).distinct().all()
                                                      ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2704, in all
    return self._iter().all()  # type: ignore
           ^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1629, in _execute_clauseelement
    compiled_sql, extracted_params, cache_hit = elem._compile_w_cache(
                                                ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 711, in _compile_w_cache
    compiled_sql = self._compiler(
                   ^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 320, in _compiler
    return dialect.statement_compiler(dialect, self, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 1446, in __init__
    Compiled.__init__(self, dialect, statement, **kwargs)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 886, in __init__
    self.string = self.process(self.statement, **compile_kwargs)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 932, in process
    return obj._compiler_dispatch(self, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\visitors.py", line 141, in _compiler_dispatch
    return meth(self, **kw)  # type: ignore  # noqa: E501
           ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 4728, in visit_select
    compile_state = select_stmt._compile_state_factory(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\base.py", line 687, in create_for_statement
    return klass.create_for_statement(statement, compiler, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 447, in create_for_statement
    return cls._create_orm_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1246, in _create_orm_context
    self._setup_for_generate()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1281, in _setup_for_generate
    self._join(query._setup_joins, self._entities)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1922, in _join
    self._join_left_to_right(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1957, in _join_left_to_right
    ) = self._join_determine_implicit_left_side(
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 2121, in _join_determine_implicit_left_side
    raise sa_exc.InvalidRequestError(
sqlalchemy.exc.InvalidRequestError: Don't know how to join to <Mapper at 0x208e57c0fd0; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
2025-05-26 20:23:26,882 - app.main - ERROR - Unhandled Internal Server Error: Don't know how to join to <Mapper at 0x208e57c0fd0; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\api\v1\routers\students.py", line 81, in get_my_dashboard_summary
    recent_notifications_data = get_notifications_for_user(db, student_id)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 655, in get_notifications_for_user
    ClassStudent.student_id == student_id).distinct().all()
                                                      ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2704, in all
    return self._iter().all()  # type: ignore
           ^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1629, in _execute_clauseelement
    compiled_sql, extracted_params, cache_hit = elem._compile_w_cache(
                                                ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 711, in _compile_w_cache
    compiled_sql = self._compiler(
                   ^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 320, in _compiler
    return dialect.statement_compiler(dialect, self, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 1446, in __init__
    Compiled.__init__(self, dialect, statement, **kwargs)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 886, in __init__
    self.string = self.process(self.statement, **compile_kwargs)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 932, in process
    return obj._compiler_dispatch(self, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\visitors.py", line 141, in _compiler_dispatch
    return meth(self, **kw)  # type: ignore  # noqa: E501
           ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\compiler.py", line 4728, in visit_select
    compile_state = select_stmt._compile_state_factory(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\base.py", line 687, in create_for_statement
    return klass.create_for_statement(statement, compiler, **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 447, in create_for_statement
    return cls._create_orm_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1246, in _create_orm_context
    self._setup_for_generate()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1281, in _setup_for_generate
    self._join(query._setup_joins, self._entities)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1922, in _join
    self._join_left_to_right(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 1957, in _join_left_to_right
    ) = self._join_determine_implicit_left_side(
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 2121, in _join_determine_implicit_left_side
    raise sa_exc.InvalidRequestError(
sqlalchemy.exc.InvalidRequestError: Don't know how to join to <Mapper at 0x208e57c0fd0; Class>. Please use the .select_from() method to establish an explicit left side, as well as providing an explicit ON clause if not present already to help resolve the ambiguity.
2025-05-26 20:23:26,897 - app.api.v1.routers.notifications - INFO - Attempting to connect WebSocket for user 1 (testuser).
2025-05-26 20:23:26,897 - app.utils.websocket_manager - INFO - User 1 connected via WebSocket
2025-05-26 20:23:26,898 - app.api.v1.routers.notifications - INFO - WebSocket connection established for user 1.
2025-05-26 20:23:28,520 - app.utils.websocket_manager - INFO - User 1 disconnected from WebSocket
2025-05-26 20:23:28,522 - app.api.v1.routers.notifications - INFO - WebSocket disconnected for user 1.
2025-05-26 20:23:49,988 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 20:23:49,993 - app.api.v1.routers.users - INFO - User 3 (teacher_zhang) accessed /users/me.
2025-05-26 20:25:35,593 - app.main - WARNING - Validation Error: [{'loc': ('query', 'course_id'), 'msg': 'Field required', 'type': 'missing'}, {'loc': ('query', 'original_filename'), 'msg': 'Field required', 'type': 'missing'}, {'loc': ('query', 'file_type'), 'msg': 'Field required', 'type': 'missing'}]
2025-05-26 20:25:46,924 - app.main - WARNING - Validation Error: [{'loc': ('query', 'course_id'), 'msg': 'Field required', 'type': 'missing'}, {'loc': ('query', 'original_filename'), 'msg': 'Field required', 'type': 'missing'}, {'loc': ('query', 'file_type'), 'msg': 'Field required', 'type': 'missing'}]
2025-05-26 20:32:00,793 - app.main - INFO - Database tables created successfully.
2025-05-26 20:32:00,793 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:32:00,795 - app.main - INFO - Minio bucket check completed.
2025-05-26 20:32:00,795 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:32:00,795 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:32:00,795 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:32:00,795 - app.main - INFO - Database tables created successfully.
2025-05-26 20:32:00,795 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:32:00,795 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:32:00,795 - app.main - INFO - Minio bucket check completed.
y.
2025-05-26 20:32:00,797 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:32:00,797 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:32:00,797 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:32:00,797 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:32:00,797 - app.main - INFO - Application started successfully.
2025-05-26 20:35:55,007 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-26 20:35:55,329 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 20:35:57,378 - app.api.v1.routers.courses - INFO - Course 'DeepSeek AI���Կγ�' (ID: 2) created by teacher 3.
2025-05-26 20:35:59,413 - app.main - WARNING - Validation Error: [{'loc': ('query', 'course_id'), 'msg': 'Field required', 'type': 'missing'}, {'loc': ('query', 'original_filename'), 'msg': 'Field required', 'type': 'missing'}, {'loc': ('query', 'file_type'), 'msg': 'Field required', 'type': 'missing'}]
2025-05-26 20:37:01,587 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 20:37:03,621 - app.api.v1.routers.courses - INFO - Course 'DeepSeek AI���Կγ�' (ID: 3) created by teacher 3.
2025-05-26 20:37:05,656 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/3/text/7b1a2866-21a5-47c0-b82c-3b7df9352fb6.txt
2025-05-26 20:37:05,657 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 3, object courses/3/text/7b1a2866-21a5-47c0-b82c-3b7df9352fb6.txt.
2025-05-26 20:37:07,706 - app.utils.minio_client - INFO - Mock Minio: File courses/3/text/7b1a2866-21a5-47c0-b82c-3b7df9352fb6.txt exists check
2025-05-26 20:37:07,712 - app.api.v1.routers.materials - INFO - Material 'Python��̳̽�' (ID: 1) created successfully for course 3.
2025-05-26 20:37:07,714 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/3/text/7b1a2866-21a5-47c0-b82c-3b7df9352fb6.txt
2025-05-26 20:37:12,745 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/3/text/7b1a2866-21a5-47c0-b82c-3b7df9352fb6.txt
2025-05-26 20:37:19,711 - app.api.v1.routers.materials - ERROR - Error vectorizing material 1: insert_vectors_into_milvus() takes 1 positional argument but 3 were given
2025-05-26 20:37:19,712 - app.api.v1.routers.materials - INFO - Material 1 processed successfully with DeepSeek analysis.
2025-05-26 20:37:24,881 - app.api.v1.routers.materials - INFO - Material 1 analyzed with DeepSeek by user 3.
2025-05-26 20:40:19,480 - app.api.v1.routers.users - INFO - User 3 (teacher_zhang) accessed /users/me.
2025-05-26 20:40:19,490 - app.api.v1.routers.users - INFO - User 3 (teacher_zhang) accessed /users/me.
2025-05-26 20:40:33,146 - app.main - ERROR - Unhandled Internal Server Error: (sqlite3.IntegrityError) NOT NULL constraint failed: materials.course_id
[SQL: UPDATE materials SET course_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: (None, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
Traceback (most recent call last):
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlite3.IntegrityError: NOT NULL constraint failed: materials.course_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\api\v1\routers\courses.py", line 129, in delete_course_by_id
    deleted_course = delete_course(db, course_id)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\app\crud.py", line 171, in delete_course
    db.commit()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2032, in commit
    trans.commit(_to_root=True)
  File "<string>", line 2, in commit
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1313, in commit
    self._prepare_impl()
  File "<string>", line 2, in _prepare_impl
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1288, in _prepare_impl
    self.session.flush()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4345, in flush
    self._flush(objects)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4480, in _flush
    with util.safe_reraise():
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4441, in _flush
    flush_context.execute()
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\unitofwork.py", line 466, in execute
    rec.execute(self)
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\unitofwork.py", line 642, in execute
    util.preloaded.orm_persistence.save_obj(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\persistence.py", line 85, in save_obj
    _emit_update_statements(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\orm\persistence.py", line 912, in _emit_update_statements
    c = connection.execute(
        ^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "D:\PycharmProjects\autoapi\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.IntegrityError: (sqlite3.IntegrityError) NOT NULL constraint failed: materials.course_id
[SQL: UPDATE materials SET course_id=?, updated_at=CURRENT_TIMESTAMP WHERE materials.id = ?]
[parameters: (None, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-26 20:40:38,200 - app.main - WARNING - Validation Error: [{'loc': ('query', 'course_id'), 'msg': 'Field required', 'type': 'missing'}, {'loc': ('query', 'original_filename'), 'msg': 'Field required', 'type': 'missing'}, {'loc': ('query', 'file_type'), 'msg': 'Field required', 'type': 'missing'}]
2025-05-26 20:41:34,028 - app.main - WARNING - Validation Error: [{'loc': ('query', 'course_id'), 'msg': 'Field required', 'type': 'missing'}, {'loc': ('query', 'original_filename'), 'msg': 'Field required', 'type': 'missing'}, {'loc': ('query', 'file_type'), 'msg': 'Field required', 'type': 'missing'}]
2025-05-26 20:44:48,406 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 20:44:50,817 - app.auth.router - INFO - User 'testuser' logged in successfully.
2025-05-26 20:44:52,886 - app.api.v1.routers.courses - INFO - Course '�ļ��ϴ����Կγ�' (ID: 4) created by teacher 3.
2025-05-26 20:44:54,931 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/4/text/a860a791-bf03-4df0-b975-c70b7948e820.txt
2025-05-26 20:44:54,932 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 4, object courses/4/text/a860a791-bf03-4df0-b975-c70b7948e820.txt.
2025-05-26 20:44:57,005 - app.utils.minio_client - INFO - Mock Minio: File courses/4/text/a860a791-bf03-4df0-b975-c70b7948e820.txt exists check
2025-05-26 20:44:57,019 - app.api.v1.routers.materials - INFO - Material '�����ĵ�' (ID: 2) created successfully for course 4.
2025-05-26 20:44:57,020 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/4/text/a860a791-bf03-4df0-b975-c70b7948e820.txt
2025-05-26 20:45:01,078 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/4/text/a860a791-bf03-4df0-b975-c70b7948e820.txt
2025-05-26 20:45:08,628 - app.api.v1.routers.materials - ERROR - Error vectorizing material 2: insert_vectors_into_milvus() takes 1 positional argument but 3 were given
2025-05-26 20:45:08,630 - app.api.v1.routers.materials - INFO - Material 2 processed successfully with DeepSeek analysis.
2025-05-26 20:45:12,419 - app.api.v1.routers.materials - INFO - Material 2 analyzed with DeepSeek by user 3.
2025-05-26 20:45:14,507 - app.api.v1.routers.courses - INFO - Course 'Python�������' (ID: 5) created by teacher 3.
2025-05-26 20:45:16,560 - app.api.v1.routers.courses - INFO - Course 'Web����ʵս' (ID: 6) created by teacher 3.
2025-05-26 20:45:18,606 - app.api.v1.routers.courses - INFO - Course '���ݿ�ѧ����' (ID: 7) created by teacher 3.
2025-05-26 20:45:20,658 - app.api.v1.routers.classes - INFO - Class 'Python������' (ID: 2) created by teacher 3.
2025-05-26 20:45:22,712 - app.api.v1.routers.classes - INFO - Class 'Web�������װ�' (ID: 3) created by teacher 3.
2025-05-26 20:47:42,116 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 20:47:44,354 - app.auth.router - INFO - User 'testuser' logged in successfully.
2025-05-26 20:47:50,535 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/1/text/1cd7ef94-fa7a-46e0-b10d-a542bbdbace9.txt
2025-05-26 20:47:50,535 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 1, object courses/1/text/1cd7ef94-fa7a-46e0-b10d-a542bbdbace9.txt.
2025-05-26 20:52:56,421 - app.main - INFO - Database tables created successfully.
2025-05-26 20:52:56,421 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:52:56,421 - app.main - INFO - Minio bucket check completed.
2025-05-26 20:52:56,422 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:52:56,422 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:52:56,422 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:52:56,422 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:52:56,423 - app.main - INFO - Application started successfully.
2025-05-26 20:53:47,389 - app.api.v1.routers.users - INFO - User 3 (teacher_zhang) accessed /users/me.
2025-05-26 20:53:47,393 - app.api.v1.routers.users - INFO - User 3 (teacher_zhang) accessed /users/me.
2025-05-26 20:53:56,091 - app.auth.security - WARNING - JWT decoding failed: Signature has expired.
2025-05-26 20:53:56,092 - app.auth.dependencies - WARNING - JWT decode failed or payload is empty for HTTP request.
2025-05-26 20:53:59,144 - app.auth.router - WARNING - Login failed: Incorrect credentials for username '<EMAIL>'.
2025-05-26 20:53:59,146 - app.main - ERROR - Custom HTTP Exception occurred: Incorrect username or password, Code: INVALID_CREDENTIALS, Status: 401, Data: None
2025-05-26 20:54:42,165 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 20:54:42,171 - app.api.v1.routers.users - INFO - User 3 (teacher_zhang) accessed /users/me.
2025-05-26 20:54:58,642 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/6/pdf/23743fee-32ff-4b11-bb5b-98dd25d58be9.pdf
2025-05-26 20:54:58,643 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 6, object courses/6/pdf/23743fee-32ff-4b11-bb5b-98dd25d58be9.pdf.
2025-05-26 20:56:47,935 - app.main - INFO - Database tables created successfully.
2025-05-26 20:56:47,935 - app.utils.minio_client - INFO - Mock Minio: Bucket exists check passed.
2025-05-26 20:56:47,936 - app.main - INFO - Minio bucket check completed.
2025-05-26 20:56:47,937 - app.utils.milvus_client_utils - INFO - Mock AI: Loading mock sentence transformer model
2025-05-26 20:56:47,937 - app.main - INFO - AI model loaded successfully.
2025-05-26 20:56:47,937 - app.utils.milvus_client_utils - INFO - Mock Milvus: Initialized mock connection
2025-05-26 20:56:47,938 - app.main - INFO - Milvus initialized successfully.
2025-05-26 20:56:47,938 - app.main - INFO - Application started successfully.
2025-05-26 20:58:16,924 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 20:58:20,976 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/1/text/14ceef3e-26b0-4baf-ae68-19be6ccf469c.txt
2025-05-26 20:58:20,976 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 1, object courses/1/text/14ceef3e-26b0-4baf-ae68-19be6ccf469c.txt.
2025-05-26 20:58:23,005 - app.utils.minio_client - INFO - Mock Minio: File courses/1/text/14ceef3e-26b0-4baf-ae68-19be6ccf469c.txt exists check
2025-05-26 20:58:23,011 - app.api.v1.routers.materials - INFO - Material '�����ĵ�' (ID: 3) created successfully for course 1.
2025-05-26 20:58:23,013 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/1/text/14ceef3e-26b0-4baf-ae68-19be6ccf469c.txt
2025-05-26 20:58:25,259 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 20:58:27,288 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/1/text/cc1954d5-74da-4429-ae54-39a9b57e3d2d.txt
2025-05-26 20:58:27,288 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 1, object courses/1/text/cc1954d5-74da-4429-ae54-39a9b57e3d2d.txt.
2025-05-26 20:58:29,315 - app.main - WARNING - Validation Error: [{'loc': ('query', 'course_id'), 'msg': 'Field required', 'type': 'missing'}, {'loc': ('query', 'original_filename'), 'msg': 'Field required', 'type': 'missing'}, {'loc': ('query', 'file_type'), 'msg': 'Field required', 'type': 'missing'}]
2025-05-26 20:58:33,486 - app.api.v1.routers.materials - ERROR - Error vectorizing material 3: insert_vectors_into_milvus() takes 1 positional argument but 3 were given
2025-05-26 20:58:33,486 - app.api.v1.routers.materials - INFO - Material 3 processed successfully with DeepSeek analysis.
2025-05-26 21:02:32,348 - app.auth.router - INFO - User 'teacher_zhang' logged in successfully.
2025-05-26 21:02:36,404 - app.utils.minio_client - INFO - Mock Minio: Generated upload URL for courses/1/text/c6bf37a2-ccb2-458c-9c26-1174c5e182ac.txt
2025-05-26 21:02:36,405 - app.api.v1.routers.materials - INFO - Generated presigned upload URL for course 1, object courses/1/text/c6bf37a2-ccb2-458c-9c26-1174c5e182ac.txt.
2025-05-26 21:02:38,445 - app.utils.minio_client - INFO - Mock Minio: File courses/1/text/c6bf37a2-ccb2-458c-9c26-1174c5e182ac.txt exists check
2025-05-26 21:02:38,449 - app.api.v1.routers.materials - INFO - Material '�����ĵ�' (ID: 4) created successfully for course 1.
2025-05-26 21:02:38,451 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/1/text/c6bf37a2-ccb2-458c-9c26-1174c5e182ac.txt
2025-05-26 21:02:42,477 - app.utils.minio_client - INFO - Mock Minio: Getting content for courses/1/text/c6bf37a2-ccb2-458c-9c26-1174c5e182ac.txt
2025-05-26 21:02:49,439 - app.api.v1.routers.materials - ERROR - Error vectorizing material 4: insert_vectors_into_milvus() takes 1 positional argument but 3 were given
2025-05-26 21:02:49,440 - app.api.v1.routers.materials - INFO - Material 4 processed successfully with DeepSeek analysis.
2025-05-26 21:02:56,025 - app.api.v1.routers.materials - INFO - Material 4 analyzed with DeepSeek by user 3.
