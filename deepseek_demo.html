<!DOCTYPE html>
<html>
<head>
    <title>DeepSeek AI 文档分析演示 - 教育平台</title>
    <style>
        body { 
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif; 
            margin: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 10px;
        }
        .section {
            margin: 30px 0;
            padding: 25px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            background: #f8f9fa;
        }
        .form-group { 
            margin: 20px 0; 
        }
        label { 
            display: block; 
            margin-bottom: 8px; 
            font-weight: bold;
            color: #333;
        }
        input, textarea, select { 
            padding: 12px; 
            width: 100%; 
            border: 2px solid #ddd;
            border-radius: 8px;
            box-sizing: border-box;
            font-size: 16px;
        }
        input:focus, textarea:focus, select:focus {
            border-color: #667eea;
            outline: none;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        button { 
            padding: 15px 30px; 
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white; 
            border: none; 
            cursor: pointer;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            margin: 10px 5px 10px 0;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .result { 
            margin: 20px 0; 
            padding: 20px; 
            border-radius: 8px;
            border: 2px solid #ddd; 
        }
        .success { 
            background: #d4edda; 
            border-color: #c3e6cb; 
            color: #155724;
        }
        .error { 
            background: #f8d7da; 
            border-color: #f5c6cb; 
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .analysis-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .analysis-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        .tag {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin: 2px;
        }
        .tag-beginner { background: #d4edda; color: #155724; }
        .tag-intermediate { background: #fff3cd; color: #856404; }
        .tag-advanced { background: #f8d7da; color: #721c24; }
        .tag-topic { background: #e2e3e5; color: #383d41; }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            transition: width 0.3s ease;
        }
        .file-upload-area {
            border: 3px dashed #667eea;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            background: #f8f9ff;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .file-upload-area:hover {
            background: #f0f0ff;
            border-color: #764ba2;
        }
        .file-upload-area.dragover {
            background: #e6e6ff;
            border-color: #5a67d8;
        }
        h1, h2, h3 { color: #333; }
        .demo-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
            text-align: center;
        }
        .feature-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 DeepSeek AI 文档分析演示</h1>
            <p>智能教学文档分析与内容提取系统</p>
        </div>

        <!-- 功能介绍 -->
        <div class="demo-features">
            <div class="feature-card">
                <div class="feature-icon">📄</div>
                <h3>多格式支持</h3>
                <p>支持PDF、Word、文本、图片等多种文档格式</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">🧠</div>
                <h3>智能分析</h3>
                <p>使用DeepSeek AI提取关键知识点和学习目标</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">📊</div>
                <h3>难度评估</h3>
                <p>自动评估文档难度级别和适用人群</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">❓</div>
                <h3>问题生成</h3>
                <p>基于内容自动生成测试问题和学习建议</p>
            </div>
        </div>

        <!-- 登录区域 -->
        <div class="section">
            <h2>🔐 教师登录</h2>
            <form id="loginForm">
                <div class="form-group">
                    <label>用户名:</label>
                    <input type="text" id="username" value="teacher_zhang" required>
                </div>
                <div class="form-group">
                    <label>密码:</label>
                    <input type="password" id="password" value="teacher123456" required>
                </div>
                <button type="submit">🚀 登录</button>
            </form>
            <div id="loginResult"></div>
        </div>

        <!-- 文件上传和分析区域 -->
        <div id="uploadSection" class="section" style="display: none;">
            <h2>📤 文档上传与AI分析</h2>
            
            <!-- 课程选择 -->
            <div class="form-group">
                <label>选择课程:</label>
                <select id="courseSelect" required>
                    <option value="">请选择课程...</option>
                </select>
            </div>

            <!-- 文件上传区域 -->
            <div class="file-upload-area" id="fileUploadArea">
                <div style="font-size: 48px; margin-bottom: 20px;">📁</div>
                <h3>拖拽文件到此处或点击选择文件</h3>
                <p>支持 PDF, Word, 文本文件, 图片等格式</p>
                <input type="file" id="fileInput" style="display: none;" accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.bmp">
            </div>

            <!-- 文件信息 -->
            <div id="fileInfo" style="display: none;">
                <h3>📋 文件信息</h3>
                <div id="fileDetails"></div>
                <div class="form-group">
                    <label>文档标题:</label>
                    <input type="text" id="materialTitle" placeholder="请输入文档标题" required>
                </div>
                <div class="form-group">
                    <label>文档描述:</label>
                    <textarea id="materialDescription" rows="3" placeholder="请输入文档描述（可选）"></textarea>
                </div>
                <button onclick="uploadAndAnalyze()" id="uploadBtn">🚀 上传并分析</button>
            </div>

            <!-- 上传进度 -->
            <div id="uploadProgress" style="display: none;">
                <h3>📤 上传进度</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                </div>
                <p id="progressText">准备上传...</p>
            </div>

            <div id="uploadResult"></div>
        </div>

        <!-- 分析结果展示区域 -->
        <div id="analysisSection" class="section" style="display: none;">
            <h2>🤖 DeepSeek AI 分析结果</h2>
            <div id="analysisResults"></div>
        </div>

        <!-- 已上传文档列表 -->
        <div id="materialsSection" class="section" style="display: none;">
            <h2>📚 已上传文档</h2>
            <button onclick="refreshMaterials()">🔄 刷新列表</button>
            <div id="materialsList"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000/api/v1';
        let authToken = null;
        let selectedFile = null;
        let courses = [];

        // 登录功能
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            await loginTeacher();
        });

        // 文件上传区域事件
        const fileUploadArea = document.getElementById('fileUploadArea');
        const fileInput = document.getElementById('fileInput');

        fileUploadArea.addEventListener('click', () => fileInput.click());
        fileUploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            fileUploadArea.classList.add('dragover');
        });
        fileUploadArea.addEventListener('dragleave', () => {
            fileUploadArea.classList.remove('dragover');
        });
        fileUploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            fileUploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect(files[0]);
            }
        });
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileSelect(e.target.files[0]);
            }
        });

        async function loginTeacher() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('loginResult');

            try {
                resultDiv.innerHTML = '<div class="info"><p>🔄 登录中...</p></div>';

                const response = await fetch(`${API_BASE_URL}/auth/token`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (response.ok) {
                    authToken = data.access_token;
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ 登录成功！</h3>
                            <p>欢迎，${username}！</p>
                        </div>
                    `;
                    
                    // 显示上传区域并加载课程
                    document.getElementById('uploadSection').style.display = 'block';
                    document.getElementById('materialsSection').style.display = 'block';
                    await loadCourses();
                    await refreshMaterials();
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ 登录失败</h3>
                            <p>${data.detail || '未知错误'}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ 网络错误</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function loadCourses() {
            try {
                const response = await fetch(`${API_BASE_URL}/courses`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                if (response.ok) {
                    courses = await response.json();
                    const courseSelect = document.getElementById('courseSelect');
                    courseSelect.innerHTML = '<option value="">请选择课程...</option>';
                    
                    courses.forEach(course => {
                        const option = document.createElement('option');
                        option.value = course.id;
                        option.textContent = course.title;
                        courseSelect.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('加载课程失败:', error);
            }
        }

        function handleFileSelect(file) {
            selectedFile = file;
            
            // 显示文件信息
            document.getElementById('fileInfo').style.display = 'block';
            document.getElementById('fileDetails').innerHTML = `
                <div class="analysis-card">
                    <p><strong>📄 文件名:</strong> ${file.name}</p>
                    <p><strong>📊 文件大小:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                    <p><strong>🏷️ 文件类型:</strong> ${file.type || '未知'}</p>
                </div>
            `;
            
            // 自动填充标题
            const fileName = file.name.split('.')[0];
            document.getElementById('materialTitle').value = fileName;
        }

        async function uploadAndAnalyze() {
            if (!selectedFile || !document.getElementById('courseSelect').value) {
                alert('请选择课程和文件！');
                return;
            }

            const courseId = parseInt(document.getElementById('courseSelect').value);
            const title = document.getElementById('materialTitle').value;
            const description = document.getElementById('materialDescription').value;

            try {
                // 显示进度
                document.getElementById('uploadProgress').style.display = 'block';
                updateProgress(10, '获取上传凭证...');

                // 获取上传凭证
                const fileType = getFileType(selectedFile.name);
                const response = await fetch(`${API_BASE_URL}/materials/upload-credentials?course_id=${courseId}&original_filename=${encodeURIComponent(selectedFile.name)}&file_type=${fileType}`, {
                    method: 'POST',
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                if (!response.ok) {
                    throw new Error('获取上传凭证失败');
                }

                const uploadInfo = await response.json();
                updateProgress(30, '上传文件到存储...');

                // 创建材料记录
                updateProgress(60, '创建文档记录...');
                const materialData = {
                    title: title,
                    description: description,
                    file_type: fileType,
                    file_path_minio: uploadInfo.object_name,
                    file_size_bytes: selectedFile.size
                };

                const materialResponse = await fetch(`${API_BASE_URL}/materials/${courseId}`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(materialData)
                });

                if (!materialResponse.ok) {
                    throw new Error('创建文档记录失败');
                }

                const material = await materialResponse.json();
                updateProgress(80, '启动AI分析...');

                // 等待一下后台处理
                await new Promise(resolve => setTimeout(resolve, 2000));

                // 分析文档
                const analysisResponse = await fetch(`${API_BASE_URL}/materials/${material.id}/analyze`, {
                    method: 'POST',
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                updateProgress(100, '分析完成！');

                if (analysisResponse.ok) {
                    const analysisResult = await analysisResponse.json();
                    displayAnalysisResult(analysisResult, material);
                    await refreshMaterials();
                } else {
                    throw new Error('AI分析失败');
                }

                // 隐藏进度，重置表单
                setTimeout(() => {
                    document.getElementById('uploadProgress').style.display = 'none';
                    document.getElementById('fileInfo').style.display = 'none';
                    selectedFile = null;
                    document.getElementById('materialTitle').value = '';
                    document.getElementById('materialDescription').value = '';
                }, 2000);

            } catch (error) {
                document.getElementById('uploadResult').innerHTML = `
                    <div class="result error">
                        <h3>❌ 上传失败</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        function updateProgress(percent, text) {
            document.getElementById('progressFill').style.width = percent + '%';
            document.getElementById('progressText').textContent = text;
        }

        function getFileType(filename) {
            const ext = filename.toLowerCase().split('.').pop();
            const typeMap = {
                'pdf': 'pdf',
                'doc': 'word', 'docx': 'word',
                'txt': 'text',
                'jpg': 'image', 'jpeg': 'image', 'png': 'image', 'bmp': 'image',
                'ppt': 'ppt', 'pptx': 'ppt'
            };
            return typeMap[ext] || 'text';
        }

        function displayAnalysisResult(analysis, material) {
            document.getElementById('analysisSection').style.display = 'block';
            
            if (analysis.success) {
                document.getElementById('analysisResults').innerHTML = `
                    <div class="analysis-card">
                        <div class="analysis-header">
                            <h3>📄 ${material.title}</h3>
                            <span class="tag tag-${analysis.difficulty_level}">${getDifficultyText(analysis.difficulty_level)}</span>
                        </div>
                        
                        <div style="margin: 20px 0;">
                            <h4>📝 内容摘要</h4>
                            <p>${analysis.summary}</p>
                        </div>

                        <div style="margin: 20px 0;">
                            <h4>🎯 关键知识点</h4>
                            <ul>
                                ${analysis.key_points?.map(point => `<li>${point}</li>`).join('') || '<li>暂无</li>'}
                            </ul>
                        </div>

                        <div style="margin: 20px 0;">
                            <h4>📚 主要主题</h4>
                            <div>
                                ${analysis.topics?.map(topic => `<span class="tag tag-topic">${topic}</span>`).join('') || '暂无'}
                            </div>
                        </div>

                        ${analysis.suggested_questions?.length ? `
                        <div style="margin: 20px 0;">
                            <h4>❓ 建议测试问题</h4>
                            <ol>
                                ${analysis.suggested_questions.map(q => `<li>${q}</li>`).join('')}
                            </ol>
                        </div>
                        ` : ''}

                        ${analysis.learning_objectives?.length ? `
                        <div style="margin: 20px 0;">
                            <h4>🎯 学习目标</h4>
                            <ul>
                                ${analysis.learning_objectives.map(obj => `<li>${obj}</li>`).join('')}
                            </ul>
                        </div>
                        ` : ''}
                    </div>
                `;
            } else {
                document.getElementById('analysisResults').innerHTML = `
                    <div class="result error">
                        <h3>❌ 分析失败</h3>
                        <p>${analysis.error}</p>
                    </div>
                `;
            }
        }

        function getDifficultyText(level) {
            const map = {
                'beginner': '🟢 初级',
                'intermediate': '🟡 中级',
                'advanced': '🔴 高级'
            };
            return map[level] || '❓ 未知';
        }

        async function refreshMaterials() {
            try {
                const allMaterials = [];
                for (const course of courses) {
                    try {
                        const response = await fetch(`${API_BASE_URL}/materials/course/${course.id}`, {
                            headers: { 'Authorization': `Bearer ${authToken}` }
                        });
                        if (response.ok) {
                            const materials = await response.json();
                            allMaterials.push(...materials.map(m => ({ ...m, courseName: course.title })));
                        }
                    } catch (err) {
                        console.error(`获取课程 ${course.id} 的材料失败:`, err);
                    }
                }

                const materialsDiv = document.getElementById('materialsList');
                if (allMaterials.length === 0) {
                    materialsDiv.innerHTML = '<p>暂无上传的文档。</p>';
                } else {
                    materialsDiv.innerHTML = allMaterials.map(material => `
                        <div class="analysis-card">
                            <div class="analysis-header">
                                <h4>${getFileIcon(material.file_type)} ${material.title}</h4>
                                <button onclick="analyzeMaterial(${material.id})" style="padding: 8px 16px; font-size: 14px;">
                                    🤖 重新分析
                                </button>
                            </div>
                            <p><strong>课程:</strong> ${material.courseName}</p>
                            <p><strong>类型:</strong> ${material.file_type} | <strong>大小:</strong> ${(material.file_size_bytes / 1024 / 1024).toFixed(2)} MB</p>
                            <p><strong>上传时间:</strong> ${new Date(material.created_at).toLocaleString()}</p>
                            ${material.description ? `<p><strong>描述:</strong> ${material.description}</p>` : ''}
                        </div>
                    `).join('');
                }
            } catch (error) {
                console.error('刷新材料列表失败:', error);
            }
        }

        async function analyzeMaterial(materialId) {
            try {
                const response = await fetch(`${API_BASE_URL}/materials/${materialId}/analyze`, {
                    method: 'POST',
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                if (response.ok) {
                    const analysis = await response.json();
                    displayAnalysisResult(analysis, { title: `文档 ID: ${materialId}` });
                } else {
                    alert('分析失败');
                }
            } catch (error) {
                alert('分析错误: ' + error.message);
            }
        }

        function getFileIcon(fileType) {
            const icons = {
                'pdf': '📄',
                'word': '📝',
                'text': '📃',
                'image': '🖼️',
                'ppt': '📊'
            };
            return icons[fileType] || '📄';
        }
    </script>
</body>
</html>
