<!DOCTYPE html>
<html>
<head>
    <title>文件上传测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { padding: 8px; width: 100%; box-sizing: border-box; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .result { margin: 15px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .step { margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 文件上传功能测试</h1>
        
        <!-- 登录区域 -->
        <div class="step">
            <h3>步骤 1: 教师登录</h3>
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" id="username" value="teacher_zhang">
            </div>
            <div class="form-group">
                <label>密码:</label>
                <input type="password" id="password" value="teacher123456">
            </div>
            <button onclick="login()">登录</button>
            <div id="loginResult"></div>
        </div>

        <!-- 课程选择 -->
        <div class="step">
            <h3>步骤 2: 选择课程</h3>
            <div class="form-group">
                <label>课程:</label>
                <select id="courseSelect">
                    <option value="">请先登录...</option>
                </select>
            </div>
            <button onclick="loadCourses()" disabled id="loadCoursesBtn">加载课程</button>
            <div id="coursesResult"></div>
        </div>

        <!-- 文件上传 -->
        <div class="step">
            <h3>步骤 3: 文件上传</h3>
            <div class="form-group">
                <label>选择文件:</label>
                <input type="file" id="fileInput">
            </div>
            <div class="form-group">
                <label>文件标题:</label>
                <input type="text" id="fileTitle" placeholder="自动从文件名生成">
            </div>
            <div class="form-group">
                <label>文件描述:</label>
                <textarea id="fileDescription" rows="3" placeholder="可选"></textarea>
            </div>
            <button onclick="uploadFile()" disabled id="uploadBtn">上传文件</button>
            <div id="uploadResult"></div>
        </div>

        <!-- 测试结果 -->
        <div class="step">
            <h3>测试结果</h3>
            <div id="testResults"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000/api/v1';
        let authToken = null;
        let selectedCourse = null;

        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('loginResult');

            try {
                resultDiv.innerHTML = '<div class="info">登录中...</div>';

                const response = await fetch(`${API_BASE_URL}/auth/token`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (response.ok) {
                    authToken = data.access_token;
                    resultDiv.innerHTML = '<div class="success">✅ 登录成功！</div>';
                    document.getElementById('loadCoursesBtn').disabled = false;
                    await loadCourses();
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 登录失败: ${data.detail}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }

        async function loadCourses() {
            const resultDiv = document.getElementById('coursesResult');
            const courseSelect = document.getElementById('courseSelect');

            try {
                resultDiv.innerHTML = '<div class="info">加载课程中...</div>';

                const response = await fetch(`${API_BASE_URL}/courses/`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                if (response.ok) {
                    const courses = await response.json();
                    courseSelect.innerHTML = '<option value="">请选择课程...</option>';
                    
                    courses.forEach(course => {
                        const option = document.createElement('option');
                        option.value = course.id;
                        option.textContent = course.title;
                        courseSelect.appendChild(option);
                    });

                    resultDiv.innerHTML = `<div class="success">✅ 加载了 ${courses.length} 门课程</div>`;
                    
                    courseSelect.onchange = function() {
                        selectedCourse = this.value ? courses.find(c => c.id == this.value) : null;
                        document.getElementById('uploadBtn').disabled = !selectedCourse;
                    };
                } else {
                    const error = await response.json();
                    resultDiv.innerHTML = `<div class="error">❌ 加载课程失败: ${error.detail}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }

        async function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const fileTitle = document.getElementById('fileTitle');
            const fileDescription = document.getElementById('fileDescription');
            const resultDiv = document.getElementById('uploadResult');

            if (!fileInput.files[0] || !selectedCourse) {
                resultDiv.innerHTML = '<div class="error">❌ 请选择文件和课程</div>';
                return;
            }

            const file = fileInput.files[0];
            const title = fileTitle.value || file.name.split('.')[0];
            const description = fileDescription.value || `上传的文件: ${file.name}`;

            try {
                resultDiv.innerHTML = '<div class="info">🔄 开始上传流程...</div>';

                // 步骤1: 获取上传凭证
                const fileType = getFileType(file.name);
                console.log('文件类型:', fileType);
                
                resultDiv.innerHTML = '<div class="info">🔄 获取上传凭证...</div>';
                
                const credentialsResponse = await fetch(`${API_BASE_URL}/materials/upload-credentials?course_id=${selectedCourse.id}&original_filename=${encodeURIComponent(file.name)}&file_type=${fileType}`, {
                    method: 'POST',
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                if (!credentialsResponse.ok) {
                    const error = await credentialsResponse.json();
                    throw new Error(`获取上传凭证失败: ${error.detail}`);
                }

                const uploadInfo = await credentialsResponse.json();
                console.log('上传凭证:', uploadInfo);

                // 步骤2: 创建材料记录
                resultDiv.innerHTML = '<div class="info">🔄 创建材料记录...</div>';
                
                const materialData = {
                    title: title,
                    description: description,
                    file_type: fileType,
                    file_path_minio: uploadInfo.object_name,
                    file_size_bytes: file.size
                };

                const materialResponse = await fetch(`${API_BASE_URL}/materials/${selectedCourse.id}`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(materialData)
                });

                if (!materialResponse.ok) {
                    const error = await materialResponse.json();
                    throw new Error(`创建材料记录失败: ${error.detail}`);
                }

                const material = await materialResponse.json();
                console.log('材料记录:', material);

                resultDiv.innerHTML = `
                    <div class="success">
                        ✅ 文件上传成功！<br>
                        📄 材料ID: ${material.id}<br>
                        📝 标题: ${material.title}<br>
                        📊 大小: ${(material.file_size_bytes / 1024 / 1024).toFixed(2)} MB<br>
                        🏷️ 类型: ${material.file_type}
                    </div>
                `;

                // 更新测试结果
                updateTestResults('success', '文件上传功能正常工作！');

            } catch (error) {
                console.error('上传错误:', error);
                resultDiv.innerHTML = `<div class="error">❌ 上传失败: ${error.message}</div>`;
                updateTestResults('error', `文件上传失败: ${error.message}`);
            }
        }

        function getFileType(filename) {
            const ext = filename.toLowerCase().split('.').pop();
            const typeMap = {
                'pdf': 'pdf',
                'doc': 'word', 'docx': 'word',
                'txt': 'text',
                'jpg': 'image', 'jpeg': 'image', 'png': 'image', 'bmp': 'image',
                'ppt': 'ppt', 'pptx': 'ppt',
                'mp4': 'video', 'avi': 'video', 'mov': 'video',
                'mp3': 'audio', 'wav': 'audio'
            };
            return typeMap[ext] || 'text';
        }

        function updateTestResults(type, message) {
            const resultsDiv = document.getElementById('testResults');
            const className = type === 'success' ? 'success' : 'error';
            const icon = type === 'success' ? '✅' : '❌';
            
            resultsDiv.innerHTML = `<div class="${className}">${icon} ${message}</div>`;
        }

        // 文件选择时自动填充标题
        document.getElementById('fileInput').addEventListener('change', function(e) {
            if (e.target.files[0]) {
                const fileName = e.target.files[0].name;
                document.getElementById('fileTitle').value = fileName.split('.')[0];
            }
        });
    </script>
</body>
</html>
