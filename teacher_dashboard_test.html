<!DOCTYPE html>
<html>
<head>
    <title>Teacher Dashboard Test - Education Platform</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .form-group { 
            margin: 15px 0; 
        }
        label { 
            display: block; 
            margin-bottom: 5px; 
            font-weight: bold;
        }
        input, textarea, select { 
            padding: 10px; 
            width: 100%; 
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button { 
            padding: 12px 24px; 
            background: #007bff; 
            color: white; 
            border: none; 
            cursor: pointer;
            border-radius: 4px;
            font-size: 16px;
            margin: 10px 5px 10px 0;
        }
        button:hover {
            background: #0056b3;
        }
        button.danger {
            background: #dc3545;
        }
        button.danger:hover {
            background: #c82333;
        }
        .result { 
            margin: 20px 0; 
            padding: 15px; 
            border-radius: 4px;
            border: 1px solid #ddd; 
        }
        .success { 
            background: #d4edda; 
            border-color: #c3e6cb; 
            color: #155724;
        }
        .error { 
            background: #f8d7da; 
            border-color: #f5c6cb; 
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        h2 {
            color: #666;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        .resource-list {
            list-style: none;
            padding: 0;
        }
        .resource-item {
            background: white;
            margin: 10px 0;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .login-section {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>👨‍🏫 Teacher Dashboard Test</h1>
        
        <!-- Login Section -->
        <div class="login-section">
            <h2>🔐 Teacher Login</h2>
            <form id="loginForm">
                <div class="form-group">
                    <label>Username:</label>
                    <input type="text" id="username" value="teacher_zhang" required>
                </div>
                <div class="form-group">
                    <label>Password:</label>
                    <input type="password" id="password" value="teacher123456" required>
                </div>
                <button type="submit">Login as Teacher</button>
            </form>
            <div id="loginResult"></div>
        </div>

        <!-- Teacher Functions (Hidden until login) -->
        <div id="teacherFunctions" style="display: none;">
            
            <!-- Course Management -->
            <div class="section">
                <h2>📚 Course Management</h2>
                <form id="createCourseForm">
                    <div class="form-group">
                        <label>Course Title:</label>
                        <input type="text" id="courseTitle" placeholder="e.g., Advanced JavaScript" required>
                    </div>
                    <div class="form-group">
                        <label>Course Description:</label>
                        <textarea id="courseDescription" rows="3" placeholder="Course description..."></textarea>
                    </div>
                    <div class="form-group">
                        <label>Status:</label>
                        <select id="courseStatus">
                            <option value="active">Active</option>
                            <option value="draft">Draft</option>
                            <option value="archived">Archived</option>
                        </select>
                    </div>
                    <button type="submit">Create Course</button>
                    <button type="button" onclick="loadCourses()">Refresh Courses</button>
                </form>
                <div id="courseResult"></div>
                <ul id="coursesList" class="resource-list"></ul>
            </div>

            <!-- Class Management -->
            <div class="section">
                <h2>🏫 Class Management</h2>
                <form id="createClassForm">
                    <div class="form-group">
                        <label>Class Name:</label>
                        <input type="text" id="className" placeholder="e.g., JavaScript班级B" required>
                    </div>
                    <div class="form-group">
                        <label>Class Description:</label>
                        <textarea id="classDescription" rows="3" placeholder="Class description..."></textarea>
                    </div>
                    <div class="form-group">
                        <label>Status:</label>
                        <select id="classStatus">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                    <button type="submit">Create Class</button>
                    <button type="button" onclick="loadClasses()">Refresh Classes</button>
                </form>
                <div id="classResult"></div>
                <ul id="classesList" class="resource-list"></ul>
            </div>

            <!-- Quiz Management -->
            <div class="section">
                <h2>📝 Quiz Management</h2>
                <form id="createQuizForm">
                    <div class="form-group">
                        <label>Quiz Title:</label>
                        <input type="text" id="quizTitle" placeholder="e.g., JavaScript基础测试" required>
                    </div>
                    <div class="form-group">
                        <label>Quiz Description:</label>
                        <textarea id="quizDescription" rows="3" placeholder="Quiz description..."></textarea>
                    </div>
                    <div class="form-group">
                        <label>Course:</label>
                        <select id="quizCourse" required>
                            <option value="">Select a course...</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Quiz Type:</label>
                        <select id="quizType">
                            <option value="quiz">Quiz</option>
                            <option value="assignment">Assignment</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Time Limit (minutes):</label>
                        <input type="number" id="timeLimit" value="30" min="1">
                    </div>
                    <div class="form-group">
                        <label>Max Attempts:</label>
                        <input type="number" id="maxAttempts" value="3" min="1">
                    </div>
                    <button type="submit">Create Quiz</button>
                    <button type="button" onclick="loadQuizzes()">Refresh Quizzes</button>
                </form>
                <div id="quizResult"></div>
                <ul id="quizzesList" class="resource-list"></ul>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000/api/v1';
        let authToken = null;

        // Login functionality
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            await loginTeacher();
        });

        async function loginTeacher() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('loginResult');

            try {
                resultDiv.innerHTML = '<div class="info"><p>Logging in...</p></div>';

                const response = await fetch(`${API_BASE_URL}/auth/token`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (response.ok) {
                    authToken = data.access_token;
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ Teacher Login Successful!</h3>
                            <p>Welcome, ${username}!</p>
                        </div>
                    `;
                    
                    // Show teacher functions
                    document.getElementById('teacherFunctions').style.display = 'block';
                    
                    // Load initial data
                    await loadCourses();
                    await loadClasses();
                    
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ Login Failed</h3>
                            <p><strong>Error:</strong> ${data.detail || 'Unknown error'}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ Network Error</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }

        // Course Management
        document.getElementById('createCourseForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            await createCourse();
        });

        async function createCourse() {
            const title = document.getElementById('courseTitle').value;
            const description = document.getElementById('courseDescription').value;
            const status = document.getElementById('courseStatus').value;
            const resultDiv = document.getElementById('courseResult');

            try {
                const response = await fetch(`${API_BASE_URL}/courses`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ title, description, status })
                });

                const data = await response.json();

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <p>✅ Course "${data.title}" created successfully! (ID: ${data.id})</p>
                        </div>
                    `;
                    document.getElementById('createCourseForm').reset();
                    await loadCourses();
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <p>❌ Course creation failed: ${data.detail}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <p>❌ Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function loadCourses() {
            try {
                const response = await fetch(`${API_BASE_URL}/courses`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const courses = await response.json();
                const coursesList = document.getElementById('coursesList');
                const quizCourseSelect = document.getElementById('quizCourse');

                coursesList.innerHTML = '';
                quizCourseSelect.innerHTML = '<option value="">Select a course...</option>';

                courses.forEach(course => {
                    // Add to courses list
                    const li = document.createElement('li');
                    li.className = 'resource-item';
                    li.innerHTML = `
                        <div>
                            <strong>${course.title}</strong> (${course.status})
                            <br><small>${course.description || 'No description'}</small>
                        </div>
                        <div>
                            <button onclick="deleteCourse(${course.id})" class="danger">Delete</button>
                        </div>
                    `;
                    coursesList.appendChild(li);

                    // Add to quiz course select
                    const option = document.createElement('option');
                    option.value = course.id;
                    option.textContent = course.title;
                    quizCourseSelect.appendChild(option);
                });

            } catch (error) {
                console.error('Error loading courses:', error);
            }
        }

        async function deleteCourse(courseId) {
            if (!confirm('Are you sure you want to delete this course?')) return;

            try {
                const response = await fetch(`${API_BASE_URL}/courses/${courseId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    alert('Course deleted successfully!');
                    await loadCourses();
                } else {
                    const data = await response.json();
                    alert('Delete failed: ' + data.detail);
                }
            } catch (error) {
                alert('Error: ' + error.message);
            }
        }

        // Class Management
        document.getElementById('createClassForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            await createClass();
        });

        async function createClass() {
            const name = document.getElementById('className').value;
            const description = document.getElementById('classDescription').value;
            const status = document.getElementById('classStatus').value;
            const resultDiv = document.getElementById('classResult');

            try {
                const response = await fetch(`${API_BASE_URL}/classes`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ name, description, status })
                });

                const data = await response.json();

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <p>✅ Class "${data.name}" created successfully! (ID: ${data.id})</p>
                        </div>
                    `;
                    document.getElementById('createClassForm').reset();
                    await loadClasses();
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <p>❌ Class creation failed: ${data.detail}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <p>❌ Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function loadClasses() {
            try {
                const response = await fetch(`${API_BASE_URL}/classes`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const classes = await response.json();
                const classesList = document.getElementById('classesList');

                classesList.innerHTML = '';

                classes.forEach(cls => {
                    const li = document.createElement('li');
                    li.className = 'resource-item';
                    li.innerHTML = `
                        <div>
                            <strong>${cls.name}</strong> (${cls.status})
                            <br><small>${cls.description || 'No description'}</small>
                            <br><small>Students: ${cls.student_count || 0}</small>
                        </div>
                        <div>
                            <button onclick="deleteClass(${cls.id})" class="danger">Delete</button>
                        </div>
                    `;
                    classesList.appendChild(li);
                });

            } catch (error) {
                console.error('Error loading classes:', error);
            }
        }

        async function deleteClass(classId) {
            if (!confirm('Are you sure you want to delete this class?')) return;

            try {
                const response = await fetch(`${API_BASE_URL}/classes/${classId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    alert('Class deleted successfully!');
                    await loadClasses();
                } else {
                    const data = await response.json();
                    alert('Delete failed: ' + data.detail);
                }
            } catch (error) {
                alert('Error: ' + error.message);
            }
        }

        // Quiz Management
        document.getElementById('createQuizForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            await createQuiz();
        });

        async function createQuiz() {
            const title = document.getElementById('quizTitle').value;
            const description = document.getElementById('quizDescription').value;
            const course_id = parseInt(document.getElementById('quizCourse').value);
            const quiz_type = document.getElementById('quizType').value;
            const time_limit = parseInt(document.getElementById('timeLimit').value);
            const max_attempts = parseInt(document.getElementById('maxAttempts').value);
            const resultDiv = document.getElementById('quizResult');

            if (!course_id) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <p>❌ Please select a course for the quiz</p>
                    </div>
                `;
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/quizzes`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ 
                        title, 
                        description, 
                        course_id, 
                        quiz_type, 
                        time_limit, 
                        max_attempts,
                        is_published: true
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <p>✅ Quiz "${data.title}" created successfully! (ID: ${data.id})</p>
                        </div>
                    `;
                    document.getElementById('createQuizForm').reset();
                    await loadQuizzes();
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <p>❌ Quiz creation failed: ${data.detail}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <p>❌ Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function loadQuizzes() {
            try {
                const response = await fetch(`${API_BASE_URL}/quizzes`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const quizzes = await response.json();
                const quizzesList = document.getElementById('quizzesList');

                quizzesList.innerHTML = '';

                quizzes.forEach(quiz => {
                    const li = document.createElement('li');
                    li.className = 'resource-item';
                    li.innerHTML = `
                        <div>
                            <strong>${quiz.title}</strong> (${quiz.quiz_type})
                            <br><small>${quiz.description || 'No description'}</small>
                            <br><small>Time: ${quiz.time_limit}min | Attempts: ${quiz.max_attempts}</small>
                        </div>
                        <div>
                            <button onclick="deleteQuiz(${quiz.id})" class="danger">Delete</button>
                        </div>
                    `;
                    quizzesList.appendChild(li);
                });

            } catch (error) {
                console.error('Error loading quizzes:', error);
            }
        }

        async function deleteQuiz(quizId) {
            if (!confirm('Are you sure you want to delete this quiz?')) return;

            try {
                const response = await fetch(`${API_BASE_URL}/quizzes/${quizId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    alert('Quiz deleted successfully!');
                    await loadQuizzes();
                } else {
                    const data = await response.json();
                    alert('Delete failed: ' + data.detail);
                }
            } catch (error) {
                alert('Error: ' + error.message);
            }
        }
    </script>
</body>
</html>
