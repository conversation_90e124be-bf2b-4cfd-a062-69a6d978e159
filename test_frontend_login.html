<!DOCTYPE html>
<html>
<head>
    <title>Frontend Login Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin: 10px 0; }
        label { display: block; margin-bottom: 5px; }
        input { padding: 8px; width: 200px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; }
        .result { margin: 20px 0; padding: 10px; border: 1px solid #ddd; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
    </style>
</head>
<body>
    <h1>Frontend Login Test</h1>
    
    <form id="loginForm">
        <div class="form-group">
            <label>Username:</label>
            <input type="text" id="username" value="testuser" required>
        </div>
        <div class="form-group">
            <label>Password:</label>
            <input type="password" id="password" value="testpass123" required>
        </div>
        <button type="submit">Login</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        const API_BASE_URL = 'http://localhost:8000/api/v1';
        
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            try {
                resultDiv.innerHTML = '<p>Logging in...</p>';
                
                const response = await fetch(`${API_BASE_URL}/auth/token`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h3>✅ Login Successful!</h3>
                        <p><strong>Token:</strong> ${data.access_token.substring(0, 50)}...</p>
                        <p><strong>Token Type:</strong> ${data.token_type}</p>
                    `;
                    
                    // Test protected endpoint
                    testProtectedEndpoint(data.access_token);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h3>❌ Login Failed</h3>
                        <p><strong>Error:</strong> ${data.detail || 'Unknown error'}</p>
                        <p><strong>Status:</strong> ${response.status}</p>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>❌ Network Error</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                `;
            }
        });
        
        async function testProtectedEndpoint(token) {
            try {
                const response = await fetch(`${API_BASE_URL}/protected-test`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById('result').innerHTML += `
                        <h3>✅ Protected Endpoint Test Successful!</h3>
                        <p><strong>Message:</strong> ${data.message}</p>
                    `;
                } else {
                    document.getElementById('result').innerHTML += `
                        <h3>❌ Protected Endpoint Test Failed</h3>
                        <p><strong>Error:</strong> ${data.detail || 'Unknown error'}</p>
                    `;
                }
            } catch (error) {
                document.getElementById('result').innerHTML += `
                    <h3>❌ Protected Endpoint Network Error</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                `;
            }
        }
        
        // Test API connectivity on page load
        window.addEventListener('load', async () => {
            try {
                const response = await fetch('http://localhost:8000/');
                if (response.ok) {
                    console.log('✅ Backend API is accessible');
                } else {
                    console.log('❌ Backend API returned error:', response.status);
                }
            } catch (error) {
                console.log('❌ Backend API is not accessible:', error.message);
            }
        });
    </script>
</body>
</html>
