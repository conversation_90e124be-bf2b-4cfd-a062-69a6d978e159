import requests
import json
import time

# Test DeepSeek integration with material upload and analysis
BASE_URL = "http://localhost:8000/api/v1"

def login_teacher():
    """Login as teacher"""
    url = f"{BASE_URL}/auth/token"
    login_data = {
        "username": "teacher_zhang",
        "password": "teacher123456"
    }

    try:
        response = requests.post(url, json=login_data)
        if response.status_code == 200:
            result = response.json()
            print("✅ Teacher login successful!")
            return result["access_token"]
        else:
            print("❌ Teacher login failed")
            return None
    except Exception as e:
        print(f"Teacher Login Error: {e}")
        return None

def create_test_course(token):
    """Create a test course for material upload"""
    headers = {"Authorization": f"Bearer {token}"}
    course_data = {
        "title": "DeepSeek AI测试课程",
        "description": "用于测试DeepSeek AI文档分析功能的课程",
        "status": "active"
    }

    try:
        response = requests.post(f"{BASE_URL}/courses", json=course_data, headers=headers)
        if response.status_code == 201:
            result = response.json()
            print(f"✅ Test course created: {result['title']} (ID: {result['id']})")
            return result['id']
        else:
            print(f"❌ Course creation failed: {response.json()}")
            return None
    except Exception as e:
        print(f"Course creation error: {e}")
        return None

def upload_test_material(token, course_id):
    """Upload a test material"""
    headers = {"Authorization": f"Bearer {token}"}

    # Get upload credentials
    try:
        # Use query parameters instead of JSON body
        params = {
            "course_id": course_id,
            "original_filename": "test_document.txt",
            "file_type": "text"
        }
        response = requests.post(f"{BASE_URL}/materials/upload-credentials", params=params, headers=headers)

        if response.status_code != 200:
            print(f"❌ Failed to get upload credentials: {response.json()}")
            return None

        upload_info = response.json()
        print(f"✅ Got upload credentials for: {upload_info['object_name']}")

        # Create material record
        material_data = {
            "title": "Python编程教程",
            "description": "Python基础编程教程文档",
            "file_type": "text",
            "file_path_minio": upload_info['object_name'],
            "file_size_bytes": 1024
        }

        response = requests.post(f"{BASE_URL}/materials/{course_id}", json=material_data, headers=headers)

        if response.status_code == 201:
            result = response.json()
            print(f"✅ Material created: {result['title']} (ID: {result['id']})")
            return result['id']
        else:
            print(f"❌ Material creation failed: {response.json()}")
            return None

    except Exception as e:
        print(f"Material upload error: {e}")
        return None

def analyze_material_with_deepseek(token, material_id):
    """Analyze material using DeepSeek AI"""
    headers = {"Authorization": f"Bearer {token}"}

    try:
        print(f"🤖 Starting DeepSeek analysis for material {material_id}...")
        response = requests.post(f"{BASE_URL}/materials/{material_id}/analyze", headers=headers)

        if response.status_code == 200:
            result = response.json()
            print("✅ DeepSeek analysis completed!")

            if result.get("success"):
                print("\n📊 Analysis Results:")
                print(f"📝 Summary: {result.get('summary', 'N/A')}")
                print(f"🎯 Key Points: {', '.join(result.get('key_points', []))}")
                print(f"📚 Topics: {', '.join(result.get('topics', []))}")
                print(f"📊 Difficulty: {result.get('difficulty_level', 'N/A')}")

                if result.get('suggested_questions'):
                    print("❓ Suggested Questions:")
                    for i, question in enumerate(result['suggested_questions'], 1):
                        print(f"   {i}. {question}")

                if result.get('learning_objectives'):
                    print("🎯 Learning Objectives:")
                    for i, objective in enumerate(result['learning_objectives'], 1):
                        print(f"   {i}. {objective}")

                return result
            else:
                print(f"❌ Analysis failed: {result.get('error', 'Unknown error')}")
                return None
        else:
            print(f"❌ Analysis request failed: {response.json()}")
            return None

    except Exception as e:
        print(f"Analysis error: {e}")
        return None

def test_deepseek_client_directly():
    """Test DeepSeek client directly"""
    try:
        from app.utils.deepseek_client import deepseek_client

        print("\n🧪 Testing DeepSeek client directly...")

        # Test with sample text content
        sample_text = """
        Python编程基础教程

        第一章：Python简介
        Python是一种高级编程语言，具有简洁的语法和强大的功能。

        主要特点：
        1. 易于学习和使用
        2. 跨平台兼容性
        3. 丰富的标准库
        4. 面向对象编程支持

        第二章：基本语法
        变量定义：使用等号赋值
        数据类型：字符串、整数、浮点数、布尔值
        控制结构：if语句、for循环、while循环

        练习题：
        1. 编写一个Hello World程序
        2. 创建变量并打印其值
        3. 使用循环打印1到10的数字
        """

        analysis = deepseek_client.analyze_document(
            file_content=sample_text.encode('utf-8'),
            file_name="python_tutorial.txt",
            file_type="text"
        )

        if analysis.get("success"):
            print("✅ Direct DeepSeek analysis successful!")
            print(f"📝 Summary: {analysis.get('summary', 'N/A')}")
            print(f"🎯 Key Points: {', '.join(analysis.get('key_points', []))}")
            print(f"📚 Topics: {', '.join(analysis.get('topics', []))}")
            print(f"📊 Difficulty: {analysis.get('difficulty_level', 'N/A')}")
        else:
            print(f"❌ Direct analysis failed: {analysis.get('error', 'Unknown error')}")

        return analysis

    except Exception as e:
        print(f"Direct DeepSeek test error: {e}")
        return None

def main():
    print("=== DeepSeek AI Integration Test ===")

    # Test 1: Direct DeepSeek client test
    print("\n1. Testing DeepSeek client directly...")
    direct_result = test_deepseek_client_directly()

    # Test 2: Full integration test
    print("\n2. Testing full integration...")

    # Login
    token = login_teacher()
    if not token:
        print("Failed to login. Exiting integration test.")
        return

    # Create test course
    course_id = create_test_course(token)
    if not course_id:
        print("Failed to create course. Exiting integration test.")
        return

    # Upload test material
    material_id = upload_test_material(token, course_id)
    if not material_id:
        print("Failed to upload material. Exiting integration test.")
        return

    # Wait a moment for background processing
    print("⏳ Waiting for background processing...")
    time.sleep(3)

    # Analyze material with DeepSeek
    analysis_result = analyze_material_with_deepseek(token, material_id)

    print("\n🎉 DeepSeek Integration Test Completed!")

    if direct_result and direct_result.get("success"):
        print("✅ Direct DeepSeek client test: PASSED")
    else:
        print("❌ Direct DeepSeek client test: FAILED")

    if analysis_result and analysis_result.get("success"):
        print("✅ Full integration test: PASSED")
    else:
        print("❌ Full integration test: FAILED")

    print("\n📋 Test Summary:")
    print(f"Course ID: {course_id}")
    print(f"Material ID: {material_id}")
    print("Frontend URL: http://localhost:3000/teacher/courses")

if __name__ == "__main__":
    main()
