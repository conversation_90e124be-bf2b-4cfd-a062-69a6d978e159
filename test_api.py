import requests
import json

# Test API endpoints
BASE_URL = "http://localhost:8000"

def test_user_registration():
    """Test user registration"""
    url = f"{BASE_URL}/auth/register"
    data = {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "testpass123",
        "full_name": "Test User",
        "role": "student"
    }

    try:
        response = requests.post(url, json=data)
        print(f"Registration Status: {response.status_code}")
        print(f"Registration Response: {response.json()}")
        return response.status_code == 201
    except Exception as e:
        print(f"Registration Error: {e}")
        return False

def test_user_login():
    """Test user login"""
    url = f"{BASE_URL}/auth/token"
    # This API expects JSON data, not form data
    data = {
        "username": "testuser",
        "password": "testpass123"
    }

    try:
        response = requests.post(url, json=data)
        print(f"Login Status: {response.status_code}")
        result = response.json()
        print(f"Login Response: {result}")

        if response.status_code == 200 and "access_token" in result:
            return result["access_token"]
        return None
    except Exception as e:
        print(f"Login Error: {e}")
        return None

def test_protected_endpoint(token):
    """Test protected endpoint with token"""
    url = f"{BASE_URL}/api/v1/protected-test"
    headers = {"Authorization": f"Bearer {token}"}

    try:
        response = requests.get(url, headers=headers)
        print(f"Protected Endpoint Status: {response.status_code}")
        print(f"Protected Endpoint Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Protected Endpoint Error: {e}")
        return False

def main():
    print("=== API Connection Test ===")

    # Test basic connectivity
    try:
        response = requests.get(BASE_URL)
        print(f"✅ Backend accessible: {response.status_code}")
    except Exception as e:
        print(f"❌ Backend not accessible: {e}")
        return

    # Test user registration
    print("\n=== Testing User Registration ===")
    if test_user_registration():
        print("✅ User registration successful")
    else:
        print("⚠️ User registration failed (user may already exist)")
        # Continue with login test anyway

    # Test user login
    print("\n=== Testing User Login ===")
    token = test_user_login()
    if token:
        print("✅ User login successful")
    else:
        print("❌ User login failed")
        return

    # Test protected endpoint
    print("\n=== Testing Protected Endpoint ===")
    if test_protected_endpoint(token):
        print("✅ Protected endpoint access successful")
    else:
        print("❌ Protected endpoint access failed")

    print("\n🎉 All API tests completed!")

if __name__ == "__main__":
    main()
