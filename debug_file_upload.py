import requests
import json

# Debug file upload issue
BASE_URL = "http://localhost:8000/api/v1"

def test_teacher_login():
    """Test teacher login"""
    url = f"{BASE_URL}/auth/token"
    login_data = {
        "username": "teacher_zhang",
        "password": "teacher123456"
    }
    
    try:
        response = requests.post(url, json=login_data)
        if response.status_code == 200:
            result = response.json()
            print("✅ Teacher login successful!")
            return result["access_token"]
        else:
            print(f"❌ Teacher login failed: {response.json()}")
            return None
    except Exception as e:
        print(f"Teacher Login Error: {e}")
        return None

def test_get_courses(token):
    """Test getting courses"""
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(f"{BASE_URL}/courses/", headers=headers)
        if response.status_code == 200:
            courses = response.json()
            print(f"✅ Found {len(courses)} courses")
            return courses
        else:
            print(f"❌ Get courses failed: {response.json()}")
            return []
    except Exception as e:
        print(f"Get courses error: {e}")
        return []

def test_upload_credentials(token, course_id):
    """Test getting upload credentials"""
    headers = {"Authorization": f"Bearer {token}"}
    
    # Test the exact same call as frontend
    params = {
        "course_id": course_id,
        "original_filename": "test_document.txt",
        "file_type": "text"
    }
    
    try:
        print(f"🔍 Testing upload credentials with params: {params}")
        response = requests.post(f"{BASE_URL}/materials/upload-credentials", 
                               params=params, 
                               headers=headers)
        
        print(f"📊 Response status: {response.status_code}")
        print(f"📊 Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Upload credentials successful!")
            print(f"📄 Response: {json.dumps(result, indent=2)}")
            return result
        else:
            print(f"❌ Upload credentials failed: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"📄 Error details: {json.dumps(error_detail, indent=2)}")
            except:
                print(f"📄 Raw response: {response.text}")
            return None
    except Exception as e:
        print(f"Upload credentials error: {e}")
        return None

def test_create_material_record(token, course_id, object_name):
    """Test creating material record"""
    headers = {"Authorization": f"Bearer {token}"}
    
    material_data = {
        "title": "测试文档",
        "description": "用于测试的文档",
        "file_type": "text",
        "file_path_minio": object_name,
        "file_size_bytes": 1024
    }
    
    try:
        print(f"🔍 Testing material creation with data: {json.dumps(material_data, indent=2)}")
        response = requests.post(f"{BASE_URL}/materials/{course_id}", 
                               json=material_data, 
                               headers=headers)
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()
            print(f"✅ Material creation successful!")
            print(f"📄 Response: {json.dumps(result, indent=2)}")
            return result
        else:
            print(f"❌ Material creation failed: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"📄 Error details: {json.dumps(error_detail, indent=2)}")
            except:
                print(f"📄 Raw response: {response.text}")
            return None
    except Exception as e:
        print(f"Material creation error: {e}")
        return None

def test_frontend_api_call():
    """Test the exact API call that frontend makes"""
    print("\n🧪 Testing Frontend API Call Pattern")
    
    # Simulate the exact frontend call
    token = test_teacher_login()
    if not token:
        return
    
    courses = test_get_courses(token)
    if not courses:
        print("❌ No courses available for testing")
        return
    
    course_id = courses[0]['id']
    print(f"🎯 Using course ID: {course_id}")
    
    # Test upload credentials (this is where the error likely occurs)
    upload_info = test_upload_credentials(token, course_id)
    if not upload_info:
        print("❌ Upload credentials failed - this is the issue!")
        return
    
    # Test material record creation
    material = test_create_material_record(token, course_id, upload_info['object_name'])
    if material:
        print("✅ Complete upload flow successful!")
    else:
        print("❌ Material creation failed")

def test_api_endpoint_directly():
    """Test API endpoint directly with curl-like request"""
    print("\n🔧 Testing API Endpoint Directly")
    
    token = test_teacher_login()
    if not token:
        return
    
    # Test with different parameter formats
    headers = {"Authorization": f"Bearer {token}"}
    
    # Method 1: Query parameters (correct way)
    print("\n📋 Method 1: Query parameters")
    try:
        url = f"{BASE_URL}/materials/upload-credentials?course_id=1&original_filename=test.txt&file_type=text"
        response = requests.post(url, headers=headers)
        print(f"Status: {response.status_code}")
        if response.status_code != 200:
            print(f"Error: {response.text}")
        else:
            print("✅ Query parameters method works!")
    except Exception as e:
        print(f"Error: {e}")
    
    # Method 2: JSON body (wrong way - should fail)
    print("\n📋 Method 2: JSON body")
    try:
        response = requests.post(f"{BASE_URL}/materials/upload-credentials", 
                               json={
                                   "course_id": 1,
                                   "original_filename": "test.txt",
                                   "file_type": "text"
                               }, 
                               headers=headers)
        print(f"Status: {response.status_code}")
        if response.status_code != 200:
            print(f"Error: {response.text}")
        else:
            print("✅ JSON body method works!")
    except Exception as e:
        print(f"Error: {e}")

def main():
    print("🔍 Debugging File Upload Issue")
    print("=" * 50)
    
    # Test 1: Frontend API call pattern
    test_frontend_api_call()
    
    # Test 2: Direct API endpoint testing
    test_api_endpoint_directly()
    
    print("\n" + "=" * 50)
    print("🎯 Debug Summary:")
    print("1. Check if teacher login works")
    print("2. Check if courses are available")
    print("3. Check if upload credentials API works")
    print("4. Check if material creation works")
    print("5. Identify where the failure occurs")

if __name__ == "__main__":
    main()
