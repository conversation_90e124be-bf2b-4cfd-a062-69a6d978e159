import requests
import time

def test_live_debug():
    """Test live debug functionality"""
    print("🔍 Testing Live Debug - Making API Call Now")
    print("=" * 50)
    
    print("📞 Making login API call...")
    try:
        response = requests.post("http://localhost:8000/api/v1/auth/token", 
                               json={"username": "teacher_zhang", "password": "teacher123456"})
        print(f"✅ Response: {response.status_code}")
        
        if response.status_code == 200:
            print("🎯 Check backend terminal NOW for debug logs!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n📞 Making root page call...")
    try:
        response = requests.get("http://localhost:8000/")
        print(f"✅ Response: {response.status_code}")
        print("🎯 Check backend terminal NOW for debug logs!")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_live_debug()
