import sqlite3
import bcrypt
from app.database import SessionLocal
from app.models import User
from app.auth.security import verify_password, get_password_hash

def check_database_users():
    """Check users in the database"""
    print("🔍 Checking Database Users")
    print("=" * 40)

    # Method 1: Direct SQLite query
    try:
        conn = sqlite3.connect('education_platform.db')
        cursor = conn.cursor()

        cursor.execute("SELECT id, username, email, role, is_active, password_hash FROM users")
        users = cursor.fetchall()

        print(f"📊 Found {len(users)} users in database:")
        for user in users:
            user_id, username, email, role, is_active, password_hash = user
            print(f"   👤 ID: {user_id}, Username: {username}, Role: {role}, Active: {bool(is_active)}")
            print(f"      📧 Email: {email}")
            print(f"      🔐 Password hash: {password_hash[:50]}...")

        conn.close()

    except Exception as e:
        print(f"❌ SQLite query error: {e}")

    # Method 2: SQLAlchemy query
    try:
        db = SessionLocal()
        users = db.query(User).all()

        print(f"\n📊 SQLAlchemy query found {len(users)} users:")
        for user in users:
            print(f"   👤 ID: {user.id}, Username: {user.username}, Role: {user.role.value}, Active: {user.is_active}")

        db.close()

    except Exception as e:
        print(f"❌ SQLAlchemy query error: {e}")

def test_password_verification():
    """Test password verification for teacher_zhang"""
    print("\n🔐 Testing Password Verification")
    print("=" * 40)

    try:
        db = SessionLocal()
        user = db.query(User).filter(User.username == "teacher_zhang").first()

        if user:
            print(f"✅ Found user: {user.username}")
            print(f"   📧 Email: {user.email}")
            print(f"   🏷️ Role: {user.role.value}")
            print(f"   ✅ Active: {user.is_active}")
            print(f"   🔐 Password hash: {user.password_hash[:50]}...")

            # Test password verification
            test_passwords = ["teacher123456", "password", "123456", "teacher"]

            for password in test_passwords:
                try:
                    is_valid = verify_password(password, user.password_hash)
                    print(f"   🔑 Password '{password}': {'✅ VALID' if is_valid else '❌ INVALID'}")
                except Exception as e:
                    print(f"   🔑 Password '{password}': ❌ ERROR - {e}")
        else:
            print("❌ User 'teacher_zhang' not found!")

        db.close()

    except Exception as e:
        print(f"❌ Password verification error: {e}")

def test_login_api():
    """Test login API directly"""
    print("\n🌐 Testing Login API")
    print("=" * 40)

    import requests

    test_credentials = [
        {"username": "teacher_zhang", "password": "teacher123456"},
        {"username": "testuser", "password": "testpass123"},
        {"username": "admin", "password": "admin123456"}
    ]

    for creds in test_credentials:
        try:
            response = requests.post("http://localhost:8000/api/v1/auth/token",
                                   json=creds, timeout=5)

            if response.status_code == 200:
                result = response.json()
                print(f"✅ {creds['username']}: Login successful")
                print(f"   🎫 Token: {result['access_token'][:50]}...")
            else:
                error = response.json()
                print(f"❌ {creds['username']}: Login failed - {error.get('detail', 'Unknown error')}")

        except Exception as e:
            print(f"❌ {creds['username']}: API error - {e}")

def recreate_teacher_user():
    """Recreate teacher user with correct password"""
    print("\n🔧 Recreating Teacher User")
    print("=" * 40)

    try:
        db = SessionLocal()

        # Delete existing teacher_zhang if exists
        existing_user = db.query(User).filter(User.username == "teacher_zhang").first()
        if existing_user:
            db.delete(existing_user)
            db.commit()
            print("🗑️ Deleted existing teacher_zhang user")

        # Create new teacher user
        from app.models import UserRole

        hashed_password = get_password_hash("teacher123456")

        new_teacher = User(
            username="teacher_zhang",
            email="<EMAIL>",
            password_hash=hashed_password,
            full_name="张老师",
            role=UserRole.teacher,
            is_active=True
        )

        db.add(new_teacher)
        db.commit()
        db.refresh(new_teacher)

        print(f"✅ Created new teacher user:")
        print(f"   👤 ID: {new_teacher.id}")
        print(f"   📧 Email: {new_teacher.email}")
        print(f"   🏷️ Role: {new_teacher.role.value}")
        print(f"   ✅ Active: {new_teacher.is_active}")

        # Test the new password
        is_valid = verify_password("teacher123456", new_teacher.password_hash)
        print(f"   🔑 Password verification: {'✅ VALID' if is_valid else '❌ INVALID'}")

        db.close()

    except Exception as e:
        print(f"❌ User creation error: {e}")

def main():
    print("🔍 User Authentication Diagnosis")
    print("=" * 50)

    # Step 1: Check existing users
    check_database_users()

    # Step 2: Test password verification
    test_password_verification()

    # Step 3: Test login API
    test_login_api()

    # Step 4: Recreate teacher user if needed
    print("\n" + "=" * 50)
    recreate = input("🤔 Do you want to recreate the teacher user? (y/n): ").lower().strip()
    if recreate == 'y':
        recreate_teacher_user()

        # Test again after recreation
        print("\n🔄 Testing after user recreation:")
        test_login_api()

    print("\n" + "=" * 50)
    print("🎯 Diagnosis Complete!")

if __name__ == "__main__":
    main()
