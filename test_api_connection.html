<!DOCTYPE html>
<html>
<head>
    <title>API Connection Test</title>
</head>
<body>
    <h1>API Connection Test</h1>
    <div id="results"></div>
    
    <script>
        async function testApiConnection() {
            const results = document.getElementById('results');
            
            try {
                // Test backend root endpoint
                const response = await fetch('http://localhost:8000/');
                const text = await response.text();
                results.innerHTML += '<p>✅ Backend root endpoint accessible</p>';
                
                // Test API endpoint (should return authentication error)
                const apiResponse = await fetch('http://localhost:8000/api/v1/protected-test');
                const apiData = await apiResponse.json();
                if (apiData.detail === "Not authenticated") {
                    results.innerHTML += '<p>✅ API endpoint working (authentication required)</p>';
                } else {
                    results.innerHTML += '<p>❌ Unexpected API response</p>';
                }
                
                // Test frontend
                const frontendResponse = await fetch('http://localhost:3000/');
                if (frontendResponse.ok) {
                    results.innerHTML += '<p>✅ Frontend accessible</p>';
                } else {
                    results.innerHTML += '<p>❌ Frontend not accessible</p>';
                }
                
                results.innerHTML += '<h2>✅ All basic connectivity tests passed!</h2>';
                
            } catch (error) {
                results.innerHTML += `<p>❌ Error: ${error.message}</p>`;
            }
        }
        
        testApiConnection();
    </script>
</body>
</html>
